#!/usr/bin/env python3

import os
import argparse
import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional

from crewai import Crew

from src.config import check_environment, OUTPUT_DIR
from src.agents import AgentsFactory
from src.tasks_and_tools import workflow_factory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def get_available_workflows() -> Dict[str, str]:
    """
    Get a dictionary of available workflows.
    
    Returns:
        Dict[str, str]: Dictionary mapping workflow names to their descriptions.
    """
    workflows = {
        "siebert": "Comprehensive content generation with research, planning, writing, editing, and quality review.",
        "basic": "Simple content generation with research, planning, writing, and editing."
    }
    
    # Look for additional workflows in the workflows directory
    workflows_dir = Path(__file__).parent / "workflows"
    for workflow_file in workflows_dir.glob("workflow_*.py"):
        workflow_name = workflow_file.stem.replace("workflow_", "")
        if workflow_name not in workflows and workflow_name != "__init__":
            workflows[workflow_name] = f"Custom workflow: {workflow_name}"
    
    return workflows


def run_workflow(topic: str, workflow_name: str, output_path: str, 
                config: Optional[Dict[str, Any]] = None) -> str:
    """
    Run a content generation workflow.
    
    Args:
        topic (str): Topic for content generation.
        workflow_name (str): Name of the workflow to run.
        output_path (str): Path to save the output.
        config (Optional[Dict[str, Any]]): Additional configuration for the workflow.
        
    Returns:
        str: Path to the generated content file.
    """
    config = config or {}
    
    # Create agents factory
    agents_factory = AgentsFactory(config)
    
    # Try to import the workflow module directly
    try:
        # First try to import the workflow module
        module_name = f"workflows.workflow_{workflow_name}"
        try:
            workflow_module = __import__(module_name, fromlist=["get_workflow", "create_workflow"])
            
            # Get the workflow configuration
            import inspect
            if hasattr(workflow_module, "get_workflow"):
                # Ispeziona la signature per capire cosa accetta get_workflow
                get_wf = workflow_module.get_workflow
                sig = inspect.signature(get_wf)
                params = list(sig.parameters.keys())
                # Prepara agent/tools/tasks se richiesti
                from src.tools import get_available_tools
from src.tasks import get_available_tasks
                client_name = config.get("client_name", "")
                tools = get_available_tools(client_name)
                agents = agents_factory.create_agents(tools)
                tasks = get_available_tasks(agents)
                # Chiama con i parametri giusti
                if len(params) >= 5:
                    workflow_config = get_wf(topic, agents, tasks, tools, config)
                elif len(params) == 4:
                    workflow_config = get_wf(topic, agents_factory, config, agents=agents)
                else:
                    workflow_config = get_wf(topic, agents_factory, config)
                # Se c'è create_workflow, usalo
                if hasattr(workflow_module, "create_workflow"):
                    workflow_result = workflow_module.create_workflow(topic, agents, tasks, tools, config)
                else:
                    workflow_result = workflow_factory(workflow_name, topic, agents_factory, config)
            else:
                workflow_result = workflow_factory(workflow_name, topic, agents_factory, config)

        except ImportError:
            # If import fails, fall back to workflow_factory
            logger.warning(f"Could not import workflow module {module_name}, falling back to workflow_factory")
            workflow_result = workflow_factory(workflow_name, topic, agents_factory, config)
            
    except ValueError as e:
        logger.error(f"Error creating workflow: {str(e)}")
        return ""
    
    # --- INTEGRA ORCHESTRATORE GENERICO ROBUSTO ---
    from src.orchestrator import run_workflow as orchestrator_run_workflow
    logger.info(f"Starting {workflow_name} workflow for topic: {topic}")
    tasks = workflow_result.get("tasks", [])
    if tasks and isinstance(tasks[0], dict) and "task" in tasks[0]:
        # Struttura dichiarativa: usare orchestratore
        outputs = orchestrator_run_workflow(workflow_result, topic=topic, verbose=True)
        final_task_id = tasks[-1]["id"]
        result = outputs[final_task_id]
    elif tasks and (hasattr(tasks[0], "description") or hasattr(tasks[0], "expected_output")):
        # Vecchia struttura: passare direttamente a CrewAI
        crew = Crew(
            agents=list(workflow_result["agents"].values()),
            tasks=tasks,
            verbose=True
        )
        result = crew.kickoff()
    else:
        logger.error("Formato tasks non riconosciuto! Assicurati che il workflow sia dichiarativo o classico.")
        return ""
    logger.info("Workflow completed successfully")
    # Save output
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(result)
    logger.info(f"Output saved to: {output_path}")
    return output_path


def main():
    """
    Main entry point for the content generation system.
    """
    # Get available workflows
    available_workflows = get_available_workflows()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Modular Content Generation System")
    parser.add_argument("--topic", required=True, help="Topic for content generation")
    parser.add_argument(
        "--workflow", 
        default="siebert", 
        choices=list(available_workflows.keys()),
        help="Workflow to use for content generation"
    )
    parser.add_argument(
        "--output", 
        default=os.path.join(OUTPUT_DIR, f"content_{int(time.time())}.md"),
        help="Path to save the generated content"
    )
    parser.add_argument(
        "--client", 
        default="",
        help="Client name for RAG integration"
    )
    parser.add_argument(
        "--audience", 
        default="General audience",
        help="Target audience for the content"
    )
    parser.add_argument(
        "--temperature", 
        type=float,
        default=0.7,
        help="Temperature for the LLM"
    )
    parser.add_argument(
        "--provider", 
        default="openai",
        choices=["openai", "anthropic", "deepseek"],
        help="LLM provider to use"
    )
    parser.add_argument(
        "--model", 
        default="",
        help="Specific model to use (provider-dependent)"
    )
    
    args = parser.parse_args()
    
    # Check environment
    try:
        check_environment()
    except EnvironmentError as e:
        logger.error(str(e))
        return
    
    # Prepare configuration
    config = {
        "provider": args.provider,
        "temperature": args.temperature,
        "client_name": args.client,
        "target_audience": args.audience
    }
    
    if args.model:
        config["model_name"] = args.model
    
    # Run workflow
    output_path = run_workflow(args.topic, args.workflow, args.output, config)
    
    if output_path:
        print(f"\nContent generation completed!")
        print(f"Output saved to: {output_path}")
    else:
        print("\nContent generation failed. Check the logs for details.")


if __name__ == "__main__":
    main()
