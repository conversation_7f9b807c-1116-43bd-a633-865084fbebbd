aiohappyeyeballs==2.4.6
aiohttp==3.11.12
aiosignal==1.3.2
airtable-python-wrapper==0.15.3
alembic==1.14.1
altair==5.5.0
annotated-types==0.7.0
anthropic==0.46.0
anyio==4.8.0
appdirs==1.4.4
asgiref==3.8.1
asttokens==3.0.0
async-timeout==4.0.3
attrs==25.1.0
auth0-python==4.8.0
backoff==2.2.1
bcrypt==4.2.1
beautifulsoup4==4.13.3
bidict==0.23.1
blinker==1.9.0
bs4==0.0.2
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
chroma-hnswlib==0.7.3
chromadb==0.4.24
clarifai==10.11.1
clarifai-grpc==11.2.5
clarifai-protocol==0.0.14
click==8.1.7
cohere==5.14.0
coloredlogs==15.0.1
contextlib2==21.6.0
crewai==0.30.11
crewai-tools==0.4.6
cryptography==44.0.1
dataclasses-json==0.6.7
decorator==5.2.0
deepseek==1.0.0
defusedxml==0.7.1
Deprecated==1.2.18
deprecation==2.1.0
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
docstring-parser==0.15
docx2txt==0.8
durationpy==0.9
embedchain==0.1.113
et_xmlfile==2.0.0
eventlet==0.39.1
exceptiongroup==1.2.2
executing==2.2.0
faiss-cpu==1.10.0
fastapi==0.115.12
fastavro==1.10.0
filelock==3.17.0
Flask==3.1.0
flask-cors==5.0.1
Flask-SocketIO==5.3.6
flatbuffers==25.2.10
frozenlist==1.5.0
fsspec==2024.6.1
funcy==2.0
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.15
google-api-core==2.24.1
google-api-python-client==2.169.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.81.0
google-cloud-bigquery==3.29.0
google-cloud-core==2.4.2
google-cloud-resource-manager==1.14.1
google-cloud-storage==2.19.0
google-crc32c==1.6.0
google-generativeai==0.8.5
google-resumable-media==2.7.2
googleapis-common-protos==1.68.0
gptcache==0.1.44
greenlet==3.1.1
grpc-google-iam-v1==0.14.0
grpcio==1.71.0
grpcio-status==1.63.0rc1
grpcio-tools==1.71.0
h11==0.14.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.4
httpx==0.27.2
httpx-sse==0.4.0
huggingface-hub==0.29.1
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.4.0
importlib_resources==6.5.2
iniconfig==2.0.0
inquirerpy==0.3.4
instructor==0.5.2
ipython==8.32.0
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.5
jiter==0.8.2
json5==0.10.0
json_repair==0.39.0
jsonpatch==1.33
jsonpickle==4.0.2
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kubernetes==32.0.1
lancedb==0.5.7
langchain==0.1.20
langchain-community==0.0.38
langchain-anthropic==0.1.13
langchain-cohere==0.1.5
langchain-openai==0.1.7
langchain-text-splitters==0.0.2
langsmith==0.1.147
litellm==1.60.2
Mako==1.3.9
markdown-it-py==3.0.0
markdownify==1.1.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.67
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
multidict==6.1.0
mypy-extensions==1.0.0
narwhals==1.34.0
networkx==3.4.2
nodeenv==1.9.1
numpy==1.26.4
oauthlib==3.2.2
onnxruntime==1.20.1
openai==1.63.2
openpyxl==3.1.5
opentelemetry-api==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-exporter-otlp-proto-http==1.27.0
opentelemetry-instrumentation==0.48b0
opentelemetry-instrumentation-asgi==0.48b0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.48b0
orjson==3.10.15
outcome==1.3.0.post0
overrides==7.7.0
packaging==23.2
pandas==2.2.3
parso==0.8.4
pdfminer.six==20231228
pdfplumber==0.11.5
pexpect==4.9.0
pfzy==0.3.4
pillow==10.4.0
pluggy==1.5.0
portalocker==2.10.1
posthog==3.15.0
prompt_toolkit==3.0.50
propcache==0.3.0
proto-plus==1.26.0
protobuf==5.26.1
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pulsar-client==3.6.1
pure_eval==0.2.3
py==1.11.0
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.8.0
pydantic_core==2.27.2
pydeck==0.9.1
Pygments==2.19.1
PyJWT==2.10.1
pylance==0.9.18
pyparsing==3.2.3
pypdf==4.3.1
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyright==1.1.396
pysbd==0.3.4
PySocks==1.7.1
pytest==8.3.4
pytest-asyncio==0.25.3
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
python-engineio==4.11.2
python-json-logger==2.0.7
python-rapidjson==1.20
python-socketio==5.10.0
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
PyYAML==6.0.1
qdrant-client==1.13.3
ratelimiter==1.2.0.post0
referencing==0.36.2
regex==2023.12.25
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
retry==0.9.2
rich==13.9.4
rpds-py==0.23.1
rsa==4.9
schema==0.7.5
selenium==4.29.0
semver==3.0.4
shapely==2.0.7
shellingham==1.5.4
simple-websocket==1.1.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.6
SQLAlchemy==2.0.38
stack-data==0.6.3
starlette==0.45.3
streamlit==1.32.2
sympy==1.13.3
tabulate==0.9.0
tavily-python==0.5.1
tenacity==8.5.0
tiktoken==0.7.0
tokenizers==0.20.3
toml==0.10.2
tomli==2.2.1
tomli_w==1.2.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
trio==0.29.0
trio-websocket==0.12.2
tritonclient==2.55.0
typer==0.9.4
types-requests==2.32.0.20250306
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
uritemplate==4.1.1
urllib3==2.3.0
uv==0.6.2
uvicorn==0.34.2
watchfiles==1.0.4
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0
Werkzeug==3.1.3
wrapt==1.17.2
wsproto==1.2.0
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0
