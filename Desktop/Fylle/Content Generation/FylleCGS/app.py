import os
import time
import streamlit as st
import yaml
from pathlib import Path
from typing import Dict, List, Any

# IMPORTANTE: Importa il patch PRIMA di CrewAI per evitare errori onnxruntime/tokenizers
try:
    from src.memory_patch import patch_all_chromadb_dependencies
    # Il patch è già stato applicato automaticamente durante l'import
except ImportError:
    pass  # Patch non disponibile

try:
    from crewai import Crew
except ImportError as e:
    st.error(f"Missing dependency: {e}")
    st.info("Please make sure all required packages are installed.")
    st.stop()
from src.profiles import init_profiles, PROFILES_DIR
from src.agent_meta import (
    list_profiles,
    load_profile_meta,
    save_profile_meta,
    generate_profile_stubs,
)
from src.config import check_environment, OUTPUT_DIR
from src.agents import AgentsFactory
from src.workflow_factory import workflow_factory
from src.tools import get_available_tools
from src.tasks import get_available_tasks
from src.workflow_builder import (
    get_predefined_tasks, load_custom_tasks,
    generate_workflow_code, save_workflow_file
)

from src.config import (
    LLM_MODELS, validate_api_keys, get_available_providers,
    get_model_parameters
)


def get_available_workflows() -> Dict[str, str]:
    workflows = {}
    workflows_dir = Path(__file__).parent / "workflows"
    for wf_file in workflows_dir.glob("workflow_*.py"):
        name = wf_file.stem.replace("workflow_", "")
        display_name = name.replace("_", " ").title()
        if name != "__init__":
            workflows[display_name] = name
    return workflows


def get_available_clients() -> List[str]:
    clients = ["None"]
    rag_dir = Path(__file__).parent / "rag"
    if rag_dir.exists():
        for d in rag_dir.iterdir():
            if d.is_dir() and not d.name.startswith("."):
                clients.append(d.name)
    return clients


def main():
    init_profiles()
    st.set_page_config(
        page_title="Content Generation System",
        page_icon="📝",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "Generazione",
        "Sistema",
        "Diagnostica",
        "Building",
        "Workflow",
        "Workflow Builder"
    ])

    # === Tab 1: Generazione ===
    with tab1:
        st.title("📝 Content Generation System")
        profiles = list_profiles()
        selected_profile = st.selectbox(
            "Select Client Profile",
            profiles,
            index=profiles.index("default") if "default" in profiles else 0
        )
        st.markdown("---")

        with st.sidebar:
            st.header("Configuration")
            # Get available providers and their models
            available_providers = get_available_providers()
            provider_options = [p for p in available_providers if p in ["openai", "anthropic", "deepseek"]]
            provider = st.selectbox(
                "LLM Provider",
                provider_options if provider_options else ["openai", "anthropic", "deepseek"]
            )
            # Model selection
            available_models = LLM_MODELS[provider]["options"]
            model = st.selectbox(
                "Model",
                available_models,
                index=available_models.index(LLM_MODELS[provider]["default"]) if LLM_MODELS[provider]["default"] in available_models else 0
            )
            temperature = st.slider(
                "Temperature", min_value=0.0, max_value=1.0,
                value=0.7, step=0.1
            )
            enable_logging = st.checkbox("Enable logging", value=False)
            st.session_state["enable_logging"] = enable_logging
            # Get dynamic max tokens based on selected model
            from src.config import MODEL_TOKEN_LIMITS
            model_limits = MODEL_TOKEN_LIMITS.get(model, {"max_output": 4000})
            max_output_limit = model_limits["max_output"]
            
            # Set default value based on model's capability, but cap at reasonable defaults
            default_tokens = min(1000, max_output_limit)
            
            max_tokens = st.slider(
                "Max Tokens", 
                min_value=100, 
                max_value=max_output_limit,
                value=default_tokens, 
                step=100,
                help=f"Max output tokens for {model}: {max_output_limit}"
            )

        col1, col2 = st.columns(2)
        with col1:
            # Workflow selection with info
            workflow_options = get_available_workflows()
            wf_display = st.selectbox(
                "Select Workflow",
                list(workflow_options.keys())
            )
            wf_id = workflow_options.get(wf_display, "")
            

            topic = st.text_area(
                "Enter Topic",
                height=100,
                placeholder="Describe the topic..."
            )
            st.subheader("Additional Context")
            additional_context = st.text_area(
                "Context",
                height=150,
                placeholder="Add any additional context..."
            )
        with col2:
            target_audience = st.text_input(
                "Target Audience", value="General audience"
            )
            clients = get_available_clients()
            client = st.selectbox(
                "Select Client for RAG Integration", clients
            )
            client_name = None if client == "None" else client

            st.subheader("RAG Context Files")
            selected_files = []
            file_contents = {}
            if client_name:
                rag_dir = Path(__file__).parent / "rag" / client_name
                if rag_dir.exists():
                    # Collect all available files with full paths
                    available_files = []
                    for root, dirs, files in os.walk(rag_dir):
                        for file in files:
                            if file.endswith('.md'):
                                full_path = Path(root) / file
                                relative_path = full_path.relative_to(rag_dir)
                                available_files.append({
                                    'display_name': str(relative_path),
                                    'full_path': full_path,
                                    'relative_path': relative_path
                                })

                    if available_files:
                        st.write(f"**Available files for {client_name}:**")

                        # Multi-selection with checkboxes
                        for file_info in available_files:
                            file_key = f"rag_file_{client_name}_{file_info['display_name']}"
                            if st.checkbox(file_info['display_name'], key=file_key):
                                selected_files.append(file_info['display_name'])
                                try:
                                    content = file_info['full_path'].read_text(encoding="utf-8")
                                    file_contents[file_info['display_name']] = content
                                except Exception as e:
                                    st.error(f"Error reading {file_info['display_name']}: {e}")

                        # Show preview of selected files
                        if selected_files:
                            st.write(f"**Selected files ({len(selected_files)}):**")
                            for file_name in selected_files:
                                with st.expander(f"Preview: {file_name}"):
                                    content = file_contents.get(file_name, "")
                                    preview = content[:500] + "..." if len(content) > 500 else content
                                    st.text_area(f"Content preview", preview, height=120, key=f"preview_{file_name}")
                    else:
                        st.info(f"No .md files found in {client_name} RAG directory")

        # Debug Configuration Display
        st.markdown("---")
        st.subheader("🔧 Debug Config")
        
        # Get filtered parameters for the selected provider
        from src.config import get_filtered_parameters, estimate_cost
        
        # Create base parameters dict
        base_params = {
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0
        }
        
        # Filter parameters for the selected provider
        filtered_params = get_filtered_parameters(provider, base_params)
        
        # Estimate cost
        estimated_cost = estimate_cost(provider, model, max_tokens)
        
        # Display debug info in a nice format
        debug_config = {
            "provider": provider,
            "model": model,
            "filtered_params": filtered_params,
            "estimated_cost": estimated_cost
        }
        
        # Use columns for better layout
        debug_col1, debug_col2 = st.columns(2)
        
        with debug_col1:
            st.json({
                "provider": debug_config["provider"],
                "model": debug_config["model"]
            })
        
        with debug_col2:
            st.json({
                "filtered_params": debug_config["filtered_params"],
                "estimated_cost": f"${debug_config['estimated_cost']:.4f}"
            })

        if st.button("Generate Content", disabled=(not topic or not selected_profile)):
            config = {
                "provider": provider,
                "model": model,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "target_audience": target_audience,
                "client_name": client_name,
                "enable_logging": st.session_state.get("enable_logging", False),
                "context": additional_context,
                "rag_selected_files": selected_files,
                "rag_file_contents": file_contents
            }

            run_workflow(topic, wf_id, config)

    # === Tab 2: Sistema ===
    with tab2:
        from src.api_health import get_all_api_status
        from src.reporting import reporting_tab
        from src.config import OPENAI_API_KEY, ANTHROPIC_API_KEY, DEEPSEEK_API_KEY
        st.header("Sistema: Health Check & Tool Access")
        api_status = st.session_state.get("api_status") or get_all_api_status()
        st.session_state["api_status"] = api_status
        if st.button("Esegui Health Check API", key="health_check_btn2"):
            api_status = get_all_api_status()
            st.session_state["api_status"] = api_status

        col1, col2 = st.columns(2)
        with col1:
            st.subheader("API Health Check")
            for prov, stat in api_status.items():
                color = "green" if stat == "OK" else "red"
                st.markdown(f"**{prov}**: <span style='color:{color}'>{stat}</span>",
                            unsafe_allow_html=True)
        with col2:
            st.subheader("Tool disponibili per gli LLM")
            tools = get_available_tools()
            for tool in (tools.values() if isinstance(tools, dict) else []):
                st.markdown(f"**{tool.name}**: {tool.description}")

        # Add model status reporting
        st.markdown("---")
        st.subheader("Stato Modelli per Provider")
        reporting_tab(
            model_options=LLM_MODELS,
            openai_key=OPENAI_API_KEY,
            anthropic_key=ANTHROPIC_API_KEY,
            deepseek_key=DEEPSEEK_API_KEY
        )

    # === Tab 3: Diagnostica ===
    with tab3:
        import pandas as pd
        from src.diagnostics import run_agent_tool_tests
        st.header("Diagnostica: Test Integrazione Agent/Tool/Task")
        if st.button("Esegui Test Integrazione", key="run_agent_tool_tests_btn"):
            st.session_state["diagnostic_report"] = run_agent_tool_tests()
        report = st.session_state.get("diagnostic_report", [])
        if report:
            df = pd.DataFrame(report)
            only_fail = st.checkbox("Mostra solo i FAIL", value=False)
            if only_fail:
                df = df[df["result"] != "SUCCESS"]
            st.dataframe(df)

    # === Tab 4: Building / Profile Manager ===
    with tab4:
        st.header("Building Agents / Profile Manager")

        # 0) Create New Profile
        new_profile = st.text_input("New Profile Name", key="new_profile_name")
        if st.button("➕ Create Profile"):
            if not new_profile.strip():
                st.error("Inserisci un nome profilo valido.")
            else:
                generate_profile_stubs(new_profile)
                st.success(f"Profilo '{new_profile}' creato.")
                st.experimental_rerun()

        st.markdown("---")

        # 1) Select Existing Profile
        profiles = list_profiles()
        selected_profile = st.selectbox(
            "Select Client Profile",
            profiles,
            index=profiles.index("default") if "default" in profiles else 0,
            key="building_profile_select"
        )

        # Load in-memory metadata
        agents_meta = load_profile_meta(selected_profile)

        # 2) Select Agent and edit
        agent_ids = list(agents_meta.keys())
        selected_agent = st.selectbox(
            "Select Agent",
            agent_ids,
            key=f"building_agent_select_{selected_profile}"
        )
        if selected_agent:
            meta = agents_meta[selected_agent]
            with st.form("building_form"):
                system_message = st.text_area(
                    "System Message",
                    value=meta.get("system_message", ""),
                    max_chars=2000
                )
                backstory = st.text_area(
                    "Backstory",
                    value=meta.get("backstory", ""),
                    max_chars=2000
                )
                goal = st.text_input(
                    "Goal",
                    value=meta.get("goal", ""),
                    max_chars=2000
                )
                examples = st.text_area(
                    "Examples (one per line)",
                    value="\n".join(meta.get("examples", [])),
                    height=150
                )
                tools_dict = get_available_tools()
                available_tools = (
                    [t.name for t in tools_dict.values()]
                    if isinstance(tools_dict, dict)
                    else list(tools_dict)
                )
                # Filter default tools to only include those that exist in available_tools
                current_tools = meta.get("tools", [])
                valid_default_tools = [tool for tool in current_tools if tool in available_tools]
                selected_tools = st.multiselect(
                    "Tools abilitati per questo Agent",
                    options=available_tools,
                    default=valid_default_tools
                )
                submitted = st.form_submit_button("Save Agent")
                if submitted:
                    if not (system_message.strip() and backstory.strip() and goal.strip()):
                        st.error("System Message, Backstory e Goal non possono essere vuoti.")
                    else:
                        meta.update({
                            "system_message": system_message,
                            "backstory": backstory,
                            "goal": goal,
                            "examples": [e for e in examples.split("\n") if e.strip()],
                            "tools": selected_tools,
                        })
                        save_profile_meta(selected_profile, selected_agent, meta)
                        st.success(f"Agent `{selected_agent}` salvato.")
                        st.experimental_rerun()

        st.markdown("---")

        # 3) List agents actually configured (ignore empty stubs)
        agents_dir = PROFILES_DIR / selected_profile / "agents"
        st.markdown("**Agent già salvati**")
        configured = []
        if agents_dir.exists():
            for f in agents_dir.glob("*.yaml"):
                content = {}
                try:
                    content = yaml.safe_load(f.read_text(encoding="utf-8")) or {}
                except Exception:
                    pass
                if any(content.get(field) for field in ("system_message", "backstory", "goal")):
                    configured.append(f.stem)
            if configured:
                for aid in configured:
                    st.write(f"✅ {aid}")
            else:
                st.info("Nessun agent ancora configurato per questo profilo.")
        else:
            st.info("Nessun agent ancora configurato per questo profilo.")

        # 3.b) Show details of configured agents
        if configured:
            st.markdown("---")
            st.markdown("**Dettagli agent configurati**")
            for aid in configured:
                data = agents_meta.get(aid, {})
                with st.expander(f"{aid} (dettagli)"):
                    st.markdown(f"**System Message:**  \n{data.get('system_message','_vuoto_')}")
                    st.markdown(f"**Backstory:**  \n{data.get('backstory','_vuoto_')}")
                    st.markdown(f"**Goal:**  \n{data.get('goal','_vuoto_')}")
                    exs = data.get('examples', [])
                    if exs:
                        st.markdown("**Examples:**")
                        for ex in exs:
                            st.markdown(f"- {ex}")
                    tools = data.get('tools', [])
                    st.markdown(f"**Tools:** {', '.join(tools) if tools else '_nessuno_'}")

        # 4) Finalize Profile
        if st.button("💾 Finalize Profile"):
            st.success(f"Profilo `{selected_profile}` pronto — selezionalo ora in Generazione.")
            st.experimental_rerun()

    # === Tab 5: Workflow Overview ===
    with tab5:
        st.header("Workflow Overview")
        from src.workflow_introspection import extract_workflow_info
        workflows_dir = Path(__file__).parent / "workflows"
        workflow_infos = extract_workflow_info(workflows_dir)
        if not workflow_infos:
            st.info("Nessun workflow trovato.")
        else:
            # Dropdown selezione workflow
            wf_names = list(workflow_infos.keys())
            selected_wf = st.selectbox("Seleziona workflow", wf_names)
            wf = workflow_infos[selected_wf]
            # Description
            desc = wf.get("description", "")
            if desc:
                st.markdown(f"**Descrizione:** {desc}")
            # Agents
            agents = wf.get("agents", {})
            tasks = wf.get("tasks", [])
            # Estrai solo agenti effettivamente coinvolti nei task
            agent_keys_in_tasks = set()
            for t in tasks:
                agent_key = t.get("agent", None)
                if isinstance(agent_key, str):
                    agent_keys_in_tasks.add(agent_key)
            if agents and agent_keys_in_tasks:
                st.markdown("**Agenti coinvolti:**")
                for k, a in agents.items():
                    if k in agent_keys_in_tasks:
                        role = a.get("role", "")
                        st.write(f"- {role}")
            # Tasks
            tasks = wf.get("tasks", [])
            if tasks:
                st.markdown("**Sequenza di task:**")
                for i, t in enumerate(tasks, 1):
                    t_desc = t.get("description", "")
                    agent_role = getattr(t.get("agent"), "role", "")
                    st.write(f"{i}. {t_desc}  _(Agente: {agent_role})_")

    # === Tab 6: Workflow Builder ===
    with tab6:
        st.header("🔧 Workflow Builder")
        # --- Selezione profilo ---
        profiles = list_profiles()
        selected_profile = st.selectbox("Profilo", profiles, key="wb_profile_select")
        if st.session_state.get("wb_profile_last") != selected_profile:
            st.info("Cambia profilo per resettare la lista dei task")
        # --- Caricamento agenti configurati ---
        agents_meta = load_profile_meta(selected_profile)
        agent_ids = list(agents_meta.keys())
        # --- Selezione agenti ---
        selected_agents = st.multiselect(
            "Agenti disponibili",
            agent_ids,
            default=agent_ids,
            key="wb_agents_multiselect"
        )
        # --- Stato task ---
        if "wb_tasks" not in st.session_state or st.session_state.get("wb_profile_last") != selected_profile:
            st.session_state.wb_tasks = []
            st.session_state.wb_profile_last = selected_profile
        # --- Task predefiniti ---
        predefined_tasks = get_predefined_tasks()
        selected_pre_task = st.selectbox("Aggiungi task predefinito", ["-"] + list(predefined_tasks.keys()), key="wb_pre_task_select")
        selected_pre_agent = st.selectbox("Assegna agente", selected_agents, key="wb_pre_task_agent") if selected_agents else None
        if selected_pre_task != "-" and selected_pre_agent and st.button("Aggiungi Task Predefinito", key="wb_add_pre_btn"):
            st.session_state.wb_tasks.append({
                "type": "pre",
                "name": selected_pre_task,
                "agent": selected_pre_agent
            })
        # --- Task custom ---
        st.markdown("---")
        st.subheader("Aggiungi Task Custom")
        # Determina l'id/nome del task precedente
        if len(st.session_state.wb_tasks) > 0:
            prev_task = st.session_state.wb_tasks[-1]
            prev_id = prev_task.get("name") or f"task{len(st.session_state.wb_tasks)}"
        else:
            prev_id = None

        custom_name = st.text_input("Nome Task Custom", key="wb_custom_name")
        use_prev_output = False
        default_desc = ""
        if prev_id:
            use_prev_output = st.checkbox(
                f"Usa come input l’output del task precedente ('{{{{{prev_id}}}}}')", 
                key="wb_use_prev_output"
            )
            if use_prev_output:
                default_desc = f"{{{{{prev_id}}}}}"
        # Usa il valore di default_desc solo se la checkbox è attiva
        custom_desc = st.text_area(
            "Prompt/Descrizione dettagliata", 
            value=default_desc if use_prev_output else "", 
            key="wb_custom_desc"
        )
        custom_agent = st.selectbox("Assegna agente", selected_agents, key="wb_custom_agent") if selected_agents else None
        custom_output = st.text_area("Output atteso", key="wb_custom_output")
        if st.button("Aggiungi Task Custom", key="wb_add_custom_btn"):
            # Assicura che ogni task abbia un name unico (se vuoto, genera uno)
            task_name = custom_name.strip() or f"task{len(st.session_state.wb_tasks)+1}"
            st.session_state.wb_tasks.append({
                "type": "custom",
                "name": task_name,
                "description": custom_desc,
                "agent": custom_agent,
                "expected_output": custom_output
            })
        # --- Lista task aggiunti ---
        st.markdown("---")
        st.subheader("Task aggiunti al workflow")
        to_remove = []
        for i, t in enumerate(st.session_state.wb_tasks):
            if t["type"] == "pre":
                st.write(f"{i+1}. [Predefinito] {t['name']} — agente: {t['agent']}")
            else:
                st.write(f"{i+1}. [Custom] {t['name']} — agente: {t['agent']}")
                st.caption(f"Descrizione: {t.get('description', '')}")
                st.caption(f"Output atteso: {t.get('expected_output', '')}")
            if st.button(f"Rimuovi", key=f"wb_remove_task_{i}"):
                to_remove.append(i)
        for idx in sorted(to_remove, reverse=True):
            st.session_state.wb_tasks.pop(idx)
        # --- Salvataggio workflow ---
        st.markdown("---")
        st.subheader("Salva Workflow")
        workflow_name = st.text_input("Nome workflow Python (senza .py)", key="wb_save_name")
        workflow_desc = st.text_area("Descrizione workflow", key="wb_save_desc")
        tools = get_available_tools(selected_profile)
        if st.button("Genera e salva workflow Python", key="wb_save_btn"):
            if not workflow_name.strip():
                st.error("Devi dare un nome al workflow")
            elif not st.session_state.wb_tasks:
                st.error("Aggiungi almeno un task")
            else:
                agents_dict = {k: agents_meta[k] for k in selected_agents}
                import ast
                code = generate_workflow_code(
                    workflow_name.strip(),
                    workflow_desc,
                    agents_dict,
                    st.session_state.wb_tasks,
                    list(tools.keys()) if isinstance(tools, dict) else tools
                )
                # Validazione sintassi prima del salvataggio
                try:
                    ast.parse(code)
                    save_workflow_file(selected_profile, workflow_name.strip(), code)
                    st.success(f"Workflow salvato come workflow_{workflow_name.strip().lower().replace(' ', '_')}.py!")
                except SyntaxError as e:
                    st.error(f"Errore di sintassi nel workflow generato: {e}")
                st.code(code, language="python")
                st.session_state.wb_tasks = []

def run_workflow(topic: str, workflow_id: str, config: Dict[str, Any]) -> None:
    try:
        check_environment()
        agents_factory = AgentsFactory(config)
        try:
            module = __import__(f"workflows.workflow_{workflow_id}",
                                fromlist=["get_workflow", "create_workflow"])
            if hasattr(module, "get_workflow"):
                tools = get_available_tools(config.get("client_name", ""), config)
                agents = agents_factory.create_agents(tools)
                if hasattr(module, "create_workflow"):
                    workflow_result = module.create_workflow(topic, agents, tools, config)
                else:
                    workflow_result = workflow_factory(workflow_id, topic, agents_factory, config)
            else:
                workflow_result = workflow_factory(workflow_id, topic, agents_factory, config)

        except ImportError:
            st.warning("Could not import workflow module, falling back to factory")
            workflow_result = workflow_factory(workflow_id, topic, agents_factory, config)

        from src.orchestrator import run_workflow as orchestrator_run_workflow
        tasks = workflow_result.get("tasks", [])
        result = None
        if tasks and isinstance(tasks[0], dict) and "task" in tasks[0]:
            # Struttura dichiarativa: usare orchestratore
            outputs = orchestrator_run_workflow(workflow_result, topic=topic, verbose=True)
            final_task_id = tasks[-1]["id"]
            result = outputs[final_task_id]
        elif tasks and (hasattr(tasks[0], "description") or hasattr(tasks[0], "expected_output")):
            # Vecchia struttura: passare direttamente a CrewAI
            agents_list = workflow_result["agents"]
            if isinstance(agents_list, dict):
                agents_list = list(agents_list.values())
            elif not isinstance(agents_list, list):
                agents_list = [agents_list]
            
            crew = Crew(
                agents=agents_list,
                tasks=tasks,
                verbose=True
            )
            with st.status(f"Running {workflow_id} on topic: {topic}", expanded=True) as status:
                try:
                    result = crew.kickoff()
                    status.update(label="Workflow completed!", state="complete")
                except Exception as e:
                    status.update(label=f"Error: {e}", state="error")
                    st.error(str(e))
                    return
        else:
            st.error("Formato tasks non riconosciuto! Assicurati che il workflow sia dichiarativo o classico.")
            return

        output_filename = f"content_{int(time.time())}.md"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(result)

        st.success(f"Generated and saved to: {output_path}")
        with st.expander("Generated Content", expanded=True):
            st.markdown(result)
        st.download_button(
            "Download Markdown",
            data=result,
            file_name=output_filename,
            mime="text/markdown"
        )

    except Exception as e:
        st.error(f"Unhandled error: {e}")
        st.exception(e)


if __name__ == "__main__":
    main()