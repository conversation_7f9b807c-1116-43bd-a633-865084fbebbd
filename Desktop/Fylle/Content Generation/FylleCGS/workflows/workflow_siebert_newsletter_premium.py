
# workflow_siebert_newsletter_premium.py
# Workflow Siebert Newsletter OTTIMIZZATO con Knowledge Base Completa

from crewai import Task

def create_workflow(topic, agents, tasks, tools, config=None):
    """
    Workflow ottimizzato per Siebert Newsletter con knowledge base completa e task specializzati.
    
    Ottimizzazioni principali:
    1. Context Setting più specifico con template esatto dalla KB
    2. Premium Sources Analysis focalizzato su contenuti Gen Z-friendly
    3. Newsletter Creation con sezioni precise e word count accurati
    """
    
    # Task 1: Enhanced Context Setting con Knowledge Base Completa
    task1_enhanced_context = Task(
        description=f"""
        OBIETTIVO: Creare briefing completo utilizzando la knowledge base Siebert NL 3 per guidare la produzione newsletter.

        TOPIC FOCUS: {topic}

        RECUPERO KNOWLEDGE BASE:
        1. Utilizza RAG Content Retriever per estrarre TUTTO il contenuto da "KnoweldgeBaseSiebert NL 3.md"
        2. Focus specifico su queste sezioni critiche:
           - NEWSLETTER TEMPLATE STRUCTURE (8-Section Template dettagliato)
           - INDEPENDENT BRAND POSITIONING & TONE 
           - CONTENT CREATION GUIDELINES (Cultural Integration Rules)
           - PRODUCTION CHECKLIST FOR AGENTS
           - KEY MESSAGING THEMES & Editorial Perspective Requirements

        ANALISI TOPIC ATTRAVERSO LENTE GEN Z:
        - Connessioni culturali: Netflix, TikTok, gaming, streaming analogies
        - Pain points Gen Z: Financial shame, information overload, unstable income
        - Messaging themes: Emergency funds = "extra lives", investments = "personal brand portfolio"
        - Community voice: "Future Millionaires Community" perspective

        TEMPLATE STRUCTURE MAPPING (dal KB):
        1. Community Greeting (50-60 words): "Hey Future Wealth Builders, 👋"
        2. Feature Story (280-320 words): Main story + "Our Take" max 30 words
        3. Market Reality Check (200-250 words): 3-4 bullet trends
        4. By The Numbers (120-150 words): 3-5 statistics con emoji
        5. Your Move This Week (150-180 words): 3 action items ✅
        6. Community Corner (60-80 words): Reader spotlight + engagement
        7. Sponsor Section (80-100 words): Solo Siebert riferimenti

        CRITICAL GUIDELINES EXTRACTION:
        - Editorial Independence: NO promozione Siebert nelle sezioni 1-6
        - Community Voice: "we," "our generation," community perspective
        - Cultural Bridges: References che servono understanding, non solo engagement
        - Mobile-First: Short paragraphs, visual breaks, emoji strategici
        - Compliance: Educational focus, legal-safe language
        
        FORBIDDEN ELEMENTS (dalla KB):
        - NO "Hey Future Millionaires" (troppo assumptivo)
        - NO corporate messaging nelle sezioni editoriali
        - NO consultation-only CTAs nel sponsor
        - NO jargon tecnico o tone condescending
        - NO competitor links

        DELIVERABLE: Brief operativo completo con:
        - Template structure esatto (word count per sezione)
        - Brand voice guidelines specifiche Gen Z
        - Cultural integration rules per {topic}
        - Editorial independence framework
        - Action-oriented instructions per premium analysis
        """,
        expected_output="Brief operativo dettagliato con template newsletter esatto dalla KB, brand guidelines Siebert Gen Z, analisi topic-specific, cultural integration rules, e specifiche precise per ogni sezione (word count, tone, elementi richiesti/vietati)",
        agent=agents["rag_specialist"]
    )
    
    # Task 2: Targeted Premium Sources Analysis
    task2_targeted_analysis = Task(
        description=f"""
        OBIETTIVO: Analisi fonti premium ottimizzata per template newsletter e target Gen Z.
        
        CONTEXT BRIEF: Utilizza il briefing completo dal Task 1 per guidare l'analisi.
        TOPIC LENS: "{topic} for Gen Z financial education with cultural bridges"

        TOOL EXECUTION:
        1. Utilizza ESCLUSIVAMENTE "premium_financial_sources"
        2. Parametri: exclude_topics="crypto,day_trading,technical_analysis,complex_derivatives"
        3. Focus: contenuti dalle 8 fonti hardcoded per newsletter structure

        ESTRAZIONE CONTENT STRUTTURATA per Template Sections:

        FEATURE STORY CONTENT (280-320 words target):
        - 1 storia finanziaria principale della settimana rilevante per {topic}
        - Dati per cultural bridge (es: "Like your Netflix algorithm learning your preferences")
        - Context specifico per young investors impact
        - Material per "Our Take" community perspective (max 30 parole)

        MARKET REALITY CHECK CONTENT (200-250 words target):
        - 3-4 trend finanziari con Gen Z implications
        - Bullet points material con emoji potentials
        - Focus: housing, student debt, side hustles, values-based investing
        - Connections a generational financial challenges

        BY THE NUMBERS CONTENT (120-150 words target):
        - 3-5 statistiche verificabili con cultural context potential
        - Percentuali/dollar amounts con Gen Z analogies
        - Format: "[STAT] → [Cultural reference context]"

        YOUR MOVE ACTION CONTENT (150-180 words target):
        - Insights actionable non-promotional
        - Steps concreti per young professionals
        - Educational focus, zero Siebert promotion

        COMMUNITY CORNER MATERIAL (60-80 words target):
        - Success stories realistiche per spotlight
        - Engagement prompts autentici
        - Community building elements

        QUALITY ASSURANCE:
        - Tutti i contenuti REALI dalle fonti premium
        - Cultural bridges naturali e funzionali
        - Zero corporate messaging o promotional content
        - Trend verification cross-source
        - Gen Z relevance per ogni elemento
        """,
        expected_output="Report premium sources strutturato per template newsletter: feature story principale con cultural bridge, 3-4 market trends Gen Z-relevant, 3-5 statistiche con context, insights actionable, community content material - tutto ottimizzato per target audience e word count specifici",
        agent=agents["premium_sources_analyzer"],
        tools=[tools.get("premium_financial_sources")] if tools.get("premium_financial_sources") else [],
        context=[task1_enhanced_context]
    )
    
    # Task 3: Precision Newsletter Creation
    task3_precision_creation = Task(
        description=f"""
        OBIETTIVO: Creare newsletter Siebert seguendo ESATTAMENTE template e word count dalla knowledge base.
        
        INPUT SOURCES: 
        - Brief completo dal Task 1 (template structure + guidelines)
        - Content strutturato dal Task 2 (premium sources material)
        - Topic principale: {topic}

        NEWSLETTER CREATION - TEMPLATE ESATTO:

        **TITLE**: [CATCHY TITLE WITH EMOJI - MAX 9 WORDS] basato su {topic}
        
        **SECTION 1: COMMUNITY GREETING (50-60 words EXACT)**
        Hey Future Wealth Builders, 👋
        [Cultural hook connecting {topic} to trending reference + newsletter value preview]
        [WORD COUNT: 50-60 parole precise]
        
        **SECTION 2: FEATURE STORY (280-320 words EXACT)**
        [SECTION HEADER WITH EMOJI dai premium insights]
        [Main financial story dal premium analysis con cultural bridge]
        [2-3 paragraphs Gen Z perspective + young investor impact specifico]
        Our Take: [Community perspective max 30 words - ZERO Siebert promotion]
        [WORD COUNT: 280-320 parole precise]
        
        **SECTION 3: MARKET REALITY CHECK (200-250 words EXACT)**
        [REALITY-FOCUSED HEADER WITH EMOJI]
        [Brief intro current trends + Gen Z implications]
        
        • [Trend Category]: [emoji] [Gen Z impact description]
        • [Trend Category]: [emoji] [Gen Z impact description]  
        • [Trend Category]: [emoji] [Gen Z impact description]
        • [Trend Category]: [emoji] [Gen Z impact description]
        
        [Closing paragraph personal financial connection]
        [WORD COUNT: 200-250 parole precise]
        
        **SECTION 4: BY THE NUMBERS (120-150 words EXACT)**
        [DATA-FOCUSED HEADER WITH EMOJI]
        
        **[PERCENTAGE/NUMBER]** → [Cultural reference context] [emoji]
        **[DOLLAR AMOUNT]** → [Gen Z specific implication] [emoji]
        **[PERCENTAGE]** → [Analogy explanation] [emoji]
        **[NUMBER]** → [Young investor impact] [emoji]
        **[STATISTIC]** → [Cultural context] [emoji]
        
        [Paragraph connecting stats to human guidance themes]
        [WORD COUNT: 120-150 parole precise]
        
        **SECTION 5: YOUR MOVE THIS WEEK (150-180 words EXACT)**
        [ACTION-ORIENTED HEADER WITH EMOJI]
        [Motivational intro from community perspective]
        [Why actions matter for generation paragraph]
        
        This week's action items:
        ✅ [Concrete actionable step 1 dai premium insights]
        ✅ [Concrete actionable step 2 dai premium insights]
        ✅ [Concrete actionable step 3 dai premium insights]
        
        [Closing community learning reinforcement]
        [WORD COUNT: 150-180 parole precise]
        
        **SECTION 6: COMMUNITY CORNER (60-80 words EXACT)**
        [COMMUNITY-FOCUSED HEADER WITH EMOJI]
        
        **Reader Spotlight:** "[Success story quote with specific details]" - [Name], [Age], [City]
        **Your Turn:** [Engagement prompt about financial journey]
        [WORD COUNT: 60-80 parole precise]
        
        **SECTION 7: CLOSING & SPONSOR (80-100 words EXACT)**
        Stay empowered,
        **The Future Millionaires Community** 🚀
        
        **P.S.** — [Financial insight or community observation]
        
        ---
        
        ## **Sponsor**
        *This newsletter is proudly sponsored by Siebert Financial. Since 1967, Siebert has been providing personalized financial guidance to help individuals build wealth through every market cycle. Discover Siebert's services tailored for younger generations.*
        
        [**Explore Our Services →**]
        [WORD COUNT: 80-100 parole precise]

        CRITICAL COMPLIANCE CHECKLIST:
        ✅ TOTAL: 800-1200 words (verificare conteggio finale)
        ✅ Mobile-friendly: Paragrafi max 2-3 sentences
        ✅ Strategic emoji per visual breaks
        ✅ Community voice: "our generation," "as a community"
        ✅ Cultural references che servono understanding
        ✅ ZERO Siebert promotion nelle sezioni 1-6
        ✅ Actionable insights con immediate value
        ✅ Professional credibility senza condescension
        ✅ Editorial independence mantenuta
        ✅ Compliance language: "may help," "potential," "historically"

        DELIVERABLE: Newsletter completa con word count preciso per ogni sezione, tone Gen Z appropriato, contenuti premium integrati, compliance completa, zero promotional content nelle sezioni editoriali.
        """,
        expected_output="Newsletter Siebert completa seguendo esattamente template knowledge base: 7 sezioni strutturate con word count preciso (50-60, 280-320, 200-250, 120-150, 150-180, 60-80, 80-100), tone Gen Z, contenuti premium integrati, editorial independence, total 800-1200 parole",
        agent=agents["copywriter"],
        context=[task1_enhanced_context, task2_targeted_analysis]
    )
    
    return {
        "name": "Siebert Newsletter Premium - Knowledge Base Optimized",
        "description": "Workflow ottimizzato con knowledge base completa, template precision, word count accuracy e editorial independence",
        "agents": [
            agents["rag_specialist"],
            agents["premium_sources_analyzer"], 
            agents["copywriter"]
        ],
        "tasks": [
            task1_enhanced_context,
            task2_targeted_analysis,
            task3_precision_creation
        ],
        "tools": tools
    }

# Compatibility function
def get_workflow(topic, agents, tasks, tools, config=None):
    return create_workflow(topic, agents, tasks, tools, config)
