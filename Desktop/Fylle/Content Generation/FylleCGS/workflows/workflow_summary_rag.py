from crewai import Task

def create_workflow(topic, agents, tools, config):
    # Parametri principali (puoi renderli dinamici se vuoi)
    nome_doc = "[Base per Workflow]_BRAND MANUAL – Fylle.md"
    client_name = "Fylle"

    # Task 1: Recupera il contenuto dal RAG tramite il rag_specialist
    # Passa client_name come parametro extra (se il sistema lo supporta)
    task_rag = Task(
        agent=agents["rag_specialist"],
        description=f"Recupera il contenuto del documento '{nome_doc}' dalla knowledge base del cliente '{client_name}'. Se il documento non viene trovato, mostra la lista dei documenti disponibili.",
        input={
            "client_name": client_name,
            "document_name": nome_doc
        },
        tools=[tools["rag_tool"]],
        expected_output="Contenuto markdown integrale del documento richiesto oppure lista dei documenti disponibili se non trovato."
    )

    # Task 2: Genera un summary con il copywriter
    task_summary = Task(
        agent=agents["copywriter"],
        description=f"Leggi il contenuto del documento '{nome_doc}' e produci un riassunto chiaro e conciso dei punti chiave.",
        input="<output_of_task_1>",  # Da sostituire con l'output effettivo del primo task nella pipeline
        tools=[],
        expected_output="Riassunto chiaro e strutturato dei punti chiave del documento."
    )

    return {
        "name": "RAG Document Summary",
        "description": "Recupera un documento dal RAG e genera un summary automatico.",
        "agents": {
            "rag_specialist": agents["rag_specialist"],
            "copywriter": agents["copywriter"]
        },
        "tasks": [task_rag, task_summary],
        "tools": tools
    }

get_workflow = create_workflow
