from crewai import Task

def create_workflow(topic, agents, tools, config):
    # Extract configuration parameters
    client_name = config.get('client_name', '<PERSON><PERSON><PERSON>')
    target_audience = config.get('target_audience', 'General audience')
    context = config.get('context', '')

    # Template substitution function
    def substitute_template(text, variables):
        for key, value in variables.items():
            text = text.replace(f'{{{{{key}}}}}', str(value))
        return text

    # Define template variables
    template_vars = {
        'topic': topic,
        'client_name': client_name,
        'target_audience': target_audience,
        'context': context
    }

    return {
        "name": "Enhanced Article Creation",
        "description": "Workflow ottimizzato con setting iniziale, ricerca web e creazione contenuto finale",
        "agents": {
            "rag_specialist": agents["rag_specialist"],
            "web_searcher": agents["web_searcher"], 
            "copywriter": agents["copywriter"]
        },
        "tasks": [
            {
                "id": "task1_brief",
                "agent": "rag_specialist",
                "task": Task(
                    agent=agents["rag_specialist"],
                    description=substitute_template("""
TASK 1 - SETTING & BRIEF CREATION:
Recupera tutto il contenuto del cliente selezionato e crea un brief di lavoro completo che integri:

INPUT SOURCES:
- Topic richiesto: {{topic}}
- Contesto aggiuntivo: {{context}}
- Target audience: {{target_audience}}
- Cliente selezionato: {{client_name}}
- Knowledge base del cliente (utilizzando RAG Content Retriever)

OBIETTIVI:
1. Analizza la knowledge base del cliente per comprendere brand voice, style guidelines, e contenuti esistenti
2. Integra le informazioni dall'interfaccia Streamlit (topic, contesto, target)
3. Crea un brief strutturato che serva da riferimento per gli altri agent
4. Definisci chiaramente ruoli, obiettivi e output richiesto

STRUTTURA DEL BRIEF:
- Executive Summary del progetto
- Brand Context & Guidelines (dal RAG)
- Topic Analysis & Objectives  
- Target Audience Profile
- Content Requirements & Specifications
- Agent Roles & Responsibilities
- Success Criteria & Expected Output
                    """, template_vars),
                    expected_output='Expected Output: A comprehensive project brief in markdown format containing:\n- Executive Summary with clear project overview\n- Complete brand context from client knowledge base\n- Detailed topic analysis and content objectives\n- Target audience profile and preferences\n- Specific content requirements and format specifications\n- Clear role definitions for subsequent agents\n- Measurable success criteria and quality standards'
                )
            },
            {
                "id": "task2_research", 
                "agent": "web_searcher",
                "task": Task(
                    agent=agents["web_searcher"],
                    description=substitute_template("""
TASK 2 - WEB RESEARCH & BRIEF ENHANCEMENT:
Ricevi il brief creato nel Task precedente e arricchiscilo con ricerche web aggiornate e pertinenti.

CONTEXT FROM PREVIOUS TASK:
Il task precedente ha creato un brief completo. Utilizza questo brief come base e arricchiscilo.

INPUT:
{{task1_brief}}

OBIETTIVI:
1. Analizza il brief ricevuto per identificare gap informativi
2. Conduci ricerche web mirate su:
   - Trend attuali relativi a {{topic}}
   - Statistiche e dati recenti
   - Best practices del settore
   - Casi studio rilevanti
3. Integra le informazioni trovate nel brief esistente
4. Affina e migliora le sezioni del brief con dati aggiornati

FOCUS AREAS:
- Cerca informazioni che supportino gli obiettivi definiti nel brief
- Identifica opportunità per differenziare il contenuto
- Trova dati e statistiche che rafforzino i messaggi chiave
                    """, template_vars),
                    expected_output='Expected Output: Enhanced brief with web research integration in markdown format containing:\n- Original brief sections enhanced with current data\n- Key market trends and statistics\n- Relevant industry insights\n- Competitive analysis\n- Updated content strategy recommendations\n- Sources and references for all research data'
                ),
                "depends_on": ["task1_brief"]
            },
            {
                "id": "task3_content",
                "agent": "copywriter", 
                "task": Task(
                    agent=agents["copywriter"],
                    description=substitute_template("""
TASK 3 - FINAL CONTENT CREATION:
Utilizzando il brief arricchito del task precedente, crea l'articolo finale che rispetti tutti i requisiti definiti.

CONTEXT FROM PREVIOUS TASKS:
Hai accesso al brief originale e alla ricerca web integrata. Utilizza entrambi per creare contenuto eccellente.

INPUT:
{{task2_research}}

OBIETTIVI:
1. Analizza il brief arricchito per comprendere tutti i requirements
2. Struttura l'articolo seguendo le guidelines del brand
3. Integra seamlessly le informazioni di ricerca con il brand voice
4. Crea contenuto engaging che parli direttamente al target audience: {{target_audience}}
5. Assicura coerenza con tutti i criteri di successo definiti nel brief

CONTENT CREATION GUIDELINES:
- Segui scrupolosamente il brand voice definito nel brief
- Utilizza la terminologia specifica del cliente {{client_name}}
- Integra naturalmente dati e statistiche dalla ricerca
- Mantieni focus su obiettivi e target audience definiti
- Crea un flow narrativo coinvolgente e professionale
- Include call-to-action appropriati

QUALITY ASSURANCE:
- Verifica allineamento con brand guidelines
- Controlla coerenza del tone of voice
- Assicura che tutti i key messages siano inclusi
- Valida la rilevanza per il target audience
                    """, template_vars),
                    expected_output="Expected Output: A polished, publication-ready article in markdown format featuring:\n- Engaging introduction that hooks the reader\n- Well-structured body with clear sections and subheadings\n- Integration of web research data and client-specific insights\n- Professional tone aligned with brand guidelines\n- Compelling conclusion with actionable insights\n- Proper formatting and citations"
                ),
                "depends_on": ["task2_research"]
            }
        ],
        "tools": tools
    }

get_workflow = create_workflow