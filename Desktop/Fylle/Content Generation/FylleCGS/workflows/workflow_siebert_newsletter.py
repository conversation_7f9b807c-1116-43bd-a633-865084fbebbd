from crewai import Task

def create_workflow(topic, agents, tools, config):
    # Extract configuration parameters
    client_name = config.get('client_name', 'Siebert')
    target_audience = config.get('target_audience', 'Gen Z and young professionals')
    context = config.get('context', '')

    # Template substitution function
    def substitute_template(text, variables):
        for key, value in variables.items():
            text = text.replace(f'{{{{{key}}}}}', str(value))
        return text

    # Define template variables
    template_vars = {
        'topic': topic,
        'client_name': client_name,
        'target_audience': target_audience,
        'context': context
    }

    # Use specialized Siebert web scraper agent
    siebert_web_scraper = agents.get("siebert_web_scraper", agents["web_searcher"])

    # Debug logging for tools
    print(f"Available tools: {list(tools.keys())}")
    if "siebert_tool" in tools:
        print("✅ siebert_tool found in tools")
    if "siebert_newsletter_sources" in tools:
        print("✅ siebert_newsletter_sources found in tools")
    if "Siebert Newsletter Sources Analysis" in [getattr(tool, 'name', '') for tool in tools.values()]:
        print("✅ Siebert Newsletter Sources Analysis tool name found")

    return {
        "name": "Siebert Newsletter Creation",
        "description": "Workflow completo per la creazione di newsletter Siebert: analisi fonti premium, blog article e newsletter Gen Z",
        "agents": {
            "rag_specialist": agents["rag_specialist"],
            "siebert_web_scraper": siebert_web_scraper, 
            "copywriter": agents["copywriter"]
        },
        "tasks": [
            {
                "id": "task1_brief",
                "agent": "rag_specialist",
                "task": Task(
                    agent=agents["rag_specialist"],
                    description=substitute_template("""
TASK 1 - SETTING & KNOWLEDGE ASSEMBLY:
Recupera tutto il contenuto del cliente {{client_name}} e crea un brief completo per la creazione di newsletter e blog article.

INPUT SOURCES:
- Topic/lente di analisi: {{topic}}
- Contesto aggiuntivo: {{context}}
- Target audience: {{target_audience}}
- Cliente selezionato: {{client_name}}
- Knowledge base del cliente (utilizzando RAG Content Retriever)
- Eventuali PDF o file premium dalle fonti

OBIETTIVI:
1. Analizza la knowledge base del cliente per comprendere brand voice, style guidelines, personas
2. Integra le informazioni dall'interfaccia Streamlit (topic, contesto, target)
3. Identifica il "taglio" o "lente" tramite cui analizzare le fonti finanziarie
4. Crea un brief strutturato per newsletter + blog article
5. Definisci chiaramente ruoli, obiettivi e output richiesti per entrambi i formati

STRUTTURA DEL BRIEF:
- Executive Summary del progetto newsletter/blog
- Brand Context & Guidelines (dal RAG Siebert)
- Topic Analysis & Focus Lens Definition
- Target Audience Profile (Gen Z + general audience)
- Content Strategy per dual-format output
- Newsletter vs Blog Article specifications
- Agent Roles & Responsibilities 
- Success Criteria & Expected Output per ogni formato
                    """, template_vars),
                    expected_output='Expected Output: Comprehensive project brief in markdown format containing:\n- Executive Summary for newsletter and blog creation\n- Complete Siebert brand context from knowledge base\n- Detailed topic analysis and focus lens definition\n- Dual target audience profile (Gen Z + general)\n- Specific content requirements for both newsletter and blog formats\n- Clear role definitions for subsequent agents\n- Measurable success criteria for both outputs'
                )
            },
            {
                "id": "task2_sources_analysis", 
                "agent": "siebert_web_scraper", 
                "task": Task(
                    agent=siebert_web_scraper,
                    tools=[tools.get("siebert_tool")] if "siebert_tool" in tools else [],
                    description=substitute_template("""
TASK 2 - SOURCES ANALYSIS & DATA COLLECTION:
Utilizza il nuovo tool di scraping delle 9 fonti Siebert hardcoded per raccogliere informazioni filtrate tramite la lente del topic.

CONTEXT FROM PREVIOUS TASK:
Il task precedente ha creato un brief completo con focus lens definito. Utilizza questo brief come guida.

INPUT:
{{task1_brief}}

OBIETTIVI:
1. Analizza il brief per identificare la "lente" di analisi del topic
2. Utilizza il tool "siebert_newsletter_sources" per scraping delle 9 fonti premium
3. Filtra contenuti attraverso la lente del topic: {{topic}}
4. Esclude automaticamente contenuti su crypto e day trading
5. Crea summary strutturato delle informazioni più rilevanti
6. Identifica trend cross-source e insights actionable

FOCUS AREAS:
- Trend finanziari rilevanti per Gen Z e young professionals
- Dati e statistiche che supportano gli obiettivi del brief
- Contenuti educativi su investimenti e finanza personale
- Opportunità per differenziare il contenuto Siebert
- Insights specifici per il target audience definito

UTILIZZO TOOL OBBLIGATORIO:
Devi utilizzare SPECIFICAMENTE il tool "Siebert Newsletter Sources Analysis" (non Web Search Tool generico).
Parametri del tool:
- topic_lens: "{{topic}}"
- exclude_topics: "crypto,day_trading"

IMPORTANTE: Non utilizzare ricerche web generiche. Usa solo il tool specializzato Siebert per le 9 fonti hardcoded.
                    """, template_vars),
                    expected_output='Expected Output: Structured sources analysis in markdown format containing:\n- Executive summary of cross-source trends\n- Key headlines and insights filtered by topic lens\n- Relevant statistics and data points\n- Educational content opportunities\n- Gen Z specific financial insights\n- Source citations and credibility indicators\n- Actionable recommendations for content creation'
                ),
                "depends_on": ["task1_brief"]
            },
            {
                "id": "task3_blog_article",
                "agent": "copywriter", 
                "task": Task(
                    agent=agents["copywriter"],
                    description=substitute_template("""
TASK 3 - BLOG ARTICLE CREATION:
Utilizzando il brief e l'analisi delle fonti, crea un articolo di blog professionale per audience generale.

CONTEXT FROM PREVIOUS TASKS:
Hai accesso al brief Siebert completo e all'analisi strutturata delle 9 fonti premium.

INPUT:
{{task2_sources_analysis}}

OBIETTIVI:
1. Analizza brief e sources analysis per comprendere tutti i requirements
2. Crea articolo di blog professionale su {{topic}}
3. Integra seamlessly le informazioni dalle fonti premium con il brand voice Siebert
4. Mantieni tone professionale ma accessibile
5. Cita appropriatamente le fonti per credibilità
6. Include actionable insights per i lettori

CONTENT CREATION GUIDELINES:
- Segui scrupolosamente il brand voice Siebert definito nel brief
- Utilizza la terminologia specifica del cliente {{client_name}}
- Integra naturalmente dati e statistiche dalle 9 fonti premium
- Mantieni focus su obiettivi definiti nel brief
- Crea un flow narrativo coinvolgente e professionale
- Include call-to-action appropriati per il brand
- Target: audience generale interessata a finanza e investimenti

QUALITY ASSURANCE:
- Verifica allineamento con brand guidelines Siebert
- Controlla coerenza del tone of voice professionale
- Assicura che tutti i key insights dalle fonti siano inclusi
- Valida la rilevanza per il target audience generale
- Verifica citazioni delle fonti premium
                    """, template_vars),
                    expected_output="Expected Output: Professional blog article in markdown format featuring:\n- Engaging introduction that hooks the reader\n- Well-structured body with clear sections and subheadings\n- Integration of premium sources data and Siebert-specific insights\n- Professional tone aligned with brand guidelines\n- Proper citations of the 9 premium sources\n- Compelling conclusion with actionable insights\n- Appropriate call-to-action for general audience"
                ),
                "depends_on": ["task2_sources_analysis"]
            },
            {
                "id": "task4_genz_newsletter",
                "agent": "copywriter",
                "task": Task(
                    agent=agents["copywriter"],
                    description=substitute_template("""
TASK 4 - GEN Z NEWSLETTER CREATION:
Converte il contenuto in formato newsletter specifico per Gen Z utilizzando il brand voice adattato.

CONTEXT FROM PREVIOUS TASKS:
Hai accesso al brief completo, all'analisi delle fonti e all'articolo blog professionale creato.

INPUT:
{{task3_blog_article}}

OBIETTIVI:
1. Converte il contenuto dell'articolo blog in formato newsletter Gen Z
2. Adatta il tone of voice per essere più casuale e accessibile
3. Utilizza linguaggio Gen Z appropriato mantenendo credibilità
4. Focus su actionable insights immediati
5. Include elementi interattivi e engaging per il formato newsletter
6. Mantieni coerenza con il brand Siebert

NEWSLETTER FORMATTING GUIDELINES:
- Subject line accattivante per Gen Z
- Introduzione breve e diretta con hook forte
- Sezioni bite-sized con titoli engaging
- Uso di emoji appropriati ma professionale
- Call-to-action specifici per Gen Z (social media, app, etc.)
- Linguaggio conversazionale ma informativo
- Focus su "what's in it for me" immediato

GEN Z TONE ADAPTATION:
- Conversazionale ma non troppo informale
- Utilizza terminologia finanziaria accessibile
- Include riferimenti culturali Gen Z appropriati
- Mantieni autorevolezza Siebert
- Enfatizza benefici pratici e immediati
- Evita jargon finanziario complesso

CONTENT STRUCTURE:
- Subject line + preview text
- Intro engaging (max 2-3 righe)
- 3-4 sezioni principali con insights actionable
- Closing con call-to-action specifico Gen Z
- Footer con social links e unsubscribe
                    """, template_vars),
                    expected_output="Expected Output: Gen Z newsletter in markdown format featuring:\n- Catchy subject line optimized for Gen Z\n- Engaging intro with strong hook\n- Bite-sized sections with actionable insights\n- Conversational but professional tone\n- Appropriate emojis and Gen Z language\n- Social media optimized call-to-actions\n- Mobile-friendly formatting\n- Clear value proposition for young professionals"
                ),
                "depends_on": ["task3_blog_article"]
            }
        ],
        "tools": tools
    }

get_workflow = create_workflow