FROM python:3.11-slim

WORKDIR /app

# Copia vincoli e requirements
COPY constraints.txt ./constraints.txt
COPY requirements.txt ./requirements.txt

# Installa dipendenze con constraints
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt -c constraints.txt

# Copia il codice
COPY . .

# Espone la porta su cui gira Streamlit
EXPOSE 8501

# Avvia l’app
CMD ["streamlit", "run", "app.py", "--server.address=0.0.0.0", "--server.port=8501"]
