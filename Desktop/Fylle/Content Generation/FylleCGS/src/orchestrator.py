import re
from typing import Dict, Any
from crewai import Task

def run_workflow(workflow: Dict[str, Any], topic: str = None, verbose: bool = True) -> Dict[str, Any]:
    """
    Orchestratore generico per workflow dichiarativi: esegue i task rispettando le dipendenze e propaga gli output.
    """
    tasks = workflow["tasks"]
    outputs = {}
    # Costruisci una mappa id -> task_dict
    id2task = {t["id"]: t for t in tasks}
    executed = set()

    def resolve_prompt(prompt: str, context: Dict[str, str]) -> str:
        # Sostituisce i placeholder {{id}} con l'output del task corrispondente
        def repl(match):
            key = match.group(1)
            return context.get(key, f"{{{{{key}}}}}")
        return re.sub(r"{{(\w+)}}", repl, prompt)

    def run_task(task_id: str):
        if task_id in outputs:
            return outputs[task_id]
        task_dict = id2task[task_id]
        task_obj: Task = task_dict["task"]
        # Risolvi le dipendenze
        context = {}
        if "depends_on" in task_dict:
            for dep in task_dict["depends_on"]:
                context[dep] = run_task(dep)
        # Sostituisci i placeholder nel prompt, se presenti
        if hasattr(task_obj, "description") and context:
            task_obj.description = resolve_prompt(task_obj.description, context)
        if verbose:
            print(f"\n--- Eseguo task: {task_id} ---")
            print(task_obj.description)
        # Esegui il task CrewAI (blocking)
        output = task_obj.execute() if hasattr(task_obj, "execute") else f"[MOCK OUTPUT for {task_id}]"
        outputs[task_id] = output
        return output

    # Esegui tutti i task in ordine dichiarato
    for t in tasks:
        run_task(t["id"])
    return outputs
