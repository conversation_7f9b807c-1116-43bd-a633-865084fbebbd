
# tools_websearch.py
# Tool specializzato per estrarre contenuti da fonti finanziarie premium specifiche

import os
import json
import logging
from typing import Dict, Optional, List
from pathlib import Path
import requests
from langchain.tools import Tool
from datetime import datetime
import re
from urllib.parse import urljoin, urlparse

logger = logging.getLogger(__name__)

def extract_individual_articles(url: str) -> List[Dict[str, str]]:
    """
    Extract individual articles from a page listing.
    
    Args:
        url: URL to scrape for article links
        
    Returns:
        List of dictionaries with article data
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        content = response.text
        articles = []
        
        # Extract article links and titles based on common patterns
        article_patterns = [
            r'<a[^>]*href="([^"]*)"[^>]*>([^<]*)</a>',  # General links
            r'<h[123][^>]*><a[^>]*href="([^"]*)"[^>]*>([^<]*)</a></h[123]>',  # Headlines
            r'<article[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>([^<]*)</a>.*?</article>',  # Articles
        ]
        
        for pattern in article_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches[:5]:  # Max 5 per pattern
                if len(match) == 2:
                    link, title = match
                    # Make absolute URL if relative
                    if link.startswith('/'):
                        base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
                        link = base_url + link
                    elif not link.startswith('http'):
                        continue
                        
                    # Clean title
                    title = re.sub(r'\s+', ' ', title.strip())
                    if len(title) > 10 and len(title) < 200:  # Reasonable title length
                        articles.append({"url": link, "title": title})
        
        return articles[:8]  # Max 8 articles
        
    except Exception as e:
        logger.warning(f"Failed to extract articles from {url}: {str(e)}")
        return []

def extract_page_content_premium(url: str, max_chars: int = 4000) -> str:
    """
    Extract actual content from premium financial websites with better parsing.
    
    Args:
        url: URL to scrape
        max_chars: Maximum characters to extract from content
        
    Returns:
        Cleaned text content from the page with title and summary
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        content = response.text
        
        # Extract title
        title_match = re.search(r'<title[^>]*>(.*?)</title>', content, re.IGNORECASE | re.DOTALL)
        title = title_match.group(1).strip() if title_match else ""
        title = re.sub(r'\s+', ' ', title)
        
        # Extract meta description
        meta_desc = ""
        meta_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\']', content, re.IGNORECASE)
        if meta_match:
            meta_desc = meta_match.group(1).strip()
            
        # Try to find main content areas
        main_content = ""
        content_patterns = [
            r'<main[^>]*>(.*?)</main>',
            r'<article[^>]*>(.*?)</article>',
            r'<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>',
            r'<div[^>]*class="[^"]*post[^"]*"[^>]*>(.*?)</div>',
            r'<div[^>]*class="[^"]*entry[^"]*"[^>]*>(.*?)</div>',
        ]
        
        for pattern in content_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                main_content = matches[0]
                break
        
        # If no main content found, use body
        if not main_content:
            body_match = re.search(r'<body[^>]*>(.*?)</body>', content, re.IGNORECASE | re.DOTALL)
            main_content = body_match.group(1) if body_match else content
        
        # Remove script, style, nav, footer elements
        main_content = re.sub(r'<script[^>]*>.*?</script>', '', main_content, flags=re.DOTALL | re.IGNORECASE)
        main_content = re.sub(r'<style[^>]*>.*?</style>', '', main_content, flags=re.DOTALL | re.IGNORECASE)
        main_content = re.sub(r'<nav[^>]*>.*?</nav>', '', main_content, flags=re.DOTALL | re.IGNORECASE)
        main_content = re.sub(r'<footer[^>]*>.*?</footer>', '', main_content, flags=re.DOTALL | re.IGNORECASE)
        main_content = re.sub(r'<header[^>]*>.*?</header>', '', main_content, flags=re.DOTALL | re.IGNORECASE)
        main_content = re.sub(r'<aside[^>]*>.*?</aside>', '', main_content, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove HTML tags
        main_content = re.sub(r'<[^>]+>', ' ', main_content)
        
        # Clean up whitespace and decode HTML entities
        main_content = re.sub(r'\s+', ' ', main_content).strip()
        main_content = main_content.replace('&nbsp;', ' ').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
        main_content = main_content.replace('&quot;', '"').replace('&#39;', "'")
        
        # Combine title, description and content
        full_content = f"TITLE: {title}\n"
        if meta_desc:
            full_content += f"DESCRIPTION: {meta_desc}\n"
        full_content += f"\nCONTENT: {main_content}"
        
        # Truncate to max_chars
        if len(full_content) > max_chars:
            full_content = full_content[:max_chars] + "..."
            
        return full_content
        
    except Exception as e:
        logger.warning(f"Failed to extract content from {url}: {str(e)}")
        return f"Failed to extract content from {url}: {str(e)}"

def extract_financial_insights(content: str, url: str) -> dict:
    """
    Extract structured financial insights from premium content.
    
    Args:
        content: Text content to analyze
        url: Source URL for context
        
    Returns:
        Dictionary with structured financial insights
    """
    if not content:
        return {"insights": [], "data_points": [], "trends": [], "source": url}
    
    content_lower = content.lower()
    insights = []
    data_points = []
    trends = []
    
    # Extract numerical data (percentages, billions, millions, etc.)
    number_patterns = [
        r'(\d+\.?\d*)\s*%',  # Percentages
        r'\$(\d+\.?\d*)\s*(billion|million|trillion|k)',  # Dollar amounts
        r'(\d+\.?\d*)\s*(billion|million|trillion|bps|basis\s*points?)',  # Large numbers
        r'(\d{4})\s*(q[1-4]|quarter)',  # Quarterly data
        r'(\d+\.?\d*)\s*points?',  # Points changes
    ]
    
    for pattern in number_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches[:5]:  # Limit to 5 per pattern
            if isinstance(match, tuple):
                data_points.append(" ".join(str(m) for m in match if m))
            else:
                data_points.append(str(match))
    
    # Extract key financial insights/events
    insight_patterns = [
        r'(fed|federal reserve|rate cut|rate hike|interest rate|monetary policy)[^.]{0,150}',
        r'(inflation|cpi|price index|deflation)[^.]{0,150}',
        r'(earnings|revenue|profit|quarterly results|financial results)[^.]{0,150}',
        r'(market|stocks?|trading|nasdaq|s&p|dow)[^.]{0,150}',
        r'(economy|economic|gdp|growth|recession)[^.]{0,150}',
        r'(investment|investing|portfolio|allocation)[^.]{0,150}',
        r'(crypto|bitcoin|ethereum|digital assets)[^.]{0,150}',
        r'(housing|real estate|mortgage|property)[^.]{0,150}',
    ]
    
    for pattern in insight_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        insights.extend([match for match in matches[:3] if len(match) > 20])  # Filter short matches
    
    # Extract trend indicators
    trend_patterns = [
        r'(rising|falling|up|down|increased|decreased|surge|drop|rally|decline)[^.]{0,100}',
        r'(trend|trending|outlook|forecast|prediction)[^.]{0,100}',
        r'(this week|this month|recently|latest|new)[^.]{0,100}',
        r'(announced|launched|released|reported|unveiled)[^.]{0,100}',
    ]
    
    for pattern in trend_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        trends.extend([match for match in matches[:3] if len(match) > 15])
    
    return {
        "insights": insights[:8],  # Top 8 insights
        "data_points": list(set(data_points))[:8],  # Top 8 unique data points
        "trends": trends[:6],  # Top 6 trends
        "source": url
    }

def premium_financial_sources_search(topic_lens: str = "", exclude_topics: str = "crypto,day_trading") -> str:
    """
    Tool per estrarre contenuti finanziari aggiornati da fonti premium specifiche.
    
    Args:
        topic_lens: Focus specifico per il contenuto (es. "investment strategies", "market trends")
        exclude_topics: Topics da escludere (separati da virgola)
        
    Returns:
        Contenuto finanziario strutturato dalle fonti premium
    """
    logger.info(f"Extracting content from premium sources with topic lens: {topic_lens}")
    
    # Fonti premium specifiche
    premium_sources = [
        "https://www.thedailyupside.com/finance/",
        "https://www.thedailyupside.com/investments/", 
        "https://www.thedailyupside.com/economics/", 
        "https://www.thedailyupside.com/newsletter/", 
        "https://moneywithkatie.com/blog/category/investing",
        "https://thehustle.co/news",
        "https://www.morningbrew.com/tag/finance",
        "https://www.morningbrew.com/tag/economy"
    ]
    
    # Escludiamo topics non desiderati
    exclude_list = [t.strip().lower() for t in exclude_topics.split(",") if t.strip()]
    
    all_content = []
    source_summaries = {}
    
    for source_url in premium_sources:
        try:
            logger.info(f"Extracting content from: {source_url}")
            
            # Prima estrai gli articoli individuali dalla pagina listing
            articles = extract_individual_articles(source_url)
            
            # Se non ci sono articoli, prova a estrarre il contenuto della pagina principale
            if not articles:
                page_content = extract_page_content_premium(source_url)
                if page_content and len(page_content) > 200:
                    articles = [{"url": source_url, "title": "Main Page Content"}]
            
            # Estrai contenuto da ogni articolo
            source_articles = []
            for article in articles[:5]:  # Max 5 articoli per fonte
                try:
                    article_content = extract_page_content_premium(article["url"])
                    if article_content and len(article_content) > 200:
                        # Check esclusioni
                        content_text = article_content.lower()
                        should_exclude = any(exclude_topic in content_text for exclude_topic in exclude_list)
                        
                        if not should_exclude:
                            # Calcola rilevanza
                            relevance_score = 0
                            if topic_lens:
                                lens_words = topic_lens.lower().split()
                                relevance_score = sum(1 for word in lens_words if word in content_text)
                            
                            # Estrai insights
                            insights_data = extract_financial_insights(article_content, article["url"])
                            
                            source_articles.append({
                                "title": article["title"],
                                "url": article["url"],
                                "content": article_content,
                                "insights": insights_data,
                                "relevance_score": relevance_score
                            })
                            
                except Exception as e:
                    logger.warning(f"Error extracting article {article['url']}: {str(e)}")
                    continue
            
            if source_articles:
                # Determina la categoria della fonte
                source_name = urlparse(source_url).netloc.replace('www.', '')
                if 'dailyupside' in source_name:
                    category = "Daily Upside"
                elif 'moneywithkatie' in source_name:
                    category = "Money with Katie"
                elif 'thehustle' in source_name:
                    category = "The Hustle"
                elif 'morningbrew' in source_name:
                    category = "Morning Brew"
                else:
                    category = source_name
                
                # Calcola metrics aggregate per fonte
                total_relevance = sum(article["relevance_score"] for article in source_articles)
                all_insights = []
                all_data_points = []
                all_trends = []
                
                for article in source_articles:
                    all_insights.extend(article["insights"]["insights"])
                    all_data_points.extend(article["insights"]["data_points"])
                    all_trends.extend(article["insights"]["trends"])
                
                source_summaries[category] = {
                    "url": source_url,
                    "articles_count": len(source_articles),
                    "top_articles": [{"title": a["title"], "url": a["url"], "relevance": a["relevance_score"]} for a in source_articles[:3]],
                    "insights": all_insights[:10],
                    "data_points": list(set(all_data_points))[:8],
                    "trends": all_trends[:6],
                    "total_relevance_score": total_relevance,
                    "best_content": max(source_articles, key=lambda x: x["relevance_score"])["content"][:800] + "..." if source_articles else ""
                }
                
                all_content.extend(source_articles)
                    
        except Exception as e:
            logger.warning(f"Error processing source {source_url}: {str(e)}")
            continue
    
    if not all_content:
        return f"""# Premium Financial Sources Analysis
**Topic Focus**: {topic_lens}
**Analysis Date**: {datetime.now().strftime('%Y-%m-%d %H:%M')}

## ⚠️ No Content Retrieved

Unable to extract content from the premium sources. This may be due to:
- Network connectivity issues
- Website access restrictions
- Content behind paywalls
- Temporary server issues

**Recommendation**: Try again later or check source accessibility.
"""
    
    # Ordiniamo per rilevanza
    all_content.sort(key=lambda x: x["relevance_score"], reverse=True)
    
    # Generiamo il summary strutturato con contenuti reali
    final_summary = f"""# Premium Financial Sources Analysis - REAL CONTENT EXTRACTION
**Topic Focus**: {topic_lens}
**Analysis Date**: {datetime.now().strftime('%Y-%m-%d %H:%M')}
**Sources Analyzed**: {len(source_summaries)}
**Total Articles Extracted**: {len(all_content)}

## 📊 SOURCE BREAKDOWN

"""
    
    # Aggiungiamo dettagli per ogni fonte con contenuti reali
    for source_name, data in source_summaries.items():
        final_summary += f"### 🔗 {source_name}\n"
        final_summary += f"**Articles Extracted**: {data['articles_count']}\n"
        final_summary += f"**Total Relevance Score**: {data['total_relevance_score']}\n"
        final_summary += f"**Source URL**: [{source_name}]({data['url']})\n\n"
        
        # Top articles estratti
        if data["top_articles"]:
            final_summary += "**📰 Top Articles Extracted:**\n"
            for article in data["top_articles"]:
                final_summary += f"- **{article['title']}** (Relevance: {article['relevance']})\n"
                final_summary += f"  [{article['title']}]({article['url']})\n"
            final_summary += "\n"
        
        # Key insights dai contenuti reali
        if data["insights"]:
            final_summary += "**💡 Key Financial Insights:**\n"
            for insight in data["insights"][:4]:
                if isinstance(insight, str) and len(insight.strip()) > 15:
                    final_summary += f"- {insight.strip()}\n"
            final_summary += "\n"
        
        # Data points estratti
        if data["data_points"]:
            final_summary += "**📊 Financial Data Points:**\n"
            for dp in data["data_points"][:4]:
                if dp and len(str(dp)) > 1:
                    final_summary += f"- {dp}\n"
            final_summary += "\n"
        
        # Preview del contenuto migliore
        if data["best_content"]:
            final_summary += f"**📄 Best Content Preview:**\n"
            final_summary += f"```\n{data['best_content']}\n```\n\n"
        
        final_summary += "---\n\n"
    
    # Aggiungiamo insights cross-source dai contenuti reali
    all_insights = []
    all_data_points = []
    all_trends = []
    high_relevance_articles = []
    
    for content in all_content:
        all_insights.extend(content["insights"]["insights"])
        all_data_points.extend(content["insights"]["data_points"])
        all_trends.extend(content["insights"]["trends"])
        if content["relevance_score"] > 2:
            high_relevance_articles.append(content)
    
    final_summary += "## 🎯 CROSS-SOURCE ANALYSIS\n\n"
    
    if high_relevance_articles:
        final_summary += f"### 🌟 High-Relevance Articles ({len(high_relevance_articles)} found)\n"
        for article in sorted(high_relevance_articles, key=lambda x: x["relevance_score"], reverse=True)[:5]:
            final_summary += f"- **{article['title']}** (Score: {article['relevance_score']})\n"
            final_summary += f"  Source: {article.get('source', 'Unknown')}\n"
        final_summary += "\n"
    
    if all_data_points:
        final_summary += "### 📈 Financial Data Points Across Sources\n"
        unique_data_points = list(set([dp for dp in all_data_points if dp and len(str(dp)) > 1]))[:10]
        for dp in unique_data_points:
            final_summary += f"- {dp}\n"
        final_summary += "\n"
    
    if all_insights:
        final_summary += "### 💡 Key Financial Insights Cross-Source\n"
        unique_insights = list(set([insight for insight in all_insights if len(insight) > 20]))[:8]
        for insight in unique_insights:
            final_summary += f"- {insight.strip()}\n"
        final_summary += "\n"
    
    # Initialize unique_trends to avoid variable reference errors
    unique_trends = []
    if all_trends:
        final_summary += "### 📊 Trending Financial Topics\n"
        unique_trends = list(set([t for t in all_trends if len(t) > 15]))[:6]
        for trend in unique_trends:
            final_summary += f"- {trend.strip()}\n"
        final_summary += "\n"
    
    final_summary += f"""## 💡 CONTENT CREATION OPPORTUNITIES

**High-Relevance Sources** (Based on "{topic_lens}"):
"""
    
    top_sources = sorted(source_summaries.items(), key=lambda x: x[1]["total_relevance_score"], reverse=True)
    for source_name, data in top_sources[:3]:
        final_summary += f"- **{source_name}** ({data['articles_count']} articles, Total Score: {data['total_relevance_score']})\n"
    
    final_summary += f"""

**🎯 Recommended Content Angles (Based on Real Extracted Content):**
- **Educational Explainers**: Use specific data points found across {len(source_summaries)} sources
- **Gen Z Financial Education**: Leverage {len(high_relevance_articles)} high-relevance articles for authentic insights
- **Current Market Analysis**: {len(unique_data_points)} financial data points to explain current trends
- **Practical Money Tips**: Cross-reference insights from {len(all_insights)} financial insights

**📋 Immediate Next Steps:**
1. **Newsletter Content**: Use top {len(high_relevance_articles)} high-relevance articles as primary sources
2. **Blog Topics**: Develop {len(unique_trends)} trending topic angles identified across sources  
3. **Social Media**: Create infographics from {len(unique_data_points)} verified data points
4. **Siebert Differentiation**: Position human-first guidance against robo-advisor trends found in sources

**🔄 Content Refresh Cycle:**
- Re-run this analysis weekly to capture fresh financial content
- Focus on sources showing highest relevance scores: {', '.join([name for name, data in top_sources[:3]])}
"""
    
    logger.info(f"Premium sources analysis completed - extracted content from {len(source_summaries)} sources")
    return final_summary

def get_premium_financial_tools() -> Dict[str, Tool]:
    """
    Get premium financial sources tools.
    """
    try:
        premium_sources_tool = Tool(
            name="Premium Financial Sources Analysis",
            description="Extracts current financial content from premium sources including Daily Upside, Money with Katie, The Hustle, and Morning Brew. Analyzes content through a specified topic lens and provides structured insights.",
            func=premium_financial_sources_search
        )
        
        return {
            "premium_financial_sources": premium_sources_tool
        }
    except Exception as e:
        logger.error(f"Error creating premium financial tools: {str(e)}")
        return {"premium_financial_sources": None}
