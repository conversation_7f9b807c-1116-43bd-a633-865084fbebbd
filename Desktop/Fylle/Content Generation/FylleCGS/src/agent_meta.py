import yaml
from pathlib import Path
from src.profiles import PROFILES_DIR, DEFAULT_PROFILE
# from src.tasks_and_tools import workflow_factory
from src.workflow_factory import workflow_factory

# Built-in default metadata for agents
BUILTIN_META: dict[str, dict] = {
    "web_searcher": {"system_message": "You are a Web Research Specialist.", "backstory": "Esperto digital researcher con una grande capacità di trovare informazioni affidabili rapidamente.", "goal": "Produrre riassunti di ricerca completi e accurati basati su ricerche web.", "examples": []},
    "copywriter": {"system_message": "You are a Copywriter.", "backstory": "Copywriter esperto con una comprovata esperienza nella creazione di contenuti persuasivi.", "goal": "Creare copy coinvolgenti e persuasivi che stimolino l'engagement.", "examples": []},
    "rag_specialist": {"system_message": "You are a RAG Knowledge Specialist.", "backstory": "Specialista nell'applicazione di conoscenza da sistemi RAG.", "goal": "Integrare conoscenza rilevante da fonti RAG nei contenuti.", "examples": []},
    "premium_sources_analyzer": {"system_message": "You are a Premium Financial Sources Analyzer.", "backstory": "Specialista nell'analisi di fonti finanziarie premium con esperienza nell'estrazione di insights rilevanti per content creation.", "goal": "Analizzare fonti finanziarie premium e estrarre insights strutturati per la creazione di contenuti.", "examples": []}
}


def load_all_agents_meta() -> dict:
    """Backward compatibility: load default profile metadata."""
    return load_profile_meta(DEFAULT_PROFILE.name)

def list_profiles() -> list[str]:
    """List all profile IDs in PROFILES_DIR."""
    PROFILES_DIR.mkdir(exist_ok=True)
    return [p.name for p in PROFILES_DIR.iterdir() if p.is_dir()]

def get_agent_keys() -> list[str]:
    """Retrieve agent IDs from workflow factory or fallback."""
    try:
        return workflow_factory.get_agent_keys()
    except Exception:
        return list(BUILTIN_META.keys())

def generate_profile_stubs(profile_id: str) -> None:
    """Create stub files for a profile's agents."""
    agents_dir = PROFILES_DIR / profile_id / "agents"
    agents_dir.mkdir(parents=True, exist_ok=True)
    for agent_id in get_agent_keys():
        stub_file = agents_dir / f"{agent_id}.yaml"
        if not stub_file.exists():
            with open(stub_file, "w", encoding="utf-8") as f:
                yaml.safe_dump({"system_message": "", "backstory": "", "goal": "", "examples": []}, f, sort_keys=False)

def load_profile_meta(profile_id: str) -> dict[str, dict]:
    """Load and merge metadata for all agents in a profile."""
    agents_dir = PROFILES_DIR / profile_id / "agents"
    agents_dir.mkdir(parents=True, exist_ok=True)
    profile_meta: dict[str, dict] = {}
    for agent_id, default in BUILTIN_META.items():
        yaml_file = agents_dir / f"{agent_id}.yaml"
        if yaml_file.exists():
            try:
                with open(yaml_file, "r", encoding="utf-8") as f:
                    custom = yaml.safe_load(f) or {}
            except:
                custom = {}
        else:
            custom = {}
        profile_meta[agent_id] = {**default, **custom}
    return profile_meta

def save_profile_meta(profile_id: str, agent_id: str, meta: dict) -> None:
    """Save metadata for an agent in a specific profile."""
    agents_dir = PROFILES_DIR / profile_id / "agents"
    agents_dir.mkdir(parents=True, exist_ok=True)
    file_path = agents_dir / f"{agent_id}.yaml"
    with open(file_path, "w", encoding="utf-8") as f:
        yaml.safe_dump(meta, f, sort_keys=False)

def save_agent_meta(agent_id: str, meta: dict) -> None:
    """Save metadata to default profile."""
    save_profile_meta(DEFAULT_PROFILE.name, agent_id, meta)
