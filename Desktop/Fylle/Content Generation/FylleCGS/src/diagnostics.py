from typing import List, Dict, Any
import traceback
from src.agents import AgentsFactory
from src.tools import get_available_tools
from src.tasks import get_available_tasks

def run_agent_tool_tests() -> list[dict]:
    """
    Test di integrazione agent/tool/task: verifica che ogni agente possa usare i tool assegnati sui task previsti.
    Restituisce una lista di dict con task, agente, tool, esito, output, errori.
    """
    import traceback
    from src.agents import AgentsFactory
    report = []
    factory = AgentsFactory()
    agent_roles = factory.get_supported_roles() if hasattr(factory, 'get_supported_roles') else [
        'web_searcher', 'architect', 'section_writer', 'copywriter', 'editor', 'quality_reviewer', 'rag_specialist'
    ]
    tools = get_available_tools()
    print("DEBUG - tools disponibili:")
    for k, v in tools.items():
        print(f"  chiave: {k}, name: {getattr(v, 'name', v)}")
    agents = factory.create_agents(tools)  # Assegna tool agli agenti come in produzione
    tasks = get_available_tasks(agents)

    print("DEBUG - agent_roles:", agent_roles)
    print("DEBUG - tools (dict):", tools)
    print("DEBUG - agents (dict):", agents)
    print("DEBUG - tasks (dict):", tasks)

    import glob
    import os
    print("DEBUG - CWD:", os.getcwd())
    base_dir = os.path.dirname(os.path.abspath(__file__))
    rag_dir = os.path.normpath(os.path.join(base_dir, "..", "rag"))
    print("DEBUG - rag_dir:", rag_dir)
    md_files = glob.glob(os.path.join(rag_dir, "**/*.md"), recursive=True)
    print("DEBUG - md_files trovati:", md_files)
    import random
    test_md_file = random.choice(md_files) if md_files else None

    for agent_role, agent in agents.items():
        agent_tools = getattr(agent, 'tools', [])
        print(f"DEBUG - agent '{agent_role}' tools:", agent_tools)
        for tool in agent_tools:
            print(f"DEBUG - tool object: {tool}, name: {getattr(tool, 'name', str(tool))}")
            tool_name = getattr(tool, 'name', getattr(tool, '__name__', str(tool)))
            for task_name, task_fn in tasks.items():
                test_result = {
                    'task': task_name,
                    'agent': agent_role,
                    'tool': tool_name,
                    'result': None,
                    'output': None,
                    'error': None,
                }
                print(f"DEBUG - tool_name: {tool_name}")
                print(f"DEBUG - callable: {tool}, type: {type(tool)}")
                try:
                    if tool_name.lower().startswith("web search"):
                        output = tool("test input")
                    elif tool_name.lower().startswith("markdown reader"):
                        if test_md_file:
                            result = tool(test_md_file)
                            # Mostra solo un estratto (prime 10 righe)
                            if isinstance(result, str):
                                output = "\n".join(result.splitlines()[:10])
                            else:
                                output = str(result)
                        else:
                            output = "Nessun file .md trovato in rag/"
                    elif tool_name.lower().startswith("markdown writer"):
                        output = tool("diagnostic_test.md", "# Test content")
                    elif "rag" in tool_name.lower():
                        # Trova i client disponibili nella directory rag/
                        client_dirs = [d for d in os.listdir(rag_dir) if os.path.isdir(os.path.join(rag_dir, d))]
                        if client_dirs:
                            chosen_client = random.choice(client_dirs)
                            print(f"DEBUG - chiamata tool RAG Retriever: {tool}, chosen_client: {chosen_client}")
                            try:
                                result = tool(chosen_client)
                            except Exception as e:
                                print(f"DEBUG - errore chiamata tool RAG Retriever: {e}")
                                from src.tools import read_rag_content
                                print(f"DEBUG - provo chiamata diretta read_rag_content({chosen_client})")
                                result = read_rag_content(chosen_client)
                            print(f"DEBUG - output tool RAG Retriever: {result}")
                            # Mostra solo un estratto (prime 10 righe o 500 caratteri)
                            if isinstance(result, str):
                                output = "\n".join(result.splitlines()[:10])[:500]
                            else:
                                output = str(result)[:500]
                            if output and chosen_client.lower() in output.lower():
                                test_result['result'] = 'SUCCESS'
                            else:
                                test_result['result'] = 'FAIL'
                                test_result['error'] = f"Output non rilevante: non contiene '{chosen_client}'. Output: {output}"
                        else:
                            output = "Nessun client trovato nella directory rag/"
                            test_result['result'] = 'FAIL'
                            test_result['error'] = output
                    else:
                        output = tool("test input")
                    if test_result['result'] != 'FAIL':
                        test_result['result'] = 'SUCCESS'
                        test_result['error'] = None
                    test_result['output'] = output
                except Exception as e:
                    test_result['result'] = 'FAIL'
                    test_result['output'] = None
                    test_result['error'] = traceback.format_exc()
                report.append(test_result)

    print("DEBUG - Numero risultati nel report:", len(report))
    print("REPORT (last print in diagnostics):", report)
    return report
