import requests
from typing import Optional, Dict, Any

class DeepseekChatAPI:
    """
    Simple wrapper for Deepseek Chat API (v1).
    """
    API_URL = "https://api.deepseek.com/v1/chat/completions"

    def __init__(self, api_key: str, model: str = "deepseek-chat", temperature: float = 0.7, max_tokens: int = 1000, top_p: float = 1.0):
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p

    def chat(self, prompt: str) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p
        }
        try:
            response = requests.post(self.API_URL, headers=headers, json=data, timeout=10)
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            return f"[Deepseek API error: {str(e)}]"

    def health_check(self) -> str:
        # Use a minimal harmless prompt for health check
        test_prompt = "Hello!"
        result = self.chat(test_prompt)
        return "OK" if result else "Not Running"
