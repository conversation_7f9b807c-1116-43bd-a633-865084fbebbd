from pathlib import Path
from typing import Dict, Callable, List, Any
import os
import importlib.util
import json

def get_predefined_tasks() -> Dict[str, Callable]:
    """Ritorna mappa nome→factory dei task predefiniti."""
    tasks_dir = Path(__file__).parent / "tasks" / "predefined"
    tasks = {}
    if tasks_dir.exists():
        for pyfile in tasks_dir.glob("*.py"):
            module_name = f"predefined_{pyfile.stem}"
            spec = importlib.util.spec_from_file_location(module_name, str(pyfile))
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                # Assumiamo che ogni modulo esponga una funzione factory con nome uguale al file
                factory_func = getattr(module, pyfile.stem, None)
                if callable(factory_func):
                    tasks[pyfile.stem] = factory_func
    else:
        # Lista statica di fallback
        tasks = {
            "research": lambda *a, **kw: None,
            "content_planning": lambda *a, **kw: None,
            "section_writing": lambda *a, **kw: None,
            "copywriting": lambda *a, **kw: None,
            "editing": lambda *a, **kw: None,
            "quality_review": lambda *a, **kw: None,
            "rag_integration": lambda *a, **kw: None
        }
    return tasks

def load_custom_tasks(profile_name: str) -> List[Dict]:
    """Carica eventuali task custom per il profilo."""
    custom_path = Path(__file__).parent.parent / "profiles" / profile_name / "custom_tasks.json"
    if custom_path.exists():
        with open(custom_path, "r", encoding="utf-8") as f:
            try:
                return json.load(f)
            except Exception:
                return []
    return []

def generate_workflow_code(name: str, description: str, selected_agents: Dict[str,Any], task_sequence: List[Dict], tools: List[str]) -> str:
    """Genera il codice Python del workflow."""
    code = "from crewai import Task\n\n"
    agent_lines = []
    for k in selected_agents:
        agent_lines.append(f'        "{k}": agents["{k}"]')
    agents_block = ",\n".join(agent_lines)
    task_lines = []
    for t in task_sequence:
        if t.get("type") == "pre":
            task_lines.append(f'            Task(agent=agents["{t["agent"]}"], description="Predefined: {t["name"]}", expected_output="")')
        elif t.get("type") == "custom":
            desc = t.get("description", "")
            expected = t.get("expected_output", desc)
            task_lines.append(
                f'            Task(agent=agents["{t["agent"]}"], description={repr(desc)}, expected_output={repr(expected)})'
            )
    tasks_block = ",\n".join(task_lines)
    code += (
        f"def create_workflow(topic, agents, tools, config):\n"
        f"    return {{\n"
        f"        \"name\": \"{name}\",\n"
        f"        \"description\": \"{description}\",\n"
        f"        \"agents\": {{\n{agents_block}\n        }},\n"
        f"        \"tasks\": [\n{tasks_block}\n        ],\n"
        f"        \"tools\": tools\n"
        f"    }}\n\n"
        f"get_workflow = create_workflow\n"
    )
    return code

def save_workflow_file(profile: str, workflow_name: str, code: str) -> None:
    """Salva il file .py in workflows/."""
    workflows_dir = Path(__file__).parent.parent / "workflows"
    workflows_dir.mkdir(exist_ok=True)
    normalized_name = workflow_name.strip().lower().replace(" ", "_")
    file_path = workflows_dir / f"workflow_{normalized_name}.py"
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(code)
