
"""
Patch completo per bypassare tutti i problemi di ChromaDB in ambiente Replit/Nix.
Questo modulo deve essere importato PRIMA di qualsiasi altro modulo per essere efficace.
"""
import sys
import importlib.util
from unittest.mock import MagicMock


def create_comprehensive_chromadb_patch():
    """Patch completo e definitivo per ChromaDB e tutte le sue dipendenze."""
    
    # 1. Mock delle dipendenze problematiche
    mock_onnxruntime = MagicMock()
    mock_onnxruntime.__version__ = "1.20.1"
    sys.modules['onnxruntime'] = mock_onnxruntime
    sys.modules['onnxruntime.capi'] = MagicMock()
    sys.modules['onnxruntime.capi._pybind_state'] = MagicMock()
    
    mock_tokenizers = MagicMock()
    mock_tokenizers.__version__ = "0.20.3"
    
    class MockTokenizer:
        def __init__(self, *args, **kwargs):
            pass
        def encode(self, text):
            tokens = text.split()
            return MockEncoding(tokens)
        @classmethod
        def from_pretrained(cls, *args, **kwargs):
            return cls()
    
    class MockEncoding:
        def __init__(self, tokens):
            self.tokens = tokens
            self.ids = list(range(len(tokens)))
    
    mock_tokenizers.Tokenizer = MockTokenizer
    sys.modules['tokenizers'] = mock_tokenizers
    
    # 2. Mock sentence-transformers
    mock_sentence_transformers = MagicMock()
    sys.modules['sentence_transformers'] = mock_sentence_transformers
    
    # 3. Crea tutti i moduli ChromaDB con implementazioni specifiche
    
    # API Models - Questo è ciò che sta causando l'errore attuale
    mock_api_models = MagicMock()
    
    class MockCollection:
        def __init__(self, name="mock_collection", metadata=None, **kwargs):
            self.name = name
            self.metadata = metadata or {}
            self.id = f"collection_{name}"
        
        def add(self, documents=None, embeddings=None, metadatas=None, ids=None, **kwargs):
            return {"status": "success", "added": len(documents or [])}
        
        def query(self, query_texts=None, query_embeddings=None, n_results=10, **kwargs):
            return {
                "ids": [["mock_id_1", "mock_id_2"]],
                "documents": [["Mock document 1", "Mock document 2"]],
                "metadatas": [[{}, {}]],
                "distances": [[0.1, 0.2]]
            }
        
        def get(self, ids=None, where=None, limit=None, offset=None, **kwargs):
            return {
                "ids": ["mock_id_1"],
                "documents": ["Mock document"],
                "metadatas": [{}]
            }
        
        def update(self, ids, embeddings=None, metadatas=None, documents=None, **kwargs):
            return {"status": "success"}
        
        def upsert(self, ids, embeddings=None, metadatas=None, documents=None, **kwargs):
            return {"status": "success"}
        
        def delete(self, ids=None, where=None, **kwargs):
            return {"status": "success"}
        
        def count(self):
            return 42
    
    mock_api_models.Collection = MockCollection
    sys.modules['chromadb.api.models'] = mock_api_models
    
    # API Types
    mock_api_types = MagicMock()
    mock_api_types.Collection = MockCollection
    mock_api_types.Document = str
    mock_api_types.Embedding = list
    mock_api_types.Metadata = dict
    mock_api_types.QueryResult = dict
    mock_api_types.GetResult = dict
    mock_api_types.CollectionMetadata = dict
    sys.modules['chromadb.api.types'] = mock_api_types
    
    # Client API
    mock_client = MagicMock()
    
    class MockClient:
        def __init__(self, *args, **kwargs):
            self._collections = {}
        
        def create_collection(self, name, metadata=None, embedding_function=None, **kwargs):
            collection = MockCollection(name, metadata)
            self._collections[name] = collection
            return collection
        
        def get_collection(self, name, embedding_function=None, **kwargs):
            if name in self._collections:
                return self._collections[name]
            return MockCollection(name)
        
        def get_or_create_collection(self, name, metadata=None, embedding_function=None, **kwargs):
            if name not in self._collections:
                self._collections[name] = MockCollection(name, metadata)
            return self._collections[name]
        
        def list_collections(self):
            return list(self._collections.values())
        
        def delete_collection(self, name):
            if name in self._collections:
                del self._collections[name]
            return {"status": "success"}
        
        def reset(self):
            self._collections.clear()
            return {"status": "success"}
    
    mock_client.Client = MockClient
    sys.modules['chromadb.api.client'] = mock_client
    
    # Embedding Functions
    mock_embedding_functions = MagicMock()
    
    class MockEmbeddingFunction:
        def __call__(self, input_texts):
            if isinstance(input_texts, str):
                input_texts = [input_texts]
            return [[0.1] * 384 for _ in input_texts]
    
    class MockONNXMiniLM_L6_V2:
        def __init__(self, *args, **kwargs):
            pass
        def __call__(self, input_texts):
            if isinstance(input_texts, str):
                input_texts = [input_texts]
            return [[0.1] * 384 for _ in input_texts]
    
    mock_embedding_functions.DefaultEmbeddingFunction = lambda: MockEmbeddingFunction()
    mock_embedding_functions.ONNXMiniLM_L6_V2 = MockONNXMiniLM_L6_V2
    mock_embedding_functions.SentenceTransformerEmbeddingFunction = MockEmbeddingFunction
    sys.modules['chromadb.utils.embedding_functions'] = mock_embedding_functions
    
    # Tutti gli altri moduli ChromaDB con mock generici
    chromadb_modules = [
        'chromadb.utils',
        'chromadb.utils.data_loaders',
        'chromadb.config',
        'chromadb.api',
        'chromadb.api.segment',
        'chromadb.db',
        'chromadb.db.base',
        'chromadb.db.clickhouse', 
        'chromadb.db.duckdb',
        'chromadb.segment',
        'chromadb.segment.impl',
        'chromadb.types',
        'chromadb.telemetry',
        'chromadb.auth',
        'chromadb.auth.providers',
        'chromadb.auth.token',
        'chromadb.auth.basic',
        'chromadb.auth.registry',
        'chromadb.ingest',
        'chromadb.ingest.impl',
        'chromadb.server',
        'chromadb.server.fastapi'
    ]
    
    for module_name in chromadb_modules:
        if module_name not in sys.modules:
            sys.modules[module_name] = MagicMock()
    
    # Utils specifici
    mock_utils = MagicMock()
    mock_utils.get_uuid = lambda: "mock-uuid-12345"
    mock_utils.validate_metadata = lambda x: x
    mock_utils.validate_ids = lambda x: x
    mock_utils.validate_embeddings = lambda x: x
    sys.modules['chromadb.utils'] = mock_utils
    
    # Auth modules specifici
    mock_auth_token = MagicMock()
    mock_auth_token.TokenAuthProvider = MagicMock
    mock_auth_token.TokenCredentials = MagicMock
    mock_auth_token.create_token = lambda: "mock-token-12345"
    sys.modules['chromadb.auth.token'] = mock_auth_token
    
    mock_auth_basic = MagicMock()
    mock_auth_basic.BasicAuthProvider = MagicMock
    mock_auth_basic.BasicCredentials = MagicMock
    sys.modules['chromadb.auth.basic'] = mock_auth_basic
    
    mock_auth_registry = MagicMock()
    mock_auth_registry.AuthRegistry = MagicMock
    sys.modules['chromadb.auth.registry'] = mock_auth_registry
    
    # Errors module - Questo è il modulo mancante!
    mock_errors = MagicMock()
    
    class MockChromaError(Exception):
        """Mock ChromaDB base error"""
        pass
    
    class MockInvalidCollectionException(MockChromaError):
        """Mock invalid collection error"""
        pass
    
    class MockDuplicateIDError(MockChromaError):
        """Mock duplicate ID error"""
        pass
    
    class MockEmbeddingFunctionNotImplementedError(MockChromaError):
        """Mock embedding function error"""
        pass
    
    mock_errors.ChromaError = MockChromaError
    mock_errors.InvalidCollectionException = MockInvalidCollectionException
    mock_errors.DuplicateIDError = MockDuplicateIDError
    mock_errors.EmbeddingFunctionNotImplementedError = MockEmbeddingFunctionNotImplementedError
    mock_errors.NoIndexException = MockChromaError
    mock_errors.InvalidDimensionException = MockChromaError
    mock_errors.AuthenticationError = MockChromaError
    mock_errors.AuthorizationError = MockChromaError
    
    sys.modules['chromadb.errors'] = mock_errors
    
    # ChromaDB principale
    mock_chromadb = MagicMock()
    mock_chromadb.__version__ = "0.4.0"
    mock_chromadb.Client = MockClient
    mock_chromadb.PersistentClient = MockClient
    mock_chromadb.EphemeralClient = MockClient
    mock_chromadb.utils = mock_utils
    mock_chromadb.errors = mock_errors
    
    # Funzioni principali di ChromaDB
    def mock_client_factory(*args, **kwargs):
        return MockClient(*args, **kwargs)
    
    mock_chromadb.client = mock_client_factory
    mock_chromadb.PersistentClient = mock_client_factory
    mock_chromadb.EphemeralClient = mock_client_factory
    
    sys.modules['chromadb'] = mock_chromadb
    
    print("✅ Patch completo ChromaDB applicato con successo!")


# Applica immediatamente il patch quando il modulo viene importato
create_comprehensive_chromadb_patch()
