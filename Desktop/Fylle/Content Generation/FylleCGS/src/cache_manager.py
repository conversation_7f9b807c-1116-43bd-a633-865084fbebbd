
import os
import hashlib
import logging
from pathlib import Path
from typing import Dict, Optional, <PERSON><PERSON>
from functools import lru_cache
from .config import RAG_DIR

logger = logging.getLogger(__name__)

class RAGCacheManager:
    """
    Gestisce cache intelligente per contenuti RAG.
    Fase 1: Cache read-only per ridurre I/O su file system.
    """
    
    def __init__(self):
        self.file_content_cache: Dict[str, Tuple[str, float]] = {}  # path -> (content, mtime)
        self.processed_cache: Dict[str, Tuple[str, str]] = {}  # cache_key -> (content, file_hash)
    
    def _get_file_hash(self, file_paths: list) -> str:
        """Genera hash basato su file paths e modification times."""
        hash_input = ""
        for path in sorted(file_paths):
            try:
                mtime = os.path.getmtime(path)
                hash_input += f"{path}:{mtime}:"
            except OSError:
                hash_input += f"{path}:missing:"
        return hashlib.md5(hash_input.encode()).hexdigest()
    
    def _read_file_cached(self, file_path: str) -> str:
        """Legge file con cache basata su modification time."""
        try:
            current_mtime = os.path.getmtime(file_path)
            
            if file_path in self.file_content_cache:
                cached_content, cached_mtime = self.file_content_cache[file_path]
                if current_mtime == cached_mtime:
                    logger.debug(f"Cache HIT for file: {file_path}")
                    return cached_content
            
            # Cache miss - leggi file
            logger.debug(f"Cache MISS for file: {file_path}")
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            self.file_content_cache[file_path] = (content, current_mtime)
            return content
            
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return ""
    
    def get_client_content(self, client_name: str, document_name: str = None, 
                          force_refresh: bool = False) -> str:
        """
        Recupera contenuto client con cache intelligente.
        Mantiene compatibilità completa con read_rag_content originale.
        """
        if not client_name:
            return "No client specified for RAG content retrieval"
        
        client_dir = Path(RAG_DIR) / client_name
        if not client_dir.exists():
            return f"Client directory '{client_name}' not found"
        
        # Genera cache key
        cache_key = f"{client_name}:{document_name or 'all'}"
        
        # Trova tutti i file .md per il client
        md_files = list(client_dir.glob("*.md"))
        if not md_files:
            return f"No markdown documents found for client '{client_name}'"
        
        # Calcola hash dei file per invalidazione cache
        current_hash = self._get_file_hash([str(f) for f in md_files])
        
        # Controlla cache processata
        if not force_refresh and cache_key in self.processed_cache:
            cached_content, cached_hash = self.processed_cache[cache_key]
            if current_hash == cached_hash:
                logger.debug(f"Processed cache HIT for client: {client_name}")
                return cached_content
        
        logger.debug(f"Processed cache MISS for client: {client_name}")
        
        # Fallback alla logica originale ma con file cache
        if document_name:
            # Richiesta documento specifico
            return self._get_specific_document(client_name, document_name, client_dir)
        else:
            # Richiesta tutti i documenti (comportamento originale)
            result = self._get_all_documents_formatted(client_name, md_files, current_hash, cache_key)
            return result
    
    def _get_specific_document(self, client_name: str, document_name: str, client_dir: Path) -> str:
        """Gestisce richiesta documento specifico (con fuzzy matching)."""
        # Import qui per evitare circular imports
        import difflib
        
        available_docs = [f.name for f in client_dir.glob('*.md')]
        
        if document_name not in available_docs:
            best_match = difflib.get_close_matches(document_name, available_docs, n=1, cutoff=0.6)
            if best_match:
                doc_path = client_dir / best_match[0]
                content = self._read_file_cached(str(doc_path))
                return f"[FUZZY MATCH] Document '{document_name}' not found. Showing closest match: '{best_match[0]}'\n\n{content}"
            else:
                return f"Document '{document_name}' not found for client '{client_name}'. Documenti disponibili: {available_docs}"
        
        # Documento trovato
        if not Path(document_name).suffix:
            document_name = f"{document_name}.md"
        doc_path = client_dir / document_name
        
        if not doc_path.exists():
            return f"Document '{document_name}' not found for client '{client_name}'"
        
        return self._read_file_cached(str(doc_path))
    
    def _get_all_documents_formatted(self, client_name: str, md_files: list, 
                                   current_hash: str, cache_key: str) -> str:
        """Formatta tutti i documenti con categorizzazione (logica originale)."""
        company_info = []
        guidelines = []
        knowledge_base = []
        other_docs = []
        
        for doc_path in md_files:
            doc_name = doc_path.name.lower()
            content = self._read_file_cached(str(doc_path))
            
            if any(term in doc_name for term in ["company", "about", "profile", "overview", "brand"]):
                company_info.append((doc_name, content))
            elif any(term in doc_name for term in ["guideline", "guide", "best_practice", "best-practice", "rule", "instruction"]):
                guidelines.append((doc_name, content))
            elif any(term in doc_name for term in ["knowledge", "kb", "reference", "detail", "info"]):
                knowledge_base.append((doc_name, content))
            else:
                other_docs.append((doc_name, content))
        
        # Formatta output (logica identica a read_rag_content originale)
        formatted_output = []
        
        if company_info:
            formatted_output.append("## COMPANY INFORMATION\n\n    The following documents contain essential information about the company, its brand, and positioning.\n    This information should be reflected in all content creation.\n    ")
            for doc_name, content in company_info:
                formatted_output.append(f"### {doc_name}\n\n{content}\n\n")
        
        if guidelines:
            formatted_output.append("\n## CONTENT GUIDELINES\n\n    The following documents contain guidelines and best practices for content creation.\n    These should be strictly followed when generating content.\n    ")
            for doc_name, content in guidelines:
                formatted_output.append(f"### {doc_name}\n\n{content}\n\n")
        
        if knowledge_base:
            formatted_output.append("\n## KNOWLEDGE BASE\n\n    The following documents contain detailed knowledge that can be referenced and incorporated into content.\n    Use this information as needed to enhance content accuracy and depth.\n    ")
            for doc_name, content in knowledge_base:
                formatted_output.append(f"### {doc_name}\n\n{content}\n\n")
        
        if other_docs:
            formatted_output.append("\n## OTHER DOCUMENTS\n\n    The following documents contain additional information that may be relevant to content creation.\n    ")
            for doc_name, content in other_docs:
                formatted_output.append(f"### {doc_name}\n\n{content}\n\n")
        
        if not formatted_output:
            return f"No markdown documents found for client '{client_name}'"
        
        result = "\n\n".join(formatted_output)
        
        # Salva in cache processata
        self.processed_cache[cache_key] = (result, current_hash)
        
        return result
    
    def clear_cache(self, client_name: str = None):
        """Pulisce cache per client specifico o tutta la cache."""
        if client_name:
            # Rimuovi solo cache per questo client
            keys_to_remove = [k for k in self.processed_cache.keys() if k.startswith(f"{client_name}:")]
            for key in keys_to_remove:
                del self.processed_cache[key]
            logger.info(f"Cleared cache for client: {client_name}")
        else:
            # Pulisci tutta la cache
            self.file_content_cache.clear()
            self.processed_cache.clear()
            logger.info("Cleared all RAG cache")

# Singleton instance
_cache_manager = None

def get_cache_manager() -> RAGCacheManager:
    """Restituisce singleton del cache manager."""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = RAGCacheManager()
    return _cache_manager
