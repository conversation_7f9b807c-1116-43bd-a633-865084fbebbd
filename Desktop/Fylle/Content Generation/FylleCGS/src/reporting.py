import streamlit as st
from typing import Dict

st.cache_data(show_spinner=False)
def get_openai_models_status(api_key: str, model_list: list) -> Dict[str, str]:
    """Check availability of OpenAI models for the given API key."""
    import openai
    openai.api_key = api_key
    try:
        models = openai.Model.list()
        available = {m['id'] for m in models['data']}
        return {model: ("active" if model in available else "inactive") for model in model_list}
    except Exception as e:
        return {model: "error" for model in model_list}

# Anthropic and Deepseek: Placeholder implementations (to be extended)
def get_anthropic_models_status(api_key: str, model_list: list) -> Dict[str, str]:
    # TODO: Implement real check if API allows, else always 'unknown'
    return {model: "unknown" for model in model_list}

def get_deepseek_models_status(api_key: str, model_list: list) -> Dict[str, str]:
    # TODO: Implement real check if API allows, else always 'unknown'
    return {model: "unknown" for model in model_list}

# Utility for reporting tab
def reporting_tab(model_options=None, openai_key=None, anthropic_key=None, deepseek_key=None):
    # Import model configuration if not provided
    if model_options is None:
        from .config import LLM_MODELS
        model_options = {
            provider: models_config.get("options", []) 
            for provider, models_config in LLM_MODELS.items()
        }
    st.title("System Reporting: Providers & Models")
    st.markdown("Visualizza lo stato dei provider e dei modelli disponibili (attivo, inattivo, errore, sconosciuto).")

    if not openai_key and not anthropic_key and not deepseek_key:
        st.warning("Nessuna API key trovata nelle variabili d'ambiente. Impossibile verificare lo stato dei modelli.")
        st.info("Imposta le variabili d'ambiente OPENAI_API_KEY, ANTHROPIC_API_KEY, DEEPSEEK_API_KEY per vedere lo stato effettivo.")
        return
    
    provider_status_funcs = {
        "openai": lambda: get_openai_models_status(openai_key, model_options["openai"]) if openai_key else {m: "no key" for m in model_options["openai"]},
        "anthropic": lambda: get_anthropic_models_status(anthropic_key, model_options["anthropic"]) if anthropic_key else {m: "no key" for m in model_options["anthropic"]},
        "deepseek": lambda: get_deepseek_models_status(deepseek_key, model_options["deepseek"]) if deepseek_key else {m: "no key" for m in model_options["deepseek"]},
    }

    for provider, models in model_options.items():
        st.markdown(f"#### {provider.capitalize()}")
        model_status = provider_status_funcs[provider]()
        cols = st.columns(len(models))
        for idx, model in enumerate(models):
            color = {
                "active": "#4CAF50",
                "inactive": "#F44336",
                "error": "#FFC107",
                "unknown": "#BDBDBD",
                "no key": "#90A4AE"
            }.get(model_status[model], "#BDBDBD")
            with cols[idx]:
                st.markdown(f"""
                    <div style='border:1px solid #ddd; border-radius:8px; padding:10px; margin-bottom:8px; background:{color}22;'>
                        <b>{model}</b><br>
                        <span style='color:{color}'>{model_status[model]}</span>
                    </div>
                """, unsafe_allow_html=True)
