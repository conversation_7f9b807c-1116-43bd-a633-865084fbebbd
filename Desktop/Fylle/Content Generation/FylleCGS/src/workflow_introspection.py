import importlib
import os
import sys
from pathlib import Path
from typing import Dict, Any
from src.agents import AgentsFactory
from src.tools import get_available_tools
from src.tasks import get_available_tasks

def extract_workflow_info(workflows_dir: str) -> Dict[str, Any]:
    """
    Scansiona la cartella dei workflow, importa ogni modulo e ne estrae le informazioni strutturate.
    Restituisce un dict: {nome_workflow: {info}}
    """
    sys.path.insert(0, str(Path(workflows_dir).parent))  # Assicura import dinamico
    workflow_infos = {}
    for fname in os.listdir(workflows_dir):
        if fname.startswith("workflow_") and fname.endswith(".py"):
            module_name = f"workflows.{fname[:-3]}"
            try:
                module = importlib.import_module(module_name)
                # Trova la funzione di creazione
                create_fn = None
                for fn_name in ["create_workflow", "get_workflow"]:
                    if hasattr(module, fn_name):
                        create_fn = getattr(module, fn_name)
                        break
                if not create_fn:
                    continue
                # Prepara agent/tool/task dummy
                config = {"client_name": "Demo", "context": "", "target_audience": "Demo audience"}
                tools = get_available_tools("Demo")
                agents = AgentsFactory(config).create_agents(tools)
                tasks = get_available_tasks(agents)
                # Parametri variabili: tentiamo le firme più comuni
                try:
                    workflow = create_fn("Demo topic", agents, tasks, tools, config)
                except TypeError:
                    try:
                        workflow = create_fn("Demo topic", agents, tasks, tools)
                    except TypeError:
                        try:
                            workflow = create_fn("Demo topic", agents, tasks)
                        except Exception:
                            workflow = None
                if not workflow:
                    continue
                # Estrai info principali
                name = workflow.get("name") or fname.replace("workflow_", "").replace(".py", "").replace("_", " ").title()
                description = workflow.get("description", "")
                agents_info = {}
                for k, agent in workflow.get("agents", {}).items():
                    agents_info[k] = {
                        "role": getattr(agent, "role", str(agent)),
                        "goal": getattr(agent, "goal", ""),
                        "backstory": getattr(agent, "backstory", "")
                    }
                tasks_info = []
                for t in workflow.get("tasks", []):
                    tasks_info.append({
                        "description": getattr(t, "description", str(t)),
                        "expected_output": getattr(t, "expected_output", ""),
                        "agent": getattr(t, "agent", None),
                        "tool": getattr(t, "tool", None)
                    })
                tools_info = {}
                for k, tool in workflow.get("tools", {}).items():
                    tools_info[k] = {
                        "name": getattr(tool, "name", str(tool)),
                        "description": getattr(tool, "description", "")
                    }
                workflow_infos[name] = {
                    "description": description,
                    "agents": agents_info,
                    "tasks": tasks_info,
                    "tools": tools_info,
                    "raw": workflow
                }
            except Exception as e:
                workflow_infos[fname] = {"error": str(e)}
    return workflow_infos
