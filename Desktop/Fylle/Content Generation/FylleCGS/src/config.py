import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
dotenv_path = Path(os.path.dirname(os.path.dirname(__file__))) / ".env"
if dotenv_path.exists():
    load_dotenv(dotenv_path=str(dotenv_path))
    logging.info(f"Loaded environment variables from {dotenv_path}")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# API Keys - Read from environment variables (Replit Secrets)
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
SERPER_API_KEY = os.getenv("SERPER_API_KEY")

# Debug: Log which keys are found (without revealing the actual keys)
logger.info(f"API Key Status:")
logger.info(f"OPENAI_API_KEY: {'✓ Found' if OPENAI_API_KEY else '✗ Not found'}")
logger.info(f"ANTHROPIC_API_KEY: {'✓ Found' if ANTHROPIC_API_KEY else '✗ Not found'}")
logger.info(f"DEEPSEEK_API_KEY: {'✓ Found' if DEEPSEEK_API_KEY else '✗ Not found'}")
logger.info(f"SERPER_API_KEY: {'✓ Found' if SERPER_API_KEY else '✗ Not found'}")

# LLM Models Configuration
LLM_MODELS = {
    "openai": {
        "default": "gpt-4o",
        "options": [
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4-turbo",
            "gpt-4o",
            "gpt-4o-mini",
            "gpt-4.1",
            "gpt-4.1-mini",
            "gpt-4.1-nano",
            "gpt-4.5",
            "o1",
            "o1-mini",
            "o1-pro",
            "o3-mini"
        ],
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 1000,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0
        }
    },
    "anthropic": {
        "default": "claude-3-5-sonnet-latest",
        "options": [
            "claude-3-sonnet-20240229",
            "claude-3-opus-20240229", 
            "claude-3-haiku-20240307",
            "claude-3-5-sonnet-latest",
            "claude-3-5-haiku-latest",
            "claude-3-opus-latest",
            "claude-3-7-sonnet-latest",
            "claude-sonnet-4-0",
            "claude-opus-4-0",
            "claude-opus-4-20250514",
            "claude-sonnet-4-20250514",
            "claude-3-7-sonnet-20250219",
            "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022"
        ],
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 1000,
            "top_p": 1.0
        }
    },
    "deepseek": {
        "default": "deepseek-chat",
        "options": [
            "deepseek-chat",
            "deepseek-coder"
        ],
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 1000,
            "top_p": 1.0
        }
    }
}

# Parametri supportati per ogni provider
PROVIDER_SPECIFIC_PARAMS = {
    "openai": ["temperature", "max_tokens", "top_p", "frequency_penalty", "presence_penalty", "stop"],
    "anthropic": ["temperature", "max_tokens", "top_p", "stop_sequences"],
    "deepseek": ["temperature", "max_tokens", "top_p"]
}

# Limiti token per modello
MODEL_TOKEN_LIMITS = {
    "gpt-3.5-turbo": {"max_context": 16385, "max_output": 4096},
    "gpt-4": {"max_context": 8192, "max_output": 4096},
    "gpt-4-turbo": {"max_context": 128000, "max_output": 4096},
    "gpt-4o": {"max_context": 128000, "max_output": 16384},
    "gpt-4o-mini": {"max_context": 128000, "max_output": 16384},
    "gpt-4.1": {"max_context": 1000000, "max_output": 32768},
    "gpt-4.1-mini": {"max_context": 1000000, "max_output": 32768},
    "gpt-4.1-nano": {"max_context": 1000000, "max_output": 32768},
    "gpt-4.5": {"max_context": 128000, "max_output": 16384},
    "o1": {"max_context": 200000, "max_output": 100000},
    "o1-mini": {"max_context": 128000, "max_output": 65000},
    "o1-pro": {"max_context": 200000, "max_output": 100000},
    "o3-mini": {"max_context": 200000, "max_output": 100000},
    "claude-3-haiku-20240307": {"max_context": 200000, "max_output": 4096},
    "claude-3-sonnet-20240229": {"max_context": 200000, "max_output": 4096},
    "claude-3-opus-20240229": {"max_context": 200000, "max_output": 4096},
    "claude-3-5-sonnet-latest": {"max_context": 200000, "max_output": 8192},
    "claude-3-5-haiku-latest": {"max_context": 200000, "max_output": 8192},
    "claude-3-opus-latest": {"max_context": 200000, "max_output": 4096},
    "claude-3-7-sonnet-latest": {"max_context": 200000, "max_output": 8192},
    "claude-sonnet-4-0": {"max_context": 200000, "max_output": 8192},
    "claude-opus-4-0": {"max_context": 200000, "max_output": 8192},
    "claude-opus-4-20250514": {"max_context": 200000, "max_output": 8192},
    "claude-sonnet-4-20250514": {"max_context": 200000, "max_output": 8192},
    "claude-3-7-sonnet-20250219": {"max_context": 200000, "max_output": 8192},
    "claude-3-5-sonnet-20241022": {"max_context": 200000, "max_output": 8192},
    "claude-3-5-haiku-20241022": {"max_context": 200000, "max_output": 8192},
    "deepseek-chat": {"max_context": 32000, "max_output": 4096},
    "deepseek-coder": {"max_context": 16000, "max_output": 4096}
}



# Default model settings
DEFAULT_PROVIDER = "openai"
DEFAULT_MODEL = LLM_MODELS[DEFAULT_PROVIDER]["default"]

# Workflow settings
WORKFLOW_TIMEOUT = 300  # seconds
MAX_RETRIES = 3

# Output settings
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "output")

# RAG settings
RAG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "rag")


def validate_api_keys() -> Dict[str, bool]:
    """
    Validate the presence of API keys for different providers.

    Returns:
        Dict[str, bool]: Dictionary with provider names as keys and boolean values
                         indicating whether the API key is available.
    """
    return {
        "openai": bool(OPENAI_API_KEY),
        "anthropic": bool(ANTHROPIC_API_KEY),
        "deepseek": bool(DEEPSEEK_API_KEY)
    }


def get_available_providers() -> list:
    """
    Get a list of available providers based on API key availability.

    Returns:
        list: List of provider names that have valid API keys.
    """
    keys_status = validate_api_keys()
    return [provider for provider, available in keys_status.items() if available]



def get_model_parameters(provider: str, model: Optional[str] = None) -> Dict[str, Any]:
    """
    Get the parameters for a specific model.

    Args:
        provider (str): The provider name (e.g., "openai", "anthropic").
        model (Optional[str]): The model name. If None, the default model for the provider is used.

    Returns:
        Dict[str, Any]: Dictionary containing model parameters.
    """
    if provider not in LLM_MODELS:
        raise ValueError(f"Provider '{provider}' not supported.")

    if not model:
        model = LLM_MODELS[provider]["default"]
    elif model not in LLM_MODELS[provider]["options"]:
        logger.warning(f"Model '{model}' not in known options for {provider}. Using anyway.")

    return LLM_MODELS[provider]["parameters"].copy()


def get_filtered_parameters(provider: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Filter parameters to only include those supported by the provider.
    
    Args:
        provider (str): The provider name (e.g., "openai", "anthropic", "deepseek").
        params (Dict[str, Any]): Dictionary of parameters to filter.
        
    Returns:
        Dict[str, Any]: Filtered parameters containing only those supported by the provider.
    """
    if provider not in PROVIDER_SPECIFIC_PARAMS:
        logger.warning(f"Provider '{provider}' not found in PROVIDER_SPECIFIC_PARAMS. Returning all parameters.")
        return params.copy()
    
    supported_params = PROVIDER_SPECIFIC_PARAMS[provider]
    filtered = {k: v for k, v in params.items() if k in supported_params}
    
    # Log any filtered out parameters
    removed_params = set(params.keys()) - set(filtered.keys())
    if removed_params:
        logger.debug(f"Filtered out unsupported parameters for {provider}: {removed_params}")
    
    return filtered


def estimate_cost(provider: str, model: str, estimated_tokens: int = 2000) -> float:
    """
    Estimate the cost for a given provider, model, and token count.
    
    Args:
        provider (str): The provider name (e.g., "openai", "anthropic", "deepseek").
        model (str): The model name.
        estimated_tokens (int): Estimated number of tokens for the request.
        
    Returns:
        float: Estimated cost in USD.
    """
    # Cost per 1M tokens (input pricing)
    cost_per_million_tokens = {
        "openai": {
            "gpt-3.5-turbo": 0.50,
            "gpt-4": 30.00,
            "gpt-4-turbo": 10.00,
            "gpt-4o": 2.50,
            "gpt-4o-mini": 0.15,
            "gpt-4.1": 2.00,
            "gpt-4.1-mini": 0.40,
            "gpt-4.1-nano": 0.10,
            "gpt-4.5": 2.50,
            "o1": 15.00,
            "o1-mini": 3.00,
            "o1-pro": 60.00,
            "o3-mini": 3.00
        },
        "anthropic": {
            "claude-3-haiku-20240307": 0.25,
            "claude-3-sonnet-20240229": 3.00,
            "claude-3-opus-20240229": 15.00,
            "claude-3-5-sonnet-latest": 3.00,
            "claude-3-5-haiku-latest": 0.80,
            "claude-3-opus-latest": 15.00,
            "claude-3-7-sonnet-latest": 3.00,
            "claude-sonnet-4-0": 3.00,
            "claude-opus-4-0": 15.00,
            "claude-opus-4-20250514": 15.00,
            "claude-sonnet-4-20250514": 3.00,
            "claude-3-7-sonnet-20250219": 3.00,
            "claude-3-5-sonnet-20241022": 3.00,
            "claude-3-5-haiku-20241022": 0.80
        },
        "deepseek": {
            "deepseek-chat": 0.14,
            "deepseek-coder": 0.14
        }
    }
    
    if provider not in cost_per_million_tokens:
        return 0.0
    
    if model not in cost_per_million_tokens[provider]:
        # Use default model pricing for the provider
        default_model = LLM_MODELS[provider]["default"]
        if default_model in cost_per_million_tokens[provider]:
            cost_per_million = cost_per_million_tokens[provider][default_model]
        else:
            return 0.0
    else:
        cost_per_million = cost_per_million_tokens[provider][model]
    
    # Calculate cost for estimated tokens
    estimated_cost = (estimated_tokens / 1_000_000) * cost_per_million
    return round(estimated_cost, 4)


def check_environment() -> None:
    """
    Check if the environment is properly set up.
    Logs warnings for missing API keys.
    """
    keys_status = validate_api_keys()

    for provider, available in keys_status.items():
        if not available:
            logger.warning(f"{provider.upper()}_API_KEY not found in environment variables")

    if not any(keys_status.values()):
        logger.error("No API keys found. At least one provider API key is required.")
        raise EnvironmentError("No API keys found. Please set at least one provider API key.")