# Adding premium_sources_analyzer agent to the factory to resolve the workflow error.
from typing import Dict, List, Optional, Any
import logging

from crewai import Agent
from langchain.tools.base import BaseTool
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from src.deepseek_api import DeepseekChatAPI
from src.agent_meta import load_profile_meta

from .config import (
    OPENAI_API_KEY, 
    ANTHROPIC_API_KEY, 
    DEEPSEEK_API_KEY,
    SERPER_API_KEY,
    DEFAULT_PROVIDER,
    DEFAULT_MODEL,
    get_model_parameters,
    LLM_MODELS, 
    get_filtered_parameters
)

logger = logging.getLogger(__name__)


class AgentsFactory:
    """
    Factory class for creating specialized agents for content generation.

    This factory creates various agents with specific roles and goals,
    each configured with appropriate LLM backends and tools.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None):
        """
        Initialize the AgentsFactory.

        Args:
            config (Optional[Dict[str, Any]]): Configuration dictionary for customizing agent creation.
            logger (Optional[logging.Logger]): Logger instance for logging.
        """
        self.config = config or {}
        self.logger = logger or logging.getLogger(__name__)

        # Default provider and model
        self.provider = self.config.get('provider', DEFAULT_PROVIDER)
        self.model_name = self.config.get('model_name', self.config.get('model', DEFAULT_MODEL))

        # Get model parameters from config
        self.model_params = get_model_parameters(self.provider, self.model_name)

        # Override parameters if specified in config
        if 'temperature' in self.config:
            self.model_params['temperature'] = self.config.get('temperature')
        if 'max_tokens' in self.config:
            self.model_params['max_tokens'] = self.config.get('max_tokens')

    def _get_llm(self, provider: Optional[str] = None, model: Optional[str] = None) -> Any:
        """
        Get the appropriate LLM instance based on provider and model
        """
        provider = provider or self.provider
        model = model or self.model_name

        # Use configured model parameters instead of defaults
        # This ensures UI-configured parameters (especially max_tokens) are respected
        all_params = self.model_params.copy()

        # Filter parameters for provider compatibility
        filtered_params = get_filtered_parameters(provider, all_params)

        if provider == "openai":
            return ChatOpenAI(
                model_name=model,
                api_key=self.config.get('openai_api_key', OPENAI_API_KEY),
                **filtered_params
            )
        elif provider == "anthropic":
            return ChatAnthropic(
                model=model,
                api_key=self.config.get('anthropic_api_key', ANTHROPIC_API_KEY),
                **filtered_params
            )
        elif provider == "deepseek":
            api_key = self.config.get('deepseek_api_key', DEEPSEEK_API_KEY)
            model_name = model or LLM_MODELS["deepseek"]["default"]
            # Use configured parameters instead of defaults
            temperature = self.model_params.get("temperature", 0.7)
            max_tokens = self.model_params.get("max_tokens", 1000)
            top_p = self.model_params.get("top_p", 1.0)
            deepseek = DeepseekChatAPI(api_key, model=model_name, temperature=temperature, max_tokens=max_tokens, top_p=top_p)
            # Wrap to mimic .invoke()
            class DeepseekLLMWrap:
                def __init__(self, deepseek):
                    self.deepseek = deepseek
                def invoke(self, prompt):
                    return self.deepseek.chat(prompt)
                def bind(self, **kwargs):
                    def bound_func(prompt):
                        return self.invoke(prompt)
                    return bound_func
            return DeepseekLLMWrap(deepseek)
        else:
            # Default to OpenAI if provider not supported
            self.logger.warning(f"Provider {provider} not directly supported. Falling back to OpenAI.")
            return ChatOpenAI(
                model_name=DEFAULT_MODEL,
                api_key=self.config.get('openai_api_key', OPENAI_API_KEY),
                **get_model_parameters(DEFAULT_PROVIDER, DEFAULT_MODEL)
            )

    def create_agents(self, tools: Optional[Dict[str, BaseTool]] = None) -> Dict[str, Agent]:
        """
        Create only the minimal set of specialized agents for content generation: web_searcher, copywriter, rag_specialist, premium_sources_analyzer.
        """
        tools = tools or {}
        agents = {}

        provider = self.provider
        model = self.model_name

        # Web Searcher Agent
        ws_tool = tools.get("web_search_tool") if "web_search_tool" in tools else None
        agents["web_searcher"] = self.create_web_searcher(provider=provider, model=model, tools=[ws_tool] if ws_tool else [])

        # Copywriter Agent
        agents["copywriter"] = self.create_copywriter(provider=provider, model=model, tools=[])

        # RAG Specialist Agent
        rag_tool = tools.get("rag_tool") if "rag_tool" in tools else None
        agents["rag_specialist"] = self.create_rag_specialist(provider=provider, model=model, tools=[rag_tool] if rag_tool else [])
        
        # Premium Sources Analyzer Agent
        premium_tool = tools.get("premium_financial_sources") if "premium_financial_sources" in tools else None
        agents["premium_sources_analyzer"] = self.create_premium_sources_analyzer(provider=provider, model=model, tools=[premium_tool] if premium_tool else [])
        
        return agents

    def create_agent(self, agent_type: str, profile_id: str = "default", provider: Optional[str] = None, model: Optional[str] = None, tools: Optional[List[BaseTool]] = None) -> Agent:
        """
        Crea un singolo agente del tipo specificato con provider e modello personalizzati.
        Args:
            agent_type (str): Tipo di agente da creare (es. 'web_searcher', 'architect').
            profile_id (str): ID del profilo da utilizzare per caricare i metadati dell'agente.
            provider (Optional[str]): Provider LLM da utilizzare (es. 'openai', 'anthropic').
            model (Optional[str]): Nome specifico del modello (es. 'gpt-4', 'claude-3-opus-20240229').
            tools (Optional[List[BaseTool]]): Lista di strumenti da assegnare all'agente.
        Returns:
            Agent: L'agente creato.
        Raises:
            ValueError: Se il tipo di agente non è supportato.
        """
        # Load profile metadata for the agent
        meta = load_profile_meta(profile_id)[agent_type]
        system_message = meta.get("system_message", "")
        backstory = meta.get("backstory", "")
        goal = meta.get("goal", "")
        examples = meta.get("examples", [])
        profile_tools = meta.get("tools", None)

        # tools: priorità a tools argomento, poi profile_tools, poi lista vuota
        if tools is not None:
            pass
        elif profile_tools is not None:
            tools = profile_tools
        else:
            tools = []

        llm = self._get_llm(provider, model)
        agent = Agent(
            role=system_message,
            goal=goal,
            backstory=backstory,
            llm=llm,
            tools=tools,
            memory=False
        )
        # Gli examples non vengono più attaccati direttamente all'oggetto Agent (non supportato da CrewAI)
        return agent

    # Factory methods specifici per tipo di agente
    def create_web_searcher(self, provider: Optional[str] = None, model: Optional[str] = None, 
                            tools: Optional[List[BaseTool]] = None) -> Agent:
        """Crea un agente Web Searcher con il provider e modello specificati."""
        return self.create_agent("web_searcher", provider=provider, model=model, tools=tools)

    def create_architect(self, provider: Optional[str] = None, model: Optional[str] = None, 
                        tools: Optional[List[BaseTool]] = None) -> Agent:
        """Crea un agente Architect con il provider e modello specificati."""
        return self.create_agent("architect", provider=provider, model=model, tools=tools)

    def create_section_writer(self, provider: Optional[str] = None, model: Optional[str] = None, 
                             tools: Optional[List[BaseTool]] = None) -> Agent:
        """Crea un agente Section Writer con il provider e modello specificati."""
        return self.create_agent("section_writer", provider=provider, model=model, tools=tools)

    def create_copywriter(self, provider: Optional[str] = None, model: Optional[str] = None, 
                          tools: Optional[List[BaseTool]] = None) -> Agent:
        """Crea un agente Copywriter con il provider e modello specificati."""
        return self.create_agent("copywriter", provider=provider, model=model, tools=tools)

    def create_editor(self, provider: Optional[str] = None, model: Optional[str] = None, 
                     tools: Optional[List[BaseTool]] = None) -> Agent:
        """Crea un agente Editor con il provider e modello specificati."""
        return self.create_agent("editor", provider=provider, model=model, tools=tools)

    def create_quality_reviewer(self, provider: Optional[str] = None, model: Optional[str] = None, 
                               tools: Optional[List[BaseTool]] = None) -> Agent:
        """Crea un agente Quality Reviewer con il provider e modello specificati."""
        return self.create_agent("quality_reviewer", provider=provider, model=model, tools=tools)

    def create_rag_specialist(self, provider: Optional[str] = None, model: Optional[str] = None, 
                             tools: Optional[List[BaseTool]] = None) -> Agent:
        """Crea un agente RAG Specialist con il provider e modello specificati."""
        return self.create_agent("rag_specialist", provider=provider, model=model, tools=tools)

    def create_premium_sources_analyzer(self, provider: Optional[str] = None, model: Optional[str] = None, 
                                       tools: Optional[List[BaseTool]] = None) -> Agent:
        """Crea un agente Premium Sources Analyzer con il provider e modello specificati."""
        return self.create_agent("premium_sources_analyzer", provider=provider, model=model, tools=tools)