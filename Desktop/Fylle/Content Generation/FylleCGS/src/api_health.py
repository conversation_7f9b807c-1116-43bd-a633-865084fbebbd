import os
from dotenv import load_dotenv
from src.config import LLM_MODELS, OPENAI_API_KEY, ANTHROPIC_API_KEY, DEEPSEEK_API_KEY

try:
    from langchain_openai import ChatOpenAI
except ImportError:
    ChatOpenAI = None
try:
    from langchain_anthropic import ChatAnthropic
except ImportError:
    ChatAnthropic = None

load_dotenv()

def check_openai_api(api_key: str) -> str:
    if not api_key or ChatOpenAI is None:
        return "Not Running"
    try:
        llm = ChatOpenAI(
            model_name=LLM_MODELS["openai"]["default"],
            api_key=api_key,
            **LLM_MODELS["openai"]["parameters"]
        )
        llm.invoke("ping")
        return "OK"
    except Exception:
        return "Not Running"

def check_anthropic_api(api_key: str) -> str:
    if not api_key or ChatAnthropic is None:
        return "Not Running"
    try:
        llm = ChatAnthropic(
            model=LLM_MODELS["anthropic"]["default"],
            api_key=api_key,
            **LLM_MODELS["anthropic"]["parameters"]
        )
        llm.invoke("ping")
        return "OK"
    except Exception:
        return "Not Running"

from src.deepseek_api import DeepseekChatAPI

def check_deepseek_api(api_key: str) -> str:
    if not api_key:
        return "Not Running"
    try:
        deepseek = DeepseekChatAPI(api_key)
        return deepseek.health_check()
    except Exception:
        return "Not Running"

def get_all_api_status() -> dict:
    openai_key = os.getenv("OPENAI_API_KEY", "")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY", "")
    deepseek_key = os.getenv("DEEPSEEK_API_KEY", "")
    return {
        "openai": check_openai_api(openai_key),
        "anthropic": check_anthropic_api(anthropic_key),
        "deepseek": check_deepseek_api(deepseek_key),
    }

if __name__ == "__main__":
    statuses = get_all_api_status()
    for provider, status in statuses.items():
        print(f"{provider}: {status}")
