import os
import datetime

def log_workflow_event(event_name, content, log_dir="logs"):
    """
    Logga un evento del workflow (prompt, output, ecc.) su file.
    Funziona per qualsiasi workflow e può essere importata ovunque.
    """
    os.makedirs(log_dir, exist_ok=True)
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{log_dir}/{event_name}_{timestamp}.log"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"[LOG] Evento '{event_name}' salvato in {filename}")
