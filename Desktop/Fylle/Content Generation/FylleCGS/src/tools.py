# tools.py
# Tutte le funzioni tool e la factory dei tools

import os
import json
import logging
from typing import Dict, Optional
from pathlib import Path
import requests
from langchain.tools import Tool
from langchain.tools.base import BaseTool
from .config import SERPER_API_KEY, RAG_DIR
from datetime import datetime  # Import datetime


logger = logging.getLogger(__name__)

def serper_search(query: str, num_results: int = 10, location: str = "us") -> str:
    """
    Perform a web search using the Serper API with optimized parameters.

    Args:
        query: Search query string
        num_results: Number of results to return (1-100)
        location: Location for localized results (us, it, etc.)
    """
    if not SERPER_API_KEY:
        logger.error("SERPER_API_KEY not found in environment variables")
        return json.dumps({"error": "API key not configured", "results": []})

    url = "https://google.serper.dev/search"
    payload = {
        "q": query,
        "num": min(num_results, 100),  # Limit to API maximum
        "gl": location,  # Geographic location
        "hl": "en",  # Language
        "type": "search"  # Search type
    }

    headers = {
        'X-API-KEY': SERPER_API_KEY,
        'Content-Type': 'application/json'
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=30)
        response.raise_for_status()

        result = response.json()

        # Format results for better readability
        formatted_results = {
            "searchParameters": result.get("searchParameters", {}),
            "organic": result.get("organic", []),
            "answerBox": result.get("answerBox"),
            "knowledgeGraph": result.get("knowledgeGraph"),
            "total_results": len(result.get("organic", []))
        }

        return json.dumps(formatted_results, indent=2)

    except requests.exceptions.Timeout:
        logger.error("Serper API request timed out")
        return json.dumps({"error": "Request timed out", "results": []})
    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP error in serper_search: {str(e)}")
        return json.dumps({"error": f"HTTP error: {str(e)}", "results": []})
    except Exception as e:
        logger.error(f"Unexpected error in serper_search: {str(e)}")
        return json.dumps({"error": str(e), "results": []})

def read_markdown(filepath: str, client_name: str = None, document_name: str = None) -> str:
    """
    Read and return content from a markdown file.
    """
    if client_name:
        return read_rag_content(client_name, document_name or (Path(filepath).name if filepath else None))
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading markdown file {filepath}: {str(e)}")
        return f"Error reading file: {str(e)}"

def write_markdown(filepath: str, content: str, client_name: str = None, document_name: str = None) -> str:
    """
    Write content to a markdown file.
    """
    if client_name:
        doc_name = document_name or (Path(filepath).name if filepath else "output.md")
        client_dir = Path(RAG_DIR) / client_name
        filepath = str(client_dir / doc_name)
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
        return f"Successfully wrote content to {filepath}"
    except Exception as e:
        logger.error(f"Error writing to markdown file {filepath}: {str(e)}")
        return f"Error writing to file: {str(e)}"

def get_available_rag_documents(client_name: str) -> list:
    """
    Restituisce la lista dei file markdown disponibili per il client nella directory RAG.
    """
    if not client_name:
        return []
    client_dir = Path(RAG_DIR) / client_name
    if not client_dir.exists():
        return []
    return [f.name for f in client_dir.glob('*.md') if f.is_file()]

def read_rag_content(client_name: str, document_name: str = None) -> str:
    """
    Read content from RAG directory for a specific client.
    Now uses cache manager for improved performance.
    """
    try:
        # Usa cache manager se disponibile
        from .cache_manager import get_cache_manager
        cache_manager = get_cache_manager()
        return cache_manager.get_client_content(client_name, document_name)
    except Exception as e:
        logger.warning(f"Cache manager failed, falling back to direct read: {e}")
        
        # Fallback alla logica originale
        if not client_name:
            return "No client specified for RAG content retrieval"
        available_docs = get_available_rag_documents(client_name)
        import difflib
        if document_name and document_name not in available_docs:
            best_match = difflib.get_close_matches(document_name, available_docs, n=1, cutoff=0.6)
            if best_match:
                doc_path = Path(RAG_DIR) / client_name / best_match[0]
                with open(str(doc_path), "r", encoding="utf-8") as f:
                    content = f.read()
                return f"[FUZZY MATCH] Document '{document_name}' not found. Showing closest match: '{best_match[0]}'\n\n{content}"
            else:
                return f"Document '{document_name}' not found for client '{client_name}'. Documenti disponibili: {available_docs}"
        client_dir = Path(RAG_DIR) / client_name
        if not client_dir.exists():
            return f"Client directory '{client_name}' not found"
        if document_name:
            if not Path(document_name).suffix:
                document_name = f"{document_name}.md"
            doc_path = client_dir / document_name
            if not doc_path.exists() or not doc_path.is_file():
                return f"Document '{document_name}' not found for client '{client_name}'"
            with open(str(doc_path), "r", encoding="utf-8") as f:
                return f.read()
        # Read and categorize all markdown files in the client directory
        company_info = []
        guidelines = []
        knowledge_base = []
        other_docs = []
        for doc_path in client_dir.glob("*.md"):
            doc_name = doc_path.name.lower()
            with open(str(doc_path), "r", encoding="utf-8") as f:
                content = f.read()
            if any(term in doc_name for term in ["company", "about", "profile", "overview", "brand"]):
                company_info.append((doc_name, content))
            elif any(term in doc_name for term in ["guideline", "guide", "best_practice", "best-practice", "rule", "instruction"]):
                guidelines.append((doc_name, content))
            elif any(term in doc_name for term in ["knowledge", "kb", "reference", "detail", "info"]):
                knowledge_base.append((doc_name, content))
            else:
                other_docs.append((doc_name, content))
        formatted_output = []
        if company_info:
            formatted_output.append("""## COMPANY INFORMATION\n\n    The following documents contain essential information about the company, its brand, and positioning.\n    This information should be reflected in all content creation.\n    """)
            for doc_name, content in company_info:
                formatted_output.append(f"""### {doc_name}\n\n{content}\n\n""")
        if guidelines:
            formatted_output.append("""\n## CONTENT GUIDELINES\n\n    The following documents contain guidelines and best practices for content creation.\n    These should be strictly followed when generating content.\n    """)
            for doc_name, content in guidelines:
                formatted_output.append(f"""### {doc_name}\n\n{content}\n\n""")
        if knowledge_base:
            formatted_output.append("""\n## KNOWLEDGE BASE\n\n    The following documents contain detailed knowledge that can be referenced and incorporated into content.\n    Use this information as needed to enhance content accuracy and depth.\n    """)
            for doc_name, content in knowledge_base:
                formatted_output.append(f"""### {doc_name}\n\n{content}\n\n""")
        if other_docs:
            formatted_output.append("""\n## OTHER DOCUMENTS\n\n    The following documents contain additional information that may be relevant to content creation.\n    """)
            for doc_name, content in other_docs:
                formatted_output.append(f"""### {doc_name}\n\n{content}\n\n""")
        if not formatted_output:
            return f"No markdown documents found for client '{client_name}'"
        return "\n\n".join(formatted_output)

def extract_page_content(url: str, max_chars: int = 3000) -> str:
    """
    Extract actual content from a webpage using requests and basic HTML parsing.

    Args:
        url: URL to scrape
        max_chars: Maximum characters to extract from content

    Returns:
        Cleaned text content from the page
    """
    try:
        import re
        from urllib.parse import urljoin, urlparse

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()

        # Simple HTML cleaning - remove tags and extract text
        content = response.text

        # Remove script and style elements
        content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
        content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)

        # Remove HTML tags
        content = re.sub(r'<[^>]+>', '', content)

        # Clean up whitespace and decode HTML entities
        content = re.sub(r'\s+', ' ', content).strip()
        content = content.replace('&nbsp;', ' ').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')

        # Truncate to max_chars
        if len(content) > max_chars:
            content = content[:max_chars] + "..."

        return content

    except Exception as e:
        logger.warning(f"Failed to extract content from {url}: {str(e)}")
        return ""

def extract_financial_facts(content: str, url: str) -> dict:
    """
    Extract concrete financial facts from content focusing on recent developments.

    Args:
        content: Text content to analyze
        url: Source URL for context

    Returns:
        Dictionary with extracted facts and data points
    """
    if not content:
        return {"facts": [], "data_points": [], "events": []}

    content_lower = content.lower()
    facts = []
    data_points = []
    events = []

    import re

    # Extract numerical data (percentages, billions, millions, etc.)
    number_patterns = [
        r'(\d+\.?\d*)\s*%',  # Percentages
        r'\$(\d+\.?\d*)\s*(billion|million|trillion)',  # Dollar amounts
        r'(\d+\.?\d*)\s*(billion|million|trillion)',  # Large numbers
        r'(\d{4})\s*(q[1-4]|quarter)',  # Quarterly data
        r'(\d+\.?\d*)\s*basis\s*points?',  # Basis points
    ]

    for pattern in number_patterns:
        matches = re.findall(pattern, content_lower)
        for match in matches:
            if isinstance(match, tuple):
                data_points.append(" ".join(str(m) for m in match if m))
            else:
                data_points.append(str(match))

    # Extract specific financial events/facts
    fact_patterns = [
        r'(acquisition|merger|buyout|purchase|acquired|bought)\s+[^.]{0,100}',
        r'(ipo|initial public offering|went public|listing)\s+[^.]{0,100}',
        r'(earnings|revenue|profit|loss)\s+[^.]{0,100}',
        r'(rate cut|rate hike|interest rate|fed|federal reserve)\s+[^.]{0,100}',
        r'(inflation|cpi|consumer price index)\s+[^.]{0,100}',
        r'(unemployment|jobs|employment)\s+[^.]{0,100}',
        r'(gdp|gross domestic product|economy)\s+[^.]{0,100}',
        r'(tariff|trade war|trade deal|import|export)\s+[^.]{0,100}',
        r'(ceo|executive|leadership|appointed|resigned)\s+[^.]{0,100}',
    ]

    for pattern in fact_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        facts.extend(matches[:3])  # Limit to 3 per pattern

    # Extract time-sensitive events (this week, recently, etc.)
    time_patterns = [
        r'(this week|last week|recently|today|yesterday)\s+[^.]{0,150}',
        r'(announced|reported|released|launched)\s+[^.]{0,100}',
        r'(up|down|increased|decreased|rose|fell)\s+[^.]{0,100}',
    ]

    for pattern in time_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        events.extend(matches[:3])  # Limit to 3 per pattern

    return {
        "facts": facts[:10],  # Top 10 facts
        "data_points": data_points[:10],  # Top 10 data points
        "events": events[:10],  # Top 10 recent events
        "source_url": url
    }

def siebert_newsletter_sources(topic_lens: str = "", exclude_topics: str = "crypto,day_trading") -> str:
    """
    Tool semplificato per ricerca di contenuto finanziario attuale e rilevante.

    Args:
        topic_lens: Focus specifico per il contenuto
        exclude_topics: Topics da escludere (separati da virgola)

    Returns:
        Contenuto finanziario attuale con contesto completo
    """
    logger.info(f"Searching for current financial content with topic lens: {topic_lens}")

    # Costruiamo query di ricerca specifiche per contenuto finanziario recente
    search_queries = [
        f"financial news investment trends this week 2025 {topic_lens}",
        f"market analysis earnings reports last 7 days {topic_lens}",
        f"economic indicators policy changes recent {topic_lens}",
        f"IPO acquisitions merger announcements 2025 {topic_lens}",
        f"inflation interest rates fed policy this month {topic_lens}"
    ]

    # Escludiamo topics non desiderati
    exclude_list = [t.strip().lower() for t in exclude_topics.split(",") if t.strip()]
    for query_idx, query in enumerate(search_queries):
        for exclude_topic in exclude_list:
            search_queries[query_idx] += f" -{exclude_topic}"

    all_content = []

    for query in search_queries:
        try:
            search_results = serper_search(query, num_results=5)
            search_data = json.loads(search_results)
            organic_results = search_data.get("organic", [])

            for result in organic_results[:3]:  # Top 3 per query
                title = result.get("title", "")
                snippet = result.get("snippet", "")
                link = result.get("link", "")

                if title and snippet:
                    # Check se dovremmo escludere questo contenuto
                    content_text = f"{title} {snippet}".lower()
                    should_exclude = any(exclude_topic in content_text for exclude_topic in exclude_list)

                    if not should_exclude:
                        all_content.append({
                            "title": title,
                            "summary": snippet,
                            "url": link,
                            "relevance_score": len([word for word in topic_lens.lower().split() if word in content_text])
                        })

        except Exception as e:
            logger.warning(f"Search error for query '{query}': {str(e)}")
            continue

    # Ordiniamo per rilevanza e prendiamo i migliori
    all_content.sort(key=lambda x: x["relevance_score"], reverse=True)
    top_content = all_content[:10]

    if not top_content:
        return f"""# Financial Content Analysis
**Topic Focus**: {topic_lens}
**Analysis Date**: {datetime.now().strftime('%Y-%m-%d %H:%M')}

## ⚠️ Limited Results Found

No relevant financial content found matching the specified criteria. This may indicate:
- Very specific topic lens requirements
- Limited recent content in the financial space
- Content behind paywalls or requiring authentication

**Recommendation**: Broaden the topic lens or try alternative search approaches.
"""

    # Generiamo summary strutturato con contenuto reale
    final_summary = f"""# Financial Content Analysis
**Topic Focus**: {topic_lens}
**Analysis Date**: {datetime.now().strftime('%Y-%m-%d %H:%M')}
**Relevant Articles Found**: {len(top_content)}

## 📰 CURRENT FINANCIAL INSIGHTS

"""

    # Raggruppiamo per categorie basate sui titoli
    market_news = []
    earnings_news = []
    policy_news = []
    general_news = []

    for item in top_content:
        title_lower = item["title"].lower()
        if any(keyword in title_lower for keyword in ["earnings", "revenue", "profit", "quarterly"]):
            earnings_news.append(item)
        elif any(keyword in title_lower for keyword in ["fed", "interest", "inflation", "policy", "rate"]):
            policy_news.append(item)
        elif any(keyword in title_lower for keyword in ["market", "stock", "trading", "index"]):
            market_news.append(item)
        else:
            general_news.append(item)

    # Costruiamo l'output strutturato
    if earnings_news:
        final_summary += "### 💰 EARNINGS & CORPORATE PERFORMANCE\n\n"
        for item in earnings_news[:3]:
            final_summary += f"**{item['title']}**\n"
            final_summary += f"{item['summary']}\n"
            final_summary += f"[Read More]({item['url']})\n\n"

    if policy_news:
        final_summary += "### 🏛️ ECONOMIC POLICY & INDICATORS\n\n"
        for item in policy_news[:3]:
            final_summary += f"**{item['title']}**\n"
            final_summary += f"{item['summary']}\n"
            final_summary += f"[Read More]({item['url']})\n\n"

    if market_news:
        final_summary += "### 📈 MARKET TRENDS & ANALYSIS\n\n"
        for item in market_news[:3]:
            final_summary += f"**{item['title']}**\n"
            final_summary += f"{item['summary']}\n"
            final_summary += f"[Read More]({item['url']})\n\n"

    if general_news:
        final_summary += "### 📊 GENERAL FINANCIAL NEWS\n\n"
        for item in general_news[:2]:
            final_summary += f"**{item['title']}**\n"
            final_summary += f"{item['summary']}\n"
            final_summary += f"[Read More]({item['url']})\n\n"

    final_summary += f"""## 🎯 KEY TAKEAWAYS FOR CONTENT CREATION

**Trending Topics Identified:**
- {len(earnings_news)} earnings-related stories
- {len(policy_news)} policy/economic indicator updates  
- {len(market_news)} market trend analyses
- {len(general_news)} other financial developments

**Content Opportunities:**
- Create educational explainers around current earnings trends
- Develop Gen Z-focused analysis of policy changes
- Use current market movements as teaching moments
- Connect financial news to practical money management tips

**Siebert Brand Angle:**
Focus on how these developments affect young investors and their financial planning decisions. Use these insights to create empowering, educational content that builds financial confidence.
"""

    logger.info(f"Financial content analysis completed - found {len(top_content)} relevant articles")
    return final_summary

def get_available_tools(client_name: str = None, config: dict = None) -> Dict[str, Tool]:
    """
    Get all available tools for the content generation system.
    """
    # Import premium tools
    from .tools_websearch import get_premium_financial_tools
    def web_search_wrapper(query: str = None, *args, **kwargs) -> str:
        """Wrapper function to ensure proper parameter passing to serper_search."""
        # Extract query from various possible parameter formats
        if not query and args:
            query = args[0]

        # Try to get query from keyword arguments if still None
        if not query and kwargs:
            query = kwargs.get('query') or kwargs.get('q') or kwargs.get('search_query')

        # Validate query parameter
        if not query or not isinstance(query, str):
            return json.dumps({"error": "Invalid or missing query parameter", "results": []})

        return serper_search(query)

    web_search_tool = Tool(
        name="Web Search Tool",
        description="Performs Google searches and returns relevant results. Input should be a search query string.",
        func=web_search_wrapper
    )
    if client_name:
        from functools import partial
        client_markdown_reader = partial(read_markdown, client_name=client_name)
        client_markdown_writer = partial(write_markdown, client_name=client_name)
        def client_rag_content_wrapper(document_name=None, *args, **kwargs):
            # Handle different parameter formats from CrewAI
            if not document_name and args:
                document_name = args[0]
            if not document_name and kwargs:
                document_name = kwargs.get('document_name') or kwargs.get('filename') or kwargs.get('file')

            # First priority: use pre-selected files from config
            if config and config.get('rag_file_contents'):
                selected_files = config['rag_file_contents']
                combined_content = []
                for file_name, content in selected_files.items():
                    combined_content.append(f"=== {file_name} ===\n\n{content}\n\n")
                return "\n".join(combined_content)

            # Fallback: check session state (for Streamlit context)
            try:
                import streamlit as st
                if hasattr(st, 'session_state') and 'current_rag_files' in st.session_state:
                    selected_files = st.session_state.get('current_rag_files', {})
                    if selected_files:
                        combined_content = []
                        for file_name, content in selected_files.items():
                            combined_content.append(f"=== {file_name} ===\n\n{content}\n\n")
                        return "\n".join(combined_content)
            except ImportError:
                pass  # Streamlit not available (CLI context)

            return read_rag_content(client_name=client_name, document_name=document_name)
        markdown_reader_tool = Tool(
            name="Markdown Reader",
            description=f"Reads and returns content from markdown files for client '{client_name}'.",
            func=client_markdown_reader
        )
        markdown_writer_tool = Tool(
            name="Markdown Writer",
            description=f"Writes content to markdown files for client '{client_name}'.",
            func=client_markdown_writer
        )
        rag_tool = Tool(
            name="RAG Content Retriever",
            description=f"Retrieves content from the RAG directory for client '{client_name}'.",
            func=client_rag_content_wrapper
        )
    else:
        markdown_reader_tool = Tool(
            name="Markdown Reader",
            description="Reads and returns content from markdown files.",
            func=read_markdown
        )
        markdown_writer_tool = Tool(
            name="Markdown Writer",
            description="Writes content to markdown files.",
            func=write_markdown
        )
        rag_tool = Tool(
            name="RAG Content Retriever",
            description="Retrieves content from the RAG directory for a specific client.",
            func=read_rag_content
        )
    siebert_tool = Tool(
        name="Siebert Newsletter Sources Analysis",
        description="Scrapes and analyzes the 9 Siebert newsletter sources to identify relevant insights based on a specified topic lens.",
        func=siebert_newsletter_sources
    )
    siebert_newsletter_tool = Tool(
        name="Siebert Newsletter Sources Analysis",
        description="Scrapes and analyzes the 9 Siebert newsletter sources to identify relevant insights based on a specified topic lens.",
        func=siebert_newsletter_sources
    )

    # Get premium financial tools
    premium_tools = get_premium_financial_tools()

    return {
        "web_search_tool": web_search_tool,
        "markdown_reader_tool": markdown_reader_tool,
        "markdown_writer_tool": markdown_writer_tool,
        "rag_tool": rag_tool,
        "siebert_tool": siebert_newsletter_tool,
        "siebert_newsletter_sources": siebert_newsletter_tool,
        "premium_financial_sources": premium_tools.get("premium_financial_sources")
    }