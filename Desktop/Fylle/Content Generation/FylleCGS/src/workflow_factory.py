# workflow_factory.py
# Factory per la creazione dei workflow, estratta da tasks_and_tools.py

import logging
from typing import Dict, Any, Optional
from .tools import get_available_tools
from .tasks import get_available_tasks

logger = logging.getLogger(__name__)

def workflow_factory(workflow_name: str, topic: str, agents_factory, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Create a workflow with the specified name and topic.
    Args:
        workflow_name (str): Name of the workflow to create.
        topic (str): Topic for the content generation.
        agents_factory: Factory for creating agents.
        config (Optional[Dict[str, Any]]): Additional configuration for the workflow.
            - client_name (str): Name of the client for RAG integration.
            - target_audience (str): Target audience for the content.
            - context (str): Additional context for the content generation.
    Returns:
        Dict[str, Any]: Dictionary containing agents, tasks, and tools for the workflow.
    Raises:
        ValueError: If the workflow is not supported.
    """
    config = config or {}
    client_name = config.get("client_name", "")
    tools = get_available_tools(client_name)
    agents = agents_factory.create_agents(tools)
    tasks = get_available_tasks(agents)
    try:
        module_name = f"workflows.{workflow_name}"
        if not workflow_name.startswith("workflow_"):
            module_name = f"workflows.workflow_{workflow_name}"
        workflow_module = __import__(module_name, fromlist=["create_workflow"])
        if hasattr(workflow_module, "create_workflow"):
            return workflow_module.create_workflow(topic, agents, tasks, tools, config)
    except ImportError as e:
        logger.error(f"Failed to import workflow '{workflow_name}': {str(e)}")
    except Exception as e:
        logger.error(f"Error creating workflow '{workflow_name}': {str(e)}")
    raise ValueError(f"Workflow '{workflow_name}' not supported")
