# tasks.py
# Tutte le factory dei task e utility correlate

from typing import Dict, Callable, Any
from crewai import Task

from .tools import get_available_rag_documents

def add_rag_context(context, client_name):
    if client_name:
        docs = get_available_rag_documents(client_name)
        return (
            f"{context}\n\n"
            f"I seguenti documenti sono disponibili nella knowledge base del cliente: {docs}.\n"
            "Usa SOLO questi nomi quando richiesto di leggere o scrivere file."
        ), docs
    return context, []

def get_available_tasks(agents: Dict[str, Any]) -> Dict[str, Callable]:
    """
    Get all available tasks for the content generation system.
    """
    return {
        # Research task
        "research": lambda topic, context="": Task(
            description=f"Conduct comprehensive research on: {topic}\n\nContext: {context}",
            expected_output="Detailed research findings in markdown format with sections for key insights, facts, and relevant information.",
            agent=agents["web_searcher"]
        ),
        # Content planning task
        "content_planning": lambda topic, research_output="", context="": Task(
            description=f"Create a comprehensive content plan for: {topic}\n\nResearch: {research_output}\n\nContext: {context}",
            expected_output="Detailed content outline in markdown format with main sections, subsections, and key points to cover.",
            agent=agents["architect"]
        ),
        # Section writing task
        "section_writing": lambda section_title, section_outline, context="", client_name=None: (lambda _context, _docs: Task(
            description=f"Write a detailed section on: {section_title}\n\nOutline: {section_outline}\n\nContext: {_context}",
            expected_output="Fully developed section content in markdown format that follows the outline and provides comprehensive information.",
            agent=agents["section_writer"],
            available_docs=_docs
        ))(*add_rag_context(context, client_name)),
        # Copywriting task
        "copywriting": lambda topic, target_audience, context="", client_name=None: (lambda _context, _docs: Task(
            description=f"Create persuasive copy for: {topic}\n\nTarget Audience: {target_audience}\n\nContext: {_context}",
            expected_output="Compelling, conversion-focused copy in markdown format that resonates with the target audience.",
            agent=agents["copywriter"],
            available_docs=_docs
        ))(*add_rag_context(context, client_name)),
        # Editing task
        "editing": lambda content, context="", client_name=None: (lambda _context, _docs: Task(
            description=f"Edit and refine the following content:\n\n{content}\n\nContext: {_context}",
            expected_output="Polished, error-free content in markdown format with improved clarity, coherence, and flow.",
            agent=agents["editor"],
            available_docs=_docs
        ))(*add_rag_context(context, client_name)),
        # Quality review task
        "quality_review": lambda content, requirements="", context="", client_name=None: (lambda _context, _docs: Task(
            description=f"Review the following content for quality and alignment with requirements:\n\n{content}\n\nRequirements: {requirements}\n\nContext: {_context}",
            expected_output="Detailed quality assessment in markdown format with identified issues, strengths, and improvement suggestions.",
            agent=agents["quality_reviewer"],
            available_docs=_docs
        ))(*add_rag_context(context, client_name)),
        # RAG knowledge integration task
        "rag_integration": lambda content, client_name, context="": (lambda _context, _docs: Task(
            description=f"Integrate relevant knowledge from RAG sources for client '{client_name}' into the following content:\n\n{content}\n\nContext: {_context}",
            expected_output="Enhanced content in markdown format with seamlessly integrated knowledge from RAG sources.",
            agent=agents["rag_specialist"],
            available_docs=_docs
        ))(*add_rag_context(context, client_name)),
    }
