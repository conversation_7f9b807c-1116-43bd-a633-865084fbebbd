import os
import sys
# Aggiungi la root del progetto a sys.path per import robusti
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
# Controllo dipendenze chiave
missing = []
for mod in ["crewai", "langchain", "requests"]:
    try:
        __import__(mod)
    except ImportError:
        missing.append(mod)
if missing:
    print("Errore: le seguenti dipendenze backend sono mancanti:", ", ".join(missing))
    print("Installa le dipendenze mancanti e riprova. (requirements.txt)\nTest interrotto.")
    sys.exit(1)
from src.agents import AgentsFactory
from src.tools import get_available_tools, get_available_rag_documents, read_rag_content
from src.config import LLM_MODELS

# === TEST RAG TOOLS (pytest-style) ===
def test_get_available_rag_documents_empty():
    """Restituisce lista vuota se il client non esiste."""
    docs = get_available_rag_documents("__non_existing_client__")
    assert isinstance(docs, list)
    assert len(docs) == 0

def test_get_available_rag_documents_real(tmp_path):
    """Restituisce la lista dei file .md se il client esiste."""
    # Setup: crea una cartella client fittizia con file .md
    client_dir = tmp_path / "TestClient"
    client_dir.mkdir()
    (client_dir / "file1.md").write_text("Contenuto 1")
    (client_dir / "file2.md").write_text("Contenuto 2")
    # Patch RAG_DIR temporaneamente
    import src.tools as tt
    old_rag_dir = tt.RAG_DIR
    tt.RAG_DIR = str(tmp_path)
    docs = get_available_rag_documents("TestClient")
    tt.RAG_DIR = old_rag_dir
    assert sorted(docs) == ["file1.md", "file2.md"]

def test_read_rag_content_doc_not_found(tmp_path):
    """Se il documento non esiste, la risposta contiene 'Documenti disponibili'."""
    client_dir = tmp_path / "TestClient"
    client_dir.mkdir()
    (client_dir / "file1.md").write_text("Contenuto 1")
    import src.tools as tt
    old_rag_dir = tt.RAG_DIR
    tt.RAG_DIR = str(tmp_path)
    result = read_rag_content("TestClient", "not_exist.md")
    tt.RAG_DIR = old_rag_dir
    assert "Documenti disponibili" in result
    assert "file1.md" in result

def test_read_rag_content_doc_found(tmp_path):
    """Se il documento esiste, viene restituito il contenuto corretto."""
    client_dir = tmp_path / "TestClient"
    client_dir.mkdir()
    (client_dir / "file1.md").write_text("Contenuto 1")
    import src.tools as tt
    old_rag_dir = tt.RAG_DIR
    tt.RAG_DIR = str(tmp_path)
    result = read_rag_content("TestClient", "file1.md")
    tt.RAG_DIR = old_rag_dir
    assert result == "Contenuto 1"

def find_random_md_file(rag_dir):
    for root, dirs, files in os.walk(rag_dir):
        for f in files:
            if f.endswith('.md'):
                return os.path.join(root, f)
    return None

def find_random_client_and_doc(rag_dir):
    clients = [d for d in os.listdir(rag_dir) if os.path.isdir(os.path.join(rag_dir, d))]
    for client in clients:
        client_dir = os.path.join(rag_dir, client)
        for f in os.listdir(client_dir):
            if f.endswith('.md'):
                return client, f
    return None, None

def test_tool(tool, tool_key, model_name, rag_dir):
    try:
        if 'search' in tool.name.lower():
            test_input = "python openai"
            result = tool.func(test_input)
        elif 'markdown' in tool.name.lower():
            md_file = find_random_md_file(rag_dir)
            if not md_file:
                return (tool_key, model_name, False, "No .md file found in RAG dir")
            result = tool.func(md_file)
        elif 'rag' in tool.name.lower():
            client, doc = find_random_client_and_doc(rag_dir)
            if not client or not doc:
                return (tool_key, model_name, False, "No client/doc found in RAG dir")
            result = tool.func(client_name=client, document_name=doc)
        else:
            result = tool.func("test")
        # Success if result is not None or empty
        success = bool(result and isinstance(result, str))
        return (tool_key, model_name, success, str(result)[:100])
    except Exception as e:
        return (tool_key, model_name, False, str(e))

def main():
    rag_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "rag"))
    models = list(LLM_MODELS.keys())
    tools = get_available_tools()
    factory = AgentsFactory()
    report = []
    for model in models:
        print(f"\n=== MODEL: {model} ===")
        agents = factory.create_agents(tools)
        for agent_name, agent in agents.items():
            print(f"\nAgent: {agent_name} ({agent.role})")
            if not agent.tools:
                print("  Nessun tool assegnato.")
                continue
            for tool in agent.tools:
                tool_key = [k for k, v in tools.items() if v is tool]
                tool_key = tool_key[0] if tool_key else tool.name
                result = test_tool(tool, tool_key, model, rag_dir)
                status = "✅" if result[2] else "❌"
                print(f"  {status} Tool '{tool.name}' [{tool_key}] (model: {model}): {result[3]}")
                report.append(result)
    print("\n--- SUMMARY ---")
    for r in report:
        status = "OK" if r[2] else "FAIL"
        print(f"Tool: {r[0]}, Model: {r[1]}, Status: {status}, Info: {r[3]}")

if __name__ == "__main__":
    main()
