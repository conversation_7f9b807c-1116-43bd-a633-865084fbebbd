import os
import sys
import tempfile
import shutil
import pytest
from pathlib import Path

# PATCH: aggiungi la root project al sys.path per import src.*
sys.path.insert(0, str(Path(__file__).parent.parent.resolve()))

from src.workflow_builder import generate_workflow_code, save_workflow_file

def example_selected_agents():
    return {
        "writer": {"role": "Writer"},
        "editor": {"role": "Editor"}
    }

def example_task_sequence():
    return [
        {"type": "pre", "name": "research", "agent": "writer"},
        {"type": "custom", "name": "custom1", "description": "Desc custom", "agent": "editor", "expected_output": "Output custom"}
    ]

def test_generate_code_sintassi():
    code = generate_workflow_code(
        name="TestWorkflow",
        description="Descrizione di test",
        selected_agents=example_selected_agents(),
        task_sequence=example_task_sequence(),
        tools=["tool1", "tool2"]
    )
    # Verifica che sia una stringa e che compili
    assert isinstance(code, str)
    try:
        exec(code, {})
    except Exception as e:
        pytest.fail(f"Il codice generato non compila: {e}")

def test_save_file(tmp_path):
    workflow_name = "test_workflow"
    code = generate_workflow_code(
        name=workflow_name,
        description="desc",
        selected_agents=example_selected_agents(),
        task_sequence=example_task_sequence(),
        tools=["tool1"]
    )
    # Patch workflows dir
    profile = "default"
    workflows_dir = tmp_path / "workflows"
    workflows_dir.mkdir()
    orig_cwd = os.getcwd()
    try:
        os.chdir(tmp_path)
        # Patch Path inside save_workflow_file
        import builtins
        orig_path = Path
        class PatchedPath(Path):
            @classmethod
            def __new__(cls, *args, **kwargs):
                p = orig_path(*args, **kwargs)
                if str(p).endswith("workflows"):
                    return workflows_dir
                return p
        import src.workflow_builder as wb
        wb.Path = PatchedPath
        wb.save_workflow_file(profile, workflow_name, code)
        wf_file = workflows_dir / f"workflow_{workflow_name}.py"
        assert wf_file.exists(), "Il file workflow non è stato creato"
        content = wf_file.read_text(encoding="utf-8")
        assert workflow_name in content
    finally:
        os.chdir(orig_cwd)
        # Clean up monkeypatch
        import src.workflow_builder as wb
        wb.Path = orig_path
