# Modular Content Generation System

A scalable and extensible platform for generating high-quality content through orchestrated workflows of specialized AI agents.

## Overview

This system enables automated content generation using a modular architecture of agents, tasks, and tools organized into customizable workflows. Each component is designed to be independent and reusable, allowing for easy extension and customization without modifying the core functionality.

## Architecture

### Core Components

- **Agents**: Specialized AI agents with specific roles (researcher, writer, editor, etc.)
- **Tasks**: Individual steps in the content generation process
- **Tools**: Utilities that agents can use (web search, markdown processing, etc.)
- **Workflows**: Sequences of tasks performed by agents to generate content

---

## System Logic & File Interaction

### Main Components and How They Interact

- **main.py**: CLI entrypoint. Handles workflow selection, parameter parsing, and execution. Interacts with the workflow factory and saves results.
- **app.py**: Streamlit UI. Allows interactive selection/configuration of workflows, model/provider choice, preview and download of results. Calls the same backend logic as main.py.
- **src/config.py**: Centralizes configuration (API keys, model/provider defaults, parameter validation). All other modules import from here.
- **src/agents.py**: Defines the `AgentsFactory` class, which creates specialized agents for each workflow step. Each agent can use a different provider/model/tool.
- **src/tools.py**: Implementa tutti i tool (web search, markdown, RAG, ecc.) e la factory dei tools.
- **src/tasks.py**: Implementa tutte le factory dei task e le utility correlate.
- **src/tasks_and_tools.py**: [DEPRECATO] (ora tutto è suddiviso in tools.py e tasks.py).
- **workflows/**: Each file defines a workflow (pipeline of tasks/agents). Workflows are discovered dynamically and selectable from CLI/UI. Example: `workflow_siebert_3step.py` (3-step pipeline: research, drafting, editing).
- **rag/**: Contains client-specific knowledge base documents. Tools in tasks_and_tools.py can retrieve and use this knowledge if the workflow is configured for RAG.
- **output/**: Stores generated content.

### Interaction Flow

1. **User launches** via CLI (`main.py`) or UI (`app.py`).
2. **Workflow is selected**; the workflow factory loads the appropriate file from `workflows/` and calls its `get_workflow` function.
3. **AgentsFactory** creates agents as specified by the workflow, each with its own model/provider/tool config.
4. **Tasks** sono definiti e assegnati agli agenti; ogni task può utilizzare uno o più tool definiti in `tools.py`.
5. **Knowledge base (rag/)** is accessed via tools if the workflow/client requires it.
6. **Results** are saved to `output/` and/or shown in the UI.
7. **Building / Profile Manager**: nella UI tab "Building" gli utenti possono creare nuovi profili, generare stub agent YAML, configurare e salvare metadati agent individuali, visualizzare in anteprima gli agent già configurati e finalizzare il profilo per utilizzarlo nella tab "Generazione".
8. **Workflow Builder**: nella UI tab "Workflow Builder" puoi creare workflow Python personalizzati scegliendo profilo, agenti, sequenza di task (predefiniti o custom), visualizzare l'anteprima del codice generato e salvarlo direttamente nella cartella `workflows/`. Ogni workflow generato avrà la firma corretta:
    ```python
    def create_workflow(topic, agents, tools, config):
        ...
    ```
    e sarà subito utilizzabile dalla UI e dal CLI.

---

### Best Practices

- Keep each workflow, agent, and tool modular (one file/function per entity where possible).
- Use configuration parameters to make workflows flexible (topic, audience, model, provider, etc.).
- Document every workflow, agent, and tool with clear docstrings.
- Reuse code: create generic, reusable agents and tools.
- Add error handling and logging for robustness.
- Use descriptive naming for all files, agents, tools, and workflows.
- Test new workflows from both CLI and UI.
- Always update the README and code comments when adding new features.

## Project Structure

```
content-generation-system/
│   main.py                 # CLI entrypoint
│   requirements.txt        # Project dependencies
│   README.md              # Project documentation

---

## Estendere il Sistema: Modelli, Agenti, Tool e Task

Questa sezione guida passo-passo su come estendere la piattaforma aggiungendo nuovi modelli, agenti, tool o task, mantenendo test e UI sempre aggiornati e funzionanti.

### ➕ **Aggiungere un nuovo MODELLO LLM**
- Aggiorna `src/config.py` aggiungendo il modello in `LLM_MODELS` e, se serve, la chiave API in `.env`.
- Aggiorna `src/api_health.py` per includere health check e stato del nuovo modello.
- Aggiorna la variabile `model_options` in `app.py` per mostrarlo nella UI.
- I test (`src/test_agent_tools.py`) lo includeranno automaticamente se presente in `LLM_MODELS`.

### ➕ **Aggiungere un nuovo AGENTE**
- Definisci la logica in `src/agents.py` (in `AgentsFactory`).
- Associa tool e parametri all’agente.
- Aggiorna descrizione, ruolo e goal per chiarezza nella UI (tab Sistema).

### ➕ **Aggiungere un nuovo TOOL**
- Implementa la funzione/classe in `src/tools.py` o `src/tasks.py` a seconda della logica.
- Aggiorna `get_available_tools()` per includere il nuovo tool (con `name` e `description` descrittivi).
- Aggiorna `src/test_agent_tools.py` per testarlo realmente (scegli input di test sensato).
- La tab Sistema mostrerà automaticamente il nuovo tool e permetterà il test interattivo.

### ➕ **Aggiungere un nuovo TASK**
- Definisci il nuovo task in `src/tasks.py` e aggiorna i workflow se necessario.
- Se il task è parte di un nuovo workflow, aggiungi il file in `workflows/` e aggiorna la funzione `get_available_workflows()` in `app.py`.
- Se il task usa nuovi tool, aggiorna i test.

### 🧪 **Testing e UI**
- I test automatici (`src/test_agent_tools.py`) validano tutti i tool reali su tutti i modelli.
- La tab Sistema mostra stato e descrizione di ogni tool e agente, senza bisogno di modifiche manuali (se hai aggiornato `get_available_tools()` e `AgentsFactory`).
- Aggiorna sempre le docstring e la documentazione inline.

### ✅ **Checklist per ogni estensione**

- [ ] Aggiornato `src/config.py` e `.env` (se serve)
- [ ] Aggiornato `AgentsFactory` (`src/agents.py`)
- [ ] Aggiornato/aggiunto il tool in `src/tools.py`
- [ ] Aggiornato il test script (`src/test_agent_tools.py`)
- [ ] Aggiornata la UI (tab Sistema) per tool e agenti
- [ ] Aggiornata la documentazione (`README.md` e docstring)
- [ ] Testato tutto da front-end con “Test Agent Tools (reale)”

---

**Segui questa checklist per garantire che ogni nuova estensione sia integrata, testata e documentata!**

---

## 📌 Esempi Pratici

### ➕ Aggiungere un nuovo modello LLM
```python
# src/config.py
LLM_MODELS["myllm"] = {
    "default": "myllm-base",
    "parameters": {"temperature": 0.7, "max_tokens": 1024}
}
```

### ➕ Aggiungere un nuovo agente
```python
# src/agents.py
class MySpecialAgent(BaseAgent):
    ...
# In AgentsFactory.create_agents:
agents["my_specialist"] = MySpecialAgent(...)
```

### ➕ Aggiungere un nuovo tool
```python
# src/tools.py e src/tasks.py
def my_tool(input):
    """Esempio di nuovo tool."""
    return f"Output di test per {input}"
tools["my_tool"] = Tool(
    name="My Tool",
    func=my_tool,
    description="Un tool di esempio che fa echo dell'input."
)
```

### ➕ Aggiungere un nuovo task
```python
# src/tools.py e src/tasks.py
def my_task(...):
    ...
# In workflow:
tasks.append(Task(agent=my_agent, tool="my_tool", ...))
```

---

## ❓ FAQ per sviluppatori

**D: Devo modificare la UI ogni volta che aggiungo un tool o un agente?**  
R: No! Se aggiorni `get_available_tools()` e `AgentsFactory`, la tab Sistema si aggiorna automaticamente.

**D: Come faccio a testare un nuovo tool?**  
R: Aggiungi il test in `src/test_agent_tools.py` con un input di esempio. Premi "Test Agent Tools" nella UI per vedere subito il risultato.

**D: Cosa succede se un tool fallisce nei test?**  
R: Vedrai una ❌ rossa nella UI e il dettaglio dell’errore. Puoi usare questo feedback per il debug.

**D: Come aggiungo un nuovo workflow?**  
R: Crea un nuovo file in `workflows/` seguendo gli esempi esistenti e aggiorna `get_available_workflows()` in `app.py`.

**D: Come posso aggiungere un nuovo provider LLM?**  
R: Aggiorna `LLM_MODELS` in `src/config.py`, aggiungi health check in `src/api_health.py`, aggiorna la UI e le chiavi API in `.env`.

**D: Come posso vedere quali agenti e tool sono disponibili?**  
R: Vai nella tab "Sistema" della UI: troverai la lista aggiornata di agenti, tool e il loro stato/test.

**D: Ho aggiunto un nuovo task, ma non compare nella UI. Perché?**  
R: I task sono visibili solo se inclusi in un workflow attivo e assegnati a un agente. Controlla la definizione del workflow.

---

├── src/
│   ├── __init__.py
│   ├── config.py          # Centralized configuration
│   ├── agents.py          # Agent definitions and factory
│   └── tasks_and_tools.py # Task and tool definitions
├── workflows/
│   ├── __init__.py
│   └── workflow_siebert.py # Siebert workflow definition
├── output/
│   └── ...                # Generated content
└── rag/
    ├── ClienteA/          # Client-specific knowledge
    │     ├── brand_voice.md
    │     ├── style_guide.md
    │     └── terminology.md
    ├── ClienteB/
    │     ├── brand_voice.md
    │     └── company_background.md
    └── ClienteC/
          └── ...
```

## Installation

### Prerequisites

- Python 3.8 or higher
- API keys for LLM providers (OpenAI, Anthropic, DeepSeek)
- Serper API key for web search functionality

### Setup

1. Clone the repository:

```bash
git clone <repository-url>
cd content-generation-system
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Create a `.env` file in the project root with your API keys:

```
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
SERPER_API_KEY=your_serper_api_key
```

## Usage

### Command Line Interface

The system can be used through the command line interface:

```bash
# Basic usage with default workflow
python main.py --topic "Artificial Intelligence in Healthcare"

# Specifying workflow and client for RAG integration
python main.py --topic "Sustainable Energy Solutions" --workflow basic --client ClienteA

# Full customization
python main.py --topic "Digital Marketing Trends 2025" \
               --workflow siebert \
               --client ClienteB \
               --audience "Marketing Professionals" \
               --temperature 0.8 \
               --provider anthropic \
               --model claude-3-opus
```

### Available Arguments

- `--topic`: (Required) Topic for content generation
- `--workflow`: Workflow to use (default: "siebert")
- `--output`: Path to save the generated content
- `--client`: Client name for RAG integration
- `--audience`: Target audience for the content
- `--temperature`: Temperature for the LLM (default: 0.7)
- `--provider`: LLM provider to use (default: "openai")
- `--model`: Specific model to use

## Extending the System

### How to Create a New Workflow (with RAG Context Selection)

1. **Create a new file** in the `workflows/` directory (e.g., `workflow_custom.py`).
2. **Define a function** `get_workflow(topic, agents, tasks, tools, config=None)` (or `create_workflow(...)` if using the factory pattern):
   - Use the `config` parameter to access dynamic options from the UI/CLI, including provider, model, temperature, RAG context, etc.
   - Instantiate agents using the correct provider/model from `config`, and assign relevant tools (including RAG tools if needed).
   - Define the sequence of tasks, specifying for each if/when to use RAG context (see below).
   - Return the workflow structure as a dictionary.
3. **No registration needed**: workflows are auto-discovered and selectable from UI/CLI.

#### **Best Practices for RAG Integration**
- Use the `config` dictionary to access:
  - `config['client_name']` for client-specific RAG knowledge
  - `config['rag_subfolder']`, `config['rag_file']`, `config['rag_file_content']` for granular context selection
  - `config['provider']`, `config['model']` for dynamic LLM selection
- When creating agents, always pass provider/model from `config` unless you want to override.
- When creating tools (e.g., markdown reader, RAG retriever), use `functools.partial` to inject client/context info if needed.

---

## 🚦 Costruire Workflow Dichiarativi con il Nuovo Orchestratore

Il nuovo orchestratore consente di definire workflow in modo dichiarativo, strutturando la sequenza di task come una lista di dizionari Python. Questo rende la definizione, la manutenzione e l'estensione dei workflow molto più semplice e trasparente.

### **Struttura di un Workflow Dichiarativo**
Un workflow dichiarativo è una funzione che restituisce un dizionario con almeno due chiavi:
- `agents`: un dizionario degli agenti coinvolti
- `tasks`: una lista di dizionari, ciascuno che rappresenta un task

**Esempio base:**
```python
def create_workflow(topic, agents, tools, config):
    return {
        "agents": {
            "copywriter": agents["copywriter"],
            "rag_specialist": agents["rag_specialist"],
        },
        "tasks": [
            {
                "id": "summary_rag",
                "agent": "rag_specialist",
                "task": "Leggi e integra i contenuti della knowledge base e produci un riassunto.",
                "input": config.get("rag_file_content", "") or topic,
                "tools": ["rag_content_retriever"],
            },
            {
                "id": "structure",
                "agent": "copywriter",
                "task": "Scrivi la struttura generale del White Paper sulla base del brief e del riassunto.",
                "input": "{{summary_rag}}",  # Usa l'output del task precedente
            },
            # ... altri task ...
        ]
    }
```

### **Caratteristiche principali**
- Ogni task ha un `id` univoco.
- Il campo `input` può referenziare l'output di altri task usando la sintassi `{{task_id}}`.
- Gli agenti sono referenziati per chiave nel dizionario `agents`.
- I tool possono essere assegnati per task (opzionale).

### **Best Practices**
- Dai nomi chiari e coerenti a task e agenti.
- Usa la dichiarazione esplicita delle dipendenze tra task tramite `input`.
- Utilizza il campo `config` per passare parametri dinamici (topic, audience, client, ecc.).
- Documenta ogni workflow con docstring e commenti.
- Testa il workflow sia da CLI che da UI.

### **Checklist per Workflow Orchestratore**
- [ ] Ogni task ha un `id` univoco
- [ ] Gli agenti sono definiti e assegnati correttamente
- [ ] Le dipendenze tra task sono esplicite negli input
- [ ] Tutti i parametri dinamici sono gestiti tramite `config`
- [ ] Il workflow è documentato con docstring e commenti
- [ ] Testato da CLI (`main.py`) e da UI (`app.py`)

### **Debug e Logging**
- Il sistema logga automaticamente errori di struttura o di esecuzione.
- Se un task non trova il file/contesto richiesto, vengono mostrati i file disponibili per il debug.

### **Esempio Avanzato: Workflow con RAG e Revisione Finale**
```python
def create_workflow(topic, agents, tools, config):
    return {
        "agents": {
            "copywriter": agents["copywriter"],
            "rag_specialist": agents["rag_specialist"],
        },
        "tasks": [
            {
                "id": "summary_rag",
                "agent": "rag_specialist",
                "task": "Leggi e integra i contenuti della knowledge base e produci un riassunto.",
                "input": config.get("rag_file_content", "") or topic,
                "tools": ["rag_content_retriever"],
            },
            {
                "id": "structure",
                "agent": "copywriter",
                "task": "Scrivi la struttura generale del White Paper sulla base del brief e del riassunto.",
                "input": "{{summary_rag}}",
            },
            {
                "id": "section1",
                "agent": "copywriter",
                "task": "Redigi la PRIMA sezione principale del White Paper seguendo la struttura.",
                "input": "{{structure}}",
            },
            {
                "id": "final_review",
                "agent": "copywriter",
                "task": "Ricevi il testo integrale e il summary fornito dal RAG Specialist. Restituisci il testo definitivo del White Paper.",
                "input": "{{section1}}\n\n{{summary_rag}}",
            },
        ]
    }
```

Per ulteriori esempi, consulta i file in `workflows/` e la documentazione inline nei moduli.

- In your tasks, explicitly state when to use RAG context (e.g., pass `rag_file_content` as input or context to the agent/task).

**Example:**
```python
from functools import partial
from src.tasks_and_tools import read_rag_content

def get_workflow(topic, agents, tasks, tools, config=None):
    # Example: dynamic agent creation
    researcher = agents['web_searcher'](provider=config['provider'], model=config['model'])
    writer = agents['copywriter'](provider=config['provider'], model=config['model'])
    editor = agents['editor'](provider=config['provider'], model=config['model'])

    # Example: client-specific RAG tool
    rag_tool = partial(read_rag_content, client_name=config.get('client_name', None))

    # Example: using RAG context in tasks
    research_task = tasks['research'](agent=researcher, tool=tools['web_search'], input=topic)
    writing_task = tasks['copywriting'](
        agent=writer,
        tool=rag_tool,
        input=config.get('rag_file_content', None) or topic
    )
    editing_task = tasks['editing'](agent=editor, tool=tools['editing'], input="output_of_previous_task")

    return {"tasks": [research_task, writing_task, editing_task]}
```

#### **Tips for Advanced RAG Workflows**
- You can chain tasks so that the output of a RAG-augmented agent is used as input for the next step (e.g., copywriter → editor).
- You can expose additional context fields in the UI and pass them via `config['context']`.
- For each agent, you can assign different tools or context depending on the workflow logic.
- All RAG context selection logic should be handled in the workflow file, using the `config` dictionary for maximum flexibility.

#### **Checklist for New RAG-Enabled Workflows**
- [ ] Use the new `config` fields for provider/model/context selection
- [ ] Support client-specific knowledge via RAG tools
- [ ] Document your workflow with clear docstrings and usage examples
- [ ] Test your workflow from both CLI and UI

### How to Create a New Agent
1. Go to `src/agents.py`.
2. Add a new method in the `AgentsFactory` class to create your agent, specifying model, provider, and allowed tools.
3. Use your new agent in any workflow.

**Example:**
```python
def create_fact_checker(self, model="gpt-4", provider="openai"):
    return Agent(role="fact_checker", model=model, provider=provider, tools=[...])
```

### How to Add a New Tool
1. Go to `src/tasks_and_tools.py`.
2. Define a new function for your tool (e.g., new API or knowledge base integration).
3. Add the tool to the tools dictionary.
4. Assign the tool to agents in your workflows as needed.

**Example:**
```python
def new_tool(...):
    ...
tools["new_tool"] = Tool(name="new_tool", func=new_tool, ...)
```

---

## Appendix: Advanced Tips & Best Practice

- **Modularity**: One workflow, agent, or tool per file/function where possible.
- **Configurability**: Use parameters for topics, models, providers, etc., to maximize flexibility.
- **Documentation**: Add docstrings and update the README for every new workflow/tool/agent.
- **Reusability**: Prefer generic agents/tools that can be reused in multiple workflows.
- **Robustness**: Add logging, error handling, and fallback logic for model/provider selection.
- **Naming**: Use clear, descriptive names for files, agents, tools, and workflows.
- **Testing**: Always test new workflows from both CLI and UI.

```python
# New SEO Specialist Agent
agents["seo_specialist"] = Agent(
    role="SEO Specialist",
    goal="Optimize content for search engines while maintaining readability and value.",
    backstory="Expert in search engine optimization with years of experience in "
             "improving content visibility and ranking. You understand both technical "
             "SEO requirements and content quality factors.",
    llm=self._get_llm(),
    tools=[tools.get("seo_tool")] if "seo_tool" in tools else []
)
```

### Adding a New Tool

Aggiungi una nuova funzione tool e registrala in `get_available_tools()` in `src/tools.py`:

```python
def analyze_seo(content: str) -> str:
    """Analyze content for SEO optimization opportunities."""
    # Implementation here
    return "SEO analysis results"

def get_available_tools() -> Dict[str, Tool]:
    # Existing tools...
    
    # New SEO analysis tool
    seo_tool = Tool(
        name="SEO Analyzer",
        description="Analyzes content for SEO optimization opportunities.",
        func=analyze_seo
    )
    
    return {
        # Existing tools...
        "seo_tool": seo_tool
    }
```

### Adding a New Task

Aggiungi una nuova task factory a `get_available_tasks()` in `src/tasks.py`:

```python
def get_available_tasks(agents: Dict[str, Any]) -> Dict[str, Callable]:
    return {
        # Existing tasks...
        
        # New SEO optimization task
        "seo_optimization": lambda content, keywords="", context="": Task(
            description=f"Optimize the following content for search engines:\n\n{content}\n\nTarget Keywords: {keywords}\n\nContext: {context}",
            expected_output="SEO-optimized content in markdown format with improved keyword usage, meta descriptions, and heading structure.",
            agent=agents["seo_specialist"]
        )
    }
```

## RAG Integration

The system supports Retrieval-Augmented Generation (RAG) to incorporate client-specific knowledge into the content generation process:

1. Create a directory for the client in the `rag` directory (e.g., `rag/ClienteD/`)
2. Add markdown files with relevant information (brand voice, style guide, terminology, etc.)
3. Specify the client name when running a workflow:

```bash
python main.py --topic "Product Features" --client ClienteD
```

## License

[MIT License](LICENSE)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
