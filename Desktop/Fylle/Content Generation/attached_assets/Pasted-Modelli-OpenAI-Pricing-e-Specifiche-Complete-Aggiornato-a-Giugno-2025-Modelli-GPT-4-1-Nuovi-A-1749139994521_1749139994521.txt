Modelli OpenAI: Pricing e Specifiche Complete
Aggiornato a Giugno 2025
🔥 Modelli GPT-4.1 (Nuovi - Aprile 2025)
GPT-4.1

Model ID: gpt-4.1 (versioni specifiche: gpt-4.1-2024-05-31)
Pricing:

Input: $10 / milione di token
Output: $30 / milione di token
Batch API: $5 / $15 per milione di token


Limiti di Token:

Input: 1.047.576 token (~1M)
Output: 32.768 token


Training Data Cutoff: Maggio 2024
Caratteristiche: Eccellente per coding, instruction following, long context

GPT-4.1 Mini

Model ID: gpt-4.1-mini
Pricing:

Input: $3 / milione di token
Output: $12 / milione di token
Batch API: $1.50 / $6 per milione di token


Limiti di Token:

Input: 1.047.576 token (~1M)
Output: 32.768 token


Training Data Cutoff: Maggio 2024
Caratteristiche: Migliore di GPT-4o con 83% meno costi e 50% meno latenza

GPT-4.1 Nano 💎

Model ID: gpt-4.1-nano
Pricing:

Input: $0.10 / milione di token
Output: $0.40 / milione di token
Batch API: $0.05 / $0.20 per milione di token


Limiti di Token:

Input: 1.047.576 token (~1M)
Output: 32.768 token


Training Data Cutoff: Maggio 2024
Caratteristiche: Il modello OpenAI più economico di sempre, ideale per classificazione


🤖 Modelli o-series (Reasoning)
o3-mini

Model ID: o3-mini (reasoning modes: low, medium, high)
Pricing:

Input: $1.10 / milione di token
Output: $4.40 / milione di token


Limiti di Token:

Input: 200.000 token
Output: 100.000 token


Training Data Cutoff: Ottobre 2023
Caratteristiche: Reasoning model economico, eccellente per STEM, coding, matematica
Disponibilità: Solo Tier 3+ ($100+ spesi)

o1

Model ID: o1-preview, o1
Pricing:

Input: $15 / milione di token
Output: $60 / milione di token


Limiti di Token:

Input: 200.000 token
Output: 32.768 token


Training Data Cutoff: Ottobre 2023
Caratteristiche: Advanced reasoning, chain-of-thought, problemi complessi

o1-mini

Model ID: o1-mini
Pricing:

Input: $3 / milione di token
Output: $12 / milione di token


Limiti di Token:

Input: 128.000 token
Output: 32.768 token


Training Data Cutoff: Ottobre 2023
Caratteristiche: Reasoning economico, focus su STEM

o3 (Preview)

Model ID: o3 (in preview)
Pricing: TBA
Limiti di Token:

Input: 200.000 token
Output: TBA


Training Data Cutoff: Giugno 2024
Caratteristiche: Next-gen reasoning model, migliore di o1


🚀 Modelli GPT-4o
GPT-4o

Model ID: gpt-4o, gpt-4o-2024-08-06
Pricing:

Input: $2.50 / milione di token
Output: $10 / milione di token
Batch API: $1.25 / $5 per milione di token


Limiti di Token:

Input: 128.000 token
Output: 16.384 token


Training Data Cutoff: Ottobre 2023
Caratteristiche: Multimodal (text, vision, audio), veloce, versatile

GPT-4o Mini

Model ID: gpt-4o-mini, gpt-4o-mini-2024-07-18
Pricing:

Input: $0.15 / milione di token
Output: $0.60 / milione di token
Batch API: $0.075 / $0.30 per milione di token


Limiti di Token:

Input: 128.000 token
Output: 16.384 token


Training Data Cutoff: Ottobre 2023
Caratteristiche: Più economico, con vision, ideale per task semplici

GPT-4o Audio/Realtime

Model ID: gpt-4o-realtime-preview, gpt-4o-audio-preview
Pricing:

Input Text: $5 / milione di token
Output Text: $20 / milione di token
Audio Input: $100 / milione di token (~$0.06/min)
Audio Output: $200 / milione di token (~$0.24/min)


Caratteristiche: Real-time audio, speech-to-speech, conversational AI


💎 Modelli GPT-4 Turbo
GPT-4 Turbo

Model ID: gpt-4-turbo, gpt-4-turbo-2024-04-09
Pricing:

Input: $10 / milione di token
Output: $30 / milione di token
Batch API: $5 / $15 per milione di token


Limiti di Token:

Input: 128.000 token
Output: 4.096 token


Training Data Cutoff: Aprile 2024
Caratteristiche: Più potente di GPT-4, context window grande

GPT-4 (Legacy)

Model ID: gpt-4, gpt-4-0613
Pricing:

Input: $30 / milione di token
Output: $60 / milione di token


Limiti di Token:

Input: 8.192 token
Output: 4.096 token


Training Data Cutoff: Settembre 2021
Caratteristiche: Modello originale GPT-4, context limitato


⚡ Modelli GPT-3.5
GPT-3.5 Turbo

Model ID: gpt-3.5-turbo, gpt-3.5-turbo-0125
Pricing:

Input: $0.50 / milione di token
Output: $1.50 / milione di token
Batch API: $0.25 / $0.75 per milione di token


Limiti di Token:

Input: 16.384 token
Output: 4.096 token


Training Data Cutoff: Settembre 2021
Caratteristiche: Veloce, economico, ottimo per dialoghi

GPT-3.5 Turbo Instruct

Model ID: gpt-3.5-turbo-instruct
Pricing:

Input: $1.50 / milione di token
Output: $2 / milione di token


Limiti di Token:

Input: 4.096 token
Output: 4.096 token


Caratteristiche: Completion model (non chat), instructions


🎨 Modelli Specializzati
DALL-E 3

Model ID: dall-e-3
Pricing:

Standard 1024×1024: $0.040 / immagine
HD 1024×1024: $0.080 / immagine
Standard 1792×1024: $0.080 / immagine
HD 1792×1024: $0.120 / immagine



DALL-E 2

Model ID: dall-e-2
Pricing:

1024×1024: $0.020 / immagine
512×512: $0.018 / immagine
256×256: $0.016 / immagine



Whisper

Model ID: whisper-1
Pricing: $0.006 / minuto
Caratteristiche: Speech-to-text, transcription, translation

TTS (Text-to-Speech)

Model ID: tts-1, tts-1-hd
Pricing:

TTS-1: $15 / milione di caratteri
TTS-1-HD: $30 / milione di caratteri


Voci: alloy, echo, fable, onyx, nova, shimmer

Embeddings

Model ID: text-embedding-3-small, text-embedding-3-large
Pricing:

Small: $0.020 / milione di token
Large: $0.130 / milione di token


Dimensioni: Small (1536), Large (3072)


🛠️ Funzionalità e Pricing Aggiuntivi
Prompt Caching

GPT-4.1: 75% sconto (1/4 del prezzo) se riusato entro 5-10 min
GPT-4o: 50% sconto su input tokens cached
Cache TTL: ~5-10 minuti

Batch API

Sconto: 50% su tutti i modelli
Tempo: Completamento entro 24 ore
Ideale per: Processing bulk, non time-sensitive

Fine-tuning

Modelli supportati: GPT-4o mini, GPT-3.5 Turbo
Training: $8 / milione di token
Usage: Pricing del modello base + premium

Assistants API

Code Interpreter: $0.03 / sessione
Retrieval: $0.20 / GB / assistant / giorno
Tools: Pricing standard del modello


📊 Rate Limits e Tiers
Tier System

Tier 1: Nuovi utenti ($5+ spesi)
Tier 2: $50+ spesi
Tier 3: $100+ spesi
Tier 4: $500+ spesi
Tier 5: $1000+ spesi

Typical Limits (Tier 3)

GPT-4o: 30,000 RPM, 10M TPM
GPT-4o mini: 30,000 RPM, 200M TPM
o1: 500 RPM, 10M TPM
o3-mini: Solo Tier 3+


🚨 Modelli Deprecati/In Rimozione
❌ In Deprecazione:

GPT-4.5 Preview: Rimosso il 14 luglio 2025
GPT-4 (versioni vecchie): Graduale phase-out

❌ Non più disponibili:

GPT-3 (davinci, curie, babbage, ada)
InstructGPT
Codex


💰 Informazioni Pricing
Calcolo Token

1 token ≈ 4 caratteri o 0.75 parole in inglese
1.000 token ≈ 750 parole
1 milione di token ≈ 750.000 parole

Best Value Models

GPT-4.1 nano: $0.10/$0.40 (più economico)
GPT-4o mini: $0.15/$0.60 (multimodal)
o3-mini: $1.10/$4.40 (reasoning)
GPT-3.5 Turbo: $0.50/$1.50 (veloce)

Sconti Disponibili

Batch API: -50% su tutti i modelli
Prompt Caching: fino a -75% sui token cached
Enterprise: Sconti volume personalizzati