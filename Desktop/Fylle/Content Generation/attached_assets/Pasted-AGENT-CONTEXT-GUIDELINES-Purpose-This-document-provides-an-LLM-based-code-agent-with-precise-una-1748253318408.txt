AGENT CONTEXT & GUIDELINES
Purpose: This document provides an LLM-based code agent with precise, unambiguous guidance on how to locate, modify, and respect the overall logic and path structure of the codebase. Follow strictly; do not take initiative beyond these instructions.

1. Base Configuration
Root Directory: /app (or the project root).

Allowed Working Directories:

/app/src/
/app/workflows/
/app/profiles/
/app/agents/
/app/tests/
/app/scripts/
Forbidden Areas:

System folders: /app/.git/, /app/.github/
Backup files: any *.bak
Output folder: /app/output/ (read-only)
All paths must be handled relative to the root. Do not hardcode absolute paths.

2. File Structure Overview
Use this map to quickly locate modules:

/app
├─ src/
│  ├─ agents.py
│  ├─ agent_meta.py
│  ├─ profiles.py
│  ├─ tools.py
│  ├─ tasks.py
│  ├─ orchestrator.py
│  ├─ workflow_factory.py
│  ├─ workflow_builder.py
│  ├─ workflow_introspection.py
│  ├─ deepseek_api.py
│  ├─ api_health.py
│  ├─ diagnostics.py
│  ├─ reporting.py
│  └─ utils.py
├─ workflows/
│  └─ <workflow_name>.py
├─ profiles/
│  └─ <profile_name>/...
├─ agents/
│  └─ <profile_name>/...
├─ tests/
│  └─ test_*.py
├─ scripts/
│  └─ *.py
├─ requirements.txt
├─ Dockerfile
└─ README.md
3. How to Locate a File for Modification
Identify the feature: Determine the module responsible (e.g. UI vs orchestration vs profile handling).

Map feature → folder:

UI (Streamlit): /app/src/workflow_builder.py, /app/src/profiles.py, /app/app.py
Orchestrator: /app/src/orchestrator.py
Workflow discovery: /app/src/workflow_factory.py
Diagnostics & tests: /app/src/diagnostics.py, /app/src/test_diag.py
Search by class or function name: Use exact filename or function definitions.

Confirm file path: Before editing, verify via root-relative path.

Example: To adjust the health check logic, open /app/src/api_health.py and /app/src/diagnostics.py.

4. Path Handling Guidelines
Always use os.path.join(BASE_DIR, ...) or Python pathlib with Path(__file__).parent.
Never write: open("src/orchestrator.py"). Use relative base: open(os.path.join(BASE_DIR, "src", "orchestrator.py")).
Respect environment variables only if already defined in config.py.
5. Modification Rules
Do:
Document at top of file: what changed and why.
Write minimal diffs—affect only the targeted functions.
Run existing tests under /app/tests/.
Maintain coding style (PEP8, docstrings).
Don't:
Introduce new top-level dependencies without CI approval.
Modify files outside allowed directories.
Assume path separators; use pathlib for OS-agnostic code.
Change global logic without updating related introspection or reporting modules.
6. General Logic & Relationships
AgentsFactory (src/agents.py) ↔ agent_meta: agent definitions.
WorkflowFactory (src/workflow_factory.py) ↔ workflow_introspection: dynamic loading & docs.
Orchestrator (src/orchestrator.py): core engine—handles retries, state tracing.
UI (app.py, workflow_builder.py, profiles.py): read-only guidance here, actual logic in src/.
Any change affecting one module often requires updates in its paired module (e.g., updating a task in tasks.py may require adjusting introspection in workflow_introspection.py).