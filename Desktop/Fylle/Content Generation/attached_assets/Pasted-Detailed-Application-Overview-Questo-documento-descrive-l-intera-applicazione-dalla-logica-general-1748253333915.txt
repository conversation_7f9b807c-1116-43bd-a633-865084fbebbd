Detailed Application Overview

Questo documento descrive l'intera applicazione, dalla logica generale di funzionamento fino ai dettagli di ciascun componente frontend e backend. Utile per sviluppatori, manutentori e agenti LLM.

Visione Generale
L'app è una piattaforma modulare per la generazione di contenuti basata su LLM, con interfaccia CLI e UI web (Streamlit). Principali capacità:

Configurazione profili agenti: definizione di comportamenti specializzati (copywriter, fact-checker, etc.)

Creazione e gestione workflow: pipeline di attività (task) assegnabili a agenti

Esecuzione orchestrata: core engine che coordina agenti, task e tool

Diagnostica e monitoraggio: health-check, test di integrazione e reporting

Generazione contenuti: input topic/contesto, output in Markdown organizzato

Flusso End-to-End

Setup: definizione profili agenti (profiles/, agents/) e workflow (workflows/)

UI/CLI: scelta del profilo, workflow e parametri LLM

Esecuzione: orchestrator.run() chiama sequenza di task defini ti

Output: documenti Markdown in output/

Diagnostica: monitoraggio runtime, test e report

Architettura dei Componenti
2.1 Backend (cartella src/)

Modulo

Ruolo

agents.py

AgentsFactory: crea istanze agenti basandosi sui profili YAML e metadata

agent_meta.py

Validazione e gestione metadata dei profili agenti

profiles.py

Discovery, caricamento e CRUD dei profili in profiles/

tools.py

Registrazione e factory di tool (web search, RAG, markdown)

tasks.py

Definizione e registrazione di task elementari

config.py

Configurazioni centralizzate: API keys, default LLM parameters

orchestrator.py

Motore centrale: esecuzione flussi, gestione retry, stato e error handling

workflow_factory.py

Discovery dinamica workflow e validazione parametri

workflow_builder.py

Generazione dinamica di file workflow da UI

workflow_introspection.py

Analisi dei workflow: estrazione docstring, sequenza task, generazione overview

deepseek_api.py

Integrazione provider alternative LLM (DeepseekChatAPI)

api_health.py

Health-check provider LLM e tool esterni

diagnostics.py

Monitoraggio performance, metriche di task/tool e supporto test diagnostici

reporting.py

Generazione report post-exec (Markdown, CSV)

utils.py

Funzioni di supporto generiche: logging, timing, gestione path

2.2 Frontend (Streamlit UI & CLI)

Interfaccia

Descrizione

CLI (main.py)

Parsifica argomenti, sezione workflow, instanzia orchestrator e agenti

App UI (app.py)

Layout Streamlit: barra tabs, routing verso componenti (Generazione, Sistema, Diagnostica, Building, Workflow, Builder)

Streamlit Components

Funzioni contenute in src/workflow_builder.py, src/profiles.py, src/diagnostics.py

Logica di Funzionamento
3.1 Setup Profili e Workflow

Profili: file YAML in profiles//agent_name.yaml; validati da agent_meta, caricati da profiles.py.

Workflow: file .py in workflows/; esportano create_workflow(topic, agents, tools, config); scoperti dinamicamente da workflow_factory.

3.2 Esecuzione Generazione Contenuti

Utente seleziona profilo e workflow nella tab Generazione

Viene generato GenerationRequest e passato a orchestrator.run()

Orchestrator:

Carica workflow tramite workflow_factory

Itera step: a. Prepara contesto e chiama agent con prompt costruito da tasks.py + tools.py b. Gestisce retry/exceptions c. Registra output parziale in memoria

Al termine, unisce output in unico Markdown e lo salva in output/

3.3 Diagnostica e Monitoring

Tab Sistema: chiama api_health.check_all(), visualizza status e latenza (via diagnostics)

Tab Diagnostica: esegue test_diag.run_integration_tests(), mostra risultati tabellati via Pandas

Reporting: reporting genera log file e CSV per analisi offline

3.4 Costruzione e Modifica

Tab Building: form per creare/modificare profili agenti → invoca profiles.create/update

Tab Workflow Builder: form dinamica per assemblare sequenza task → workflow_builder.generate_workflow_py() crea file Python sotto workflows/

Tab Workflow: overview dettagliata dei workflow esistenti tramite introspezione

Diagramma dei Flussi (Pseudo-Code)
CLI/UI Entry
request = parse_input() # main.py o app.py orchestrator = Orchestrator(config) workflow = workflow_factory.load(request.workflow_name) output = orchestrator.run(workflow, request) save(output, 'output/...')

Health Check
statuses = api_health.check_all() metrics = diagnostics.collect_health_metrics(statuses) display(metrics)

Profile CRUD
profiles = profiles.list() profile = profiles.get(name) profile.update(new_config) profiles.save(profile)

Considerazioni Finali
L'app è modulare: ogni cartella/modulo ha responsabilità chiare

Rispetto percorsi e coerenza tra UI e backend sono fondamentali

Aggiornare sempre workflow_introspection e reporting se si modificano task/workflow

Fine del documento di spiegazione dettagliata dell'app

