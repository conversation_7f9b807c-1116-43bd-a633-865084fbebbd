from crewai import Task

def create_workflow(topic, agents, tools, config):
    return {
        "name": "Siebert Article",
        "description": "PED",
        "agents": {
        "web_searcher": agents["web_searcher"],
        "copywriter": agents["copywriter"],
        "rag_specialist": agents["rag_specialist"]
        },
        "tasks": [
            Task(agent=agents["web_searcher"], description='Task Name: Web Research\nDescription: Research the given topic thoroughly. Search the web for relevant, up-to-date information focusing on {{topic}}. Consider {{context}} while gathering information. Target audience: {{target_audience}}.', expected_output='Expected Output: A comprehensive research document in markdown format containing:\n- Key findings and insights\n- Relevant statistics and data\n- Current trends\n- Expert opinions and quotes\n- Sources and references'),
            Task(agent=agents["rag_specialist"], description="\nUsing the {{Web Research}}  summary about {{topic}} as a foundation, analyze and integrate relevant knowledge from {{client_name}}'s knowledge base. Key objectives:\n- Review and understand the web research summary\n- Extract relevant client-specific information that complements or enriches the web findings\n- Ensure alignment between external research and internal knowledge\n- Apply brand voice and terminology guidelines consistently\n", expected_output='Expected Output: A comprehensive knowledge synthesis in markdown format containing:\n- Summary of key web research findings\n- Relevant client-specific information and insights\n- Integration points between external and internal knowledge\n- Brand voice elements and terminology to incorporate\n- Recommendations for content alignment'),
            Task(agent=agents["copywriter"], description="\nCreate engaging content about {{topic}} based on {{RAG Knowledge Integration}}. Key objectives:\n- Review and understand both the web research findings and RAG knowledge synthesis\n- Create content that blends external research with client-specific insights\n- Follow {{client_name}}'s brand voice and style guidelines\n- Structure content for maximum impact and engagement\n- Ensure seamless integration of all sources\nExpected Output: A polished, publication-ready article in markdown format featuring:\n- Engaging introduction incorporating key research findings\n- Well-structured main sections blending web research and client knowledge\n- Strategic use of client terminology and brand voice\n- Clear conclusions that align with both research and brand guidelines\n- SEO-optimized content structure", expected_output='Expected Output: A comprehensive knowledge synthesis in markdown format containing:\n- Summary of key web research findings\n- Relevant client-specific information and insights\n- Integration points between external and internal knowledge\n- Brand voice elements and terminology to incorporate\n- Recommendations for content alignment')
        ],
        "tools": tools
    }

get_workflow = create_workflow
