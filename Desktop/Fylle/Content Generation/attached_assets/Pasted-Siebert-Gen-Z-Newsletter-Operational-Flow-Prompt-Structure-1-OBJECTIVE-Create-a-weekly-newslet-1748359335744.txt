<PERSON><PERSON>ert Gen Z Newsletter – Operational Flow & Prompt Structure

1. OBJECTIVE
Create a weekly newsletter and supporting blog articles for <PERSON><PERSON><PERSON>, targeted at Gen Z. The aim is to deliver curated financial and lifestyle content that informs, empowers, and reflects <PERSON><PERSON><PERSON>’s tone of voice: empowering, inclusive, and aspirational. The newsletter should help Gen Z feel financially savvy and confident, while the blog articles offer deeper but still accessible insight.

2. CONTENT SOURCES (CURATED LIST)
Trusted source list used for weekly content intake and scraping:
Finimize – concise explainers and financial trends


Morning Brew – Markets – quick summaries, Gen Z tone


The Daily Upside – sharp daily analysis


The Hustle – News – startup/economy/business with culture-driven slant


Money with Katie – personal finance insights and lifestyle budgeting with Gen Z-friendly tone


Chartr – visual data insights (Instagram scraping)


Axios Markets – smart brevity, economic signal focus


WSJ Noted – Gen Z-focused WSJ section (premium)


Robinhood Snacks – snackable news for investors



3. NAMING PROPOSALS (FOR CLIENT APPROVAL)
Section
Option A
Option B
Option C
Trend
Trend of the Week
What’s Happening
Financial Signals
Guide
Smart Money Guide
Wealth 101
Your Finance, Simplified
Story
Young Money Story
GenW Spotlight
Trailblazer of the Week
Links
Quick Links
This Week’s Scroll
Things Worth Reading


4. TIMELINE (WEEKLY FLOW)
Phase
When (Time Zone: IT / ET)
Content scraping & tagging
Mon → Thu AM (Italian time)
Editorial curation
Thu PM (Italian time)
Client review & feedback
Fri AM (ET) / Fri PM (IT)
Finalization & publication
Fri PM (Italian time)


5. PROMPT CHAIN (INTERNAL USE)
Prompt A – Automated Scraping Agent
You are a scraping assistant. Your task is to scan and extract all the available news headlines, summaries, and visual content from the following websites and/or PDF files (if available in homepage or linked in posts):
https://www.finimize.com/


https://www.morningbrew.com/markets


https://www.thedailyupside.com/


https://thehustle.co/news


https://moneywithkatie.com/


https://www.instagram.com/chartrdaily/ (extract last chart + caption)


https://www.axios.com/newsletters/axios-markets


https://www.wsj.com/news/noted


https://snacks.robinhood.com/


For each piece of content you find:
Navigate the homepage and linked article sections


If the content is in PDF (e.g. downloadable newsletter/briefing), extract the relevant text content


Return structured fields:


title


source


original link


published date


snippet (intro or summary, max 50 words)


image (if available)


tag suggestion (Trend / Guide / Story)


Reject content related to:
Cryptocurrency


Institutional-only financial advice


Day trading signals or speculation



Prompt B – Newsletter Block Generator
Based on a selected article, generate a newsletter block for Siebert Gen Z readers.
1. Analyze the article: find the practical takeaway, trend, or story relevant to lifestyle and money.
2. Rewrite using Siebert tone:
Empowering, inclusive, aspirational


Friendly expert, not overly formal


Max 120 words


3. Format your output as:
Title (max 9 words)


Body


Visual suggestion (optional)


CTA


Disclaimer: "This content is for informational purposes only and does not constitute financial advice."



Prompt C – Blog Article Expansion
Take the above newsletter block and expand it into a blog article (~400 words) with the following structure:
Title


Intro (hook or relatable setup)


The Insight (clearly explain the core point)


Why It Matters (context for Gen Z’s lifestyle/money goals)


Takeaway (actionable or reflective)


Link to source


Disclaimer: "This blog is for informational purposes only and does not constitute financial advice."



Last updated: May 2025
