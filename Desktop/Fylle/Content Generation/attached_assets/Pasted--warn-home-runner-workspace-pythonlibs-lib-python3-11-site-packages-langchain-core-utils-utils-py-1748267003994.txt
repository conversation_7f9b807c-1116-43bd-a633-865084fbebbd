 warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! top_p is not default parameter.
                top_p was transferred to model_kwargs.
                Please confirm that top_p is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! frequency_penalty is not default parameter.
                frequency_penalty was transferred to model_kwargs.
                Please confirm that frequency_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! presence_penalty is not default parameter.
                presence_penalty was transferred to model_kwargs.
                Please confirm that presence_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! top_p is not default parameter.
                top_p was transferred to model_kwargs.
                Please confirm that top_p is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! frequency_penalty is not default parameter.
                frequency_penalty was transferred to model_kwargs.
                Please confirm that frequency_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! presence_penalty is not default parameter.
                presence_penalty was transferred to model_kwargs.
                Please confirm that presence_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! top_p is not default parameter.
                top_p was transferred to model_kwargs.
                Please confirm that top_p is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! frequency_penalty is not default parameter.
                frequency_penalty was transferred to model_kwargs.
                Please confirm that frequency_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! presence_penalty is not default parameter.
                presence_penalty was transferred to model_kwargs.
                Please confirm that presence_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! top_p is not default parameter.
                top_p was transferred to model_kwargs.
                Please confirm that top_p is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! frequency_penalty is not default parameter.
                frequency_penalty was transferred to model_kwargs.
                Please confirm that frequency_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! presence_penalty is not default parameter.
                presence_penalty was transferred to model_kwargs.
                Please confirm that presence_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! top_p is not default parameter.
                top_p was transferred to model_kwargs.
                Please confirm that top_p is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! frequency_penalty is not default parameter.
                frequency_penalty was transferred to model_kwargs.
                Please confirm that frequency_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! presence_penalty is not default parameter.
                presence_penalty was transferred to model_kwargs.
                Please confirm that presence_penalty is what you intended.
  warnings.warn(
 [DEBUG]: == Working Agent: You are a RAG Knowledge Specialist.
 [INFO]: == Starting Task: Recupera il contenuto del documento '[Base per Workflow]_BRAND MANUAL – Fylle.md' dalla knowledge base del cliente 'Fylle'. Se il documento non viene trovato, mostra la lista dei documenti disponibili.
 

I encountered an error while trying to use the tool. This was the error: get_available_tools.<locals>.client_rag_content_wrapper() takes from 0 to 1 positional arguments but 2 were given.
 Tool RAG Content Retriever accepts these inputs: Retrieves content from the RAG directory for client 'Siebert'.

 

I encountered an error while trying to use the tool. This was the error: get_available_tools.<locals>.client_rag_content_wrapper() takes from 0 to 1 positional arguments but 2 were given.
 Tool RAG Content Retriever accepts these inputs: Retrieves content from the RAG directory for client 'Siebert'.

2025-05-26 13:42:16.438 Uncaught app exception
Traceback (most recent call last):
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/script_runner.py", line 542, in _run_script
    exec(code, module.__dict__)
  File "/home/<USER>/workspace/FylleCGS/app.py", line 561, in <module>
    main()
  File "/home/<USER>/workspace/FylleCGS/app.py", line 268, in main
    selected_tools = st.multiselect(
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/metrics_util.py", line 397, in wrapped_func
    result = non_optional_func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/widgets/multiselect.py", line 249, in multiselect
    return self._multiselect(
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/widgets/multiselect.py", line 292, in _multiselect
    indices = _check_and_convert_to_indices(opt, default)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/widgets/multiselect.py", line 92, in _check_and_convert_to_indices
    raise StreamlitAPIException(
streamlit.errors.StreamlitAPIException: Every Multiselect default value must exist in options
 


Error executing tool. Co-worker mentioned not found, it must to be one of the following options:
- you are a copywriter.


 

I encountered an error while trying to use the tool. This was the error: get_available_tools.<locals>.client_rag_content_wrapper() takes from 0 to 1 positional arguments but 2 were given.
 Tool RAG Content Retriever accepts these inputs: Retrieves content from the RAG directory for client 'Siebert'.

 [DEBUG]: == [You are a RAG Knowledge Specialist.] Task output: I attempted to retrieve the document '[Base per Workflow]_BRAND MANUAL – Fylle.md' from the RAG directory for the client 'Siebert', as specified in the available tools. However, I encountered a consistent error indicating a mismatch in the required parameters or configurations of the tool. Unfortunately, due to these constraints and errors, I am unable to retrieve the document or list the available documents directly. I recommend verifying the client name and tool configuration to align with the task requirements correctly.


 [DEBUG]: == Working Agent: You are a Copywriter.
 [INFO]: == Starting Task: Leggi il contenuto del documento '[Base per Workflow]_BRAND MANUAL – Fylle.md' e produci un riassunto chiaro e conciso dei punti chiave.
 

I encountered an error while trying to use the tool. This was the error: 'NoneType' object has no attribute 'startswith'.
 Tool Ask question to co-worker accepts these inputs: Ask question to co-worker(question: str, context: str, coworker: Optional[str] = None, **kwargs) - Ask a specific question to one of the following co-workers: [You are a RAG Knowledge Specialist.]
The input to this tool should be the co-worker, the question you have for them, and ALL necessary context to ask the question properly, they know nothing about the question, so share absolute everything you know, don't reference things but instead explain them.

 

I encountered an error while trying to use the tool. This was the error: 'NoneType' object has no attribute 'startswith'.
 Tool Delegate work to co-worker accepts these inputs: Delegate work to co-worker(task: str, context: str, coworker: Optional[str] = None, **kwargs) - Delegate a specific task to one of the following co-workers: [You are a RAG Knowledge Specialist.]
The input to this tool should be the co-worker, the task you want them to do, and ALL necessary context to execute the task, they know nothing about the task, so share absolute everything you know, don't reference things but instead explain them.

 [DEBUG]: == [You are a Copywriter.] Task output: Unfortunately, due to repeated tool errors and configuration mismatches, I am unable to access the document '[Base per Workflow]_BRAND MANUAL – Fylle.md' for the client 'Siebert'. This prevents me from producing a summary or providing the content of the document as requested. I recommend verifying the client name, document path, and tool configurations with the appropriate technical team to resolve these access issues and proceed with the task correctly.


/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! top_p is not default parameter.
                top_p was transferred to model_kwargs.
                Please confirm that top_p is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! frequency_penalty is not default parameter.
                frequency_penalty was transferred to model_kwargs.
                Please confirm that frequency_penalty is what you intended.
  warnings.warn(
/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/langchain_core/utils/utils.py:161: UserWarning: WARNING! presence_penalty is not default parameter.
                presence_penalty was transferred to model_kwargs.
                Please confirm that presence_penalty is what you intended.
  warnings.warn(
2025-05-26 13:42:53.324 Uncaught app exception
Traceback (most recent call last):
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/script_runner.py", line 542, in _run_script
    exec(code, module.__dict__)
  File "/home/<USER>/workspace/FylleCGS/app.py", line 561, in <module>
    main()
  File "/home/<USER>/workspace/FylleCGS/app.py", line 268, in main
    selected_tools = st.multiselect(
                     ^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/metrics_util.py", line 397, in wrapped_func
    result = non_optional_func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/widgets/multiselect.py", line 249, in multiselect
    return self._multiselect(
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/widgets/multiselect.py", line 292, in _multiselect
    indices = _check_and_convert_to_indices(opt, default)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/widgets/multiselect.py", line 92, in _check_and_convert_to_indices
    raise StreamlitAPIException(
streamlit.errors.StreamlitAPIException: Every Multiselect default value must exist in options