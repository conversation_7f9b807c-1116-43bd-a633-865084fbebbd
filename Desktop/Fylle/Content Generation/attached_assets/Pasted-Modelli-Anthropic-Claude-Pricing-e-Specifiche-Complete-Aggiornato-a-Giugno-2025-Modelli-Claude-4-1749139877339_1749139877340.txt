Modelli Anthropic Claude: Pricing e Specifiche Complete
Aggiornato a Giugno 2025
🔥 Modelli Claude 4 (Nuovi - Maggio 2025)
Claude Opus 4

Model ID: claude-opus-4-20250514 (alias: claude-4-opus)
Pricing:

Input: $15 / milione di token
Output: $75 / milione di token
Batch API: $7.50 / $37.50 per milione di token


Limiti di Token:

Input: 200.000 token
Output: 32.000 token


Training Data Cutoff: Marzo 2025
Caratteristiche: <PERSON>lo più potente, eccellente per coding complesso, ragionamento avanzato, agenti AI

Claude Sonnet 4

Model ID: claude-sonnet-4-20250514 (alias: claude-4-sonnet)
Pricing:

Input: $3 / milione di token
Output: $15 / milione di token
Batch API: $1.50 / $7.50 per milione di token


Limiti di Token:

Input: 200.000 token
Output: 64.000 token


Training Data Cutoff: Marzo 2025
Caratteristiche: Equilibrio ottimale tra performance e velocità, sostituisce Sonnet 3.7


📊 <PERSON><PERSON> 3.7
Claude Sonnet 3.7

Model ID: claude-3-7-sonnet-20241029
Pricing:

Input: $3 / milione di token
Output: $15 / milione di token
Batch API: $1.50 / $7.50 per milione di token


Limiti di Token:

Input: 200.000 token
Output: 64.000 token (128k con beta header)


Training Data Cutoff: Ottobre 2024
Caratteristiche: Supporta prompt caching, tool use token-efficient


🚀 Modelli Claude 3.5
Claude Sonnet 3.5

Model ID: claude-3-5-sonnet-20241022
Pricing:

Input: $3 / milione di token
Output: $15 / milione di token
Batch API: $1.50 / $7.50 per milione di token


Limiti di Token:

Input: 200.000 token
Output: 8.192 token


Caratteristiche: Eccellente per coding, supporta visione e PDF

Claude Haiku 3.5

Model ID: claude-3-5-haiku-20241022
Pricing:

Input: $0.80 / milione di token
Output: $4 / milione di token
Batch API: $0.40 / $2 per milione di token


Limiti di Token:

Input: 200.000 token
Output: 8.192 token


Caratteristiche: Veloce ed economico, ideale per task semplici


💎 Modelli Claude 3
Claude Opus 3

Model ID: claude-3-opus-20240229
Pricing:

Input: $15 / milione di token
Output: $75 / milione di token
Batch API: $7.50 / $37.50 per milione di token


Limiti di Token:

Input: 200.000 token
Output: 4.096 token


Caratteristiche: Modello legacy più potente, sostituito da Opus 4

Claude Haiku 3

Model ID: claude-3-haiku-20240307
Pricing:

Input: $0.25 / milione di token
Output: $1.25 / milione di token
Batch API: $0.125 / $0.625 per milione di token


Limiti di Token:

Input: 200.000 token
Output: 4.096 token


Caratteristiche: Il modello più economico disponibile


🛠️ Funzionalità e Pricing Aggiuntivi
Prompt Caching

Cache Writes: +25% del prezzo base input
Cache Hits: 90% di sconto (es. $0.30/MTok per Sonnet 4)
TTL Standard: 5 minuti
TTL Esteso: 1 ora (costo aggiuntivo)

Tool Use

Tokens aggiuntivi per sistema:

Claude 4: ~300-350 token
Claude 3.7: ~300-350 token
Claude 3.5: ~260-340 token



Batch API

Sconto: 50% su tutti i prezzi input/output
Modalità: Asincrona, ideale per grandi volumi


🚨 Modelli Deprecati
❌ Non più disponibili:

Claude 2.1 (deprecato)
Claude 2 (deprecato)
Claude Sonnet 3 (deprecato)
Claude Instant (ritirato)
Claude 1 (ritirato)


💰 Informazioni Pricing
Calcolo Token

1 token ≈ 4 caratteri o 0.75 parole in inglese
1 milione di token ≈ 750.000 parole

Sconti e Volume

Batch API: -50% su tutti i modelli
Prompt Caching: fino a -90% sui token cached
Enterprise: Sconti volume personalizzati

Rate Limits

Variano per tier di utilizzo (Tier 1-4 + Enterprise)
Contatti sales per limiti personalizzati