# Fylle AI Dashboard - Architecture Summary

## Overview

This is a complete full-stack web application built for Fylle AI, a multi-agent AI platform. The system provides a comprehensive dashboard for managing clients, knowledge bases, integrations, and AI agents with advanced workflow creation capabilities. The application uses a modern tech stack with React frontend, Express backend, and PostgreSQL database with Drizzle ORM. Features include multi-modal knowledge input, detailed workflow builders, and Apple-inspired design system.

## User Preferences

Preferred communication style: Simple, everyday language.
Layout preference: Left sidebar navigation (implemented December 2024)
Design preference: Apple-like interface with clean, professional aesthetics

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom Apple-inspired design system
- **UI Components**: Radix UI primitives with shadcn/ui components
- **State Management**: TanStack Query for server state, local state with React hooks
- **Routing**: Wouter for lightweight client-side routing
- **Build Tool**: Vite for fast development and optimized production builds

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **Runtime**: Node.js with ES modules
- **Database**: PostgreSQL with Drizzle ORM
- **File Upload**: Multer for handling multipart/form-data
- **Session Management**: Express sessions with PostgreSQL store

### Database Design
- **ORM**: Drizzle with schema-first approach
- **Tables**: Users, Clients, Knowledge Bases, Integrations, AI Agents, Workflows
- **Relationships**: Foreign key constraints between clients and their resources
- **Migrations**: Automated schema migrations via Drizzle Kit

## Key Components

### Client Management
- Multi-tenant architecture supporting multiple clients
- Client selector with role-based views (Marketing Manager, C-Level)
- Client-specific branding with custom icons and colors

### Advanced Knowledge Base System
- Multi-modal knowledge input system with 5 input methods:
  * Document upload (.md, .txt, .pdf, .docx formats)
  * AI Workflow requests (competitor research, market analysis)
  * Natural language requests (conversational prompts)
  * Q&A system (questions about existing knowledge)
  * Scheduled reports (automated recurring content)
- Categorized knowledge organization (Performance, Insights, Operations, Brand DNA)
- Real-time search capabilities
- Knowledge optimization tracking with health metrics
- 4 informative overview cards (General Info, Summary, Performance, AI Insights)

### Integration Hub
- External service connections (Google Ads, Meta Ads, LinkedIn, etc.)
- Real-time sync status monitoring with detailed tooltips
- Connection health tracking with last sync timestamps
- API usage metrics and performance indicators

### AI Agent & Workflow Management
- Multi-agent system with 8 specialized agents (Context Setter, Web Agent, Copywriter, etc.)
- Advanced workflow creation system with:
  * Task-by-task builder with agent assignment
  * Tool & API selection from 19+ available tools
  * Input/output configuration for each task
  * Estimated time tracking
  * Output channel selection (Slack, Email, Discord, Dashboard, API)
- Pre-built workflows with detailed task breakdowns:
  * Blog Post Workflow (15 min, 3 tasks)
  * Video Content Workflow (30 min, 4 tasks) 
  * Market Analysis Workflow (25 min, 4 tasks)
- Workflow detail modals showing complete execution flow
- Agent status tracking (Available, Running, Configured)
- Cross-platform channel support with tooltips

### User Interface
- Apple-inspired design with clean, minimalist aesthetics
- Left sidebar navigation for improved UX (implemented December 2024)
- Responsive design with mobile-first approach
- Activity feed with real-time updates
- Role-based UI adaptation
- Advanced modal system for workflow creation and knowledge input

## Data Flow

### Client Selection Flow
1. User selects client from dropdown
2. Application fetches client-specific data
3. UI updates to show client branding and data
4. Role-based filtering applied to displayed content

### Advanced Knowledge Input Flow
1. User clicks "Add Knowledge" to open multi-modal input system
2. Selects from 5 input methods (Document, AI Workflow, Natural Language, Q&A, Scheduled Reports)
3. Method-specific form validation and AI explanation provided
4. Data processed and integrated into knowledge base
5. UI refreshed with updated knowledge metrics

### Workflow Creation Flow
1. User clicks "Create Workflow" to open workflow builder
2. Configures workflow basics (name, description, icon, channels)
3. Adds tasks one-by-one with agent, tools, and I/O configuration
4. Real-time validation and estimated time calculation
5. Workflow saved and available for execution

### Integration Sync Flow
1. Background jobs monitor integration health
2. Sync status updated in real-time
3. UI displays connection status with visual indicators and tooltips
4. Failed syncs trigger notification system

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL database connection
- **drizzle-orm**: Type-safe database operations
- **@tanstack/react-query**: Server state management
- **@radix-ui/react-***: Accessible UI primitives
- **tailwindcss**: Utility-first CSS framework
- **express**: Web application framework
- **multer**: File upload handling

### Development Dependencies
- **vite**: Build tool and development server
- **typescript**: Static type checking
- **tsx**: TypeScript execution for development
- **esbuild**: Fast JavaScript bundler for production

## Deployment Strategy

### Development Environment
- Vite dev server for frontend with HMR
- TSX for backend development with auto-reload
- Replit integration with runtime error overlay
- Environment-specific configuration

### Production Build
- Vite build for optimized frontend bundle
- ESBuild for backend bundling with external packages
- Static file serving from Express
- Environment variable configuration for database and external services

### Database Management
- Schema-first approach with Drizzle
- Migration system for schema changes
- Connection pooling for production scalability
- Backup and recovery procedures

## Recent Changes (July 2024)

### Complete Interface Restructure - Knowledge Base & AI Agents
- **Date**: July 19, 2025
- **Change**: Major architectural restructure to simplify and focus the interface
- **Knowledge Base Simplification**:
  * Removed tab navigation (Agents, Knowledge, Summary)
  * Knowledge Base becomes single card feed with search and filtering
  * Clean card layout with key insights, status badges, and category filtering
  * 7 knowledge items across 5 categories: Competitor, Brand Book, Audience, Product/Servizio, Mission
- **AI Agents Restructure**:
  * Team-based organization now main content of AI Agents section
  * Team Content (Active): Content Setter, Web Searcher, Copywriter
  * Team Analisi (Inactive): Data Analyzer, Market Researcher, Performance Monitor
  * Enhanced team modal with two-column layout: Team Members + Active Workflows
  * Added Newsletter Creation and Blog Post Creation workflows with task breakdowns
  * Workflow details show agent assignments, progress, and task status
- **Team Modal Features**:
  * Left column: Team members, communication channels, web links, documents
  * Right column: Active workflows with detailed task tracking
  * Team performance metrics integrated in workflow column
  * Real-time workflow progress and task status indicators
- **Impact**: Streamlined user experience focused on content production workflows
- **Components**: Complete rewrite of knowledge-base.tsx and agents-overview.tsx, updated dashboard.tsx routing

### Knowledge Base Content Restructure 
- **Date**: July 19, 2025
- **Change**: Restructured knowledge base categories to align with content production needs
- **New Categories**: 
  * Competitor: Analysis of competitor strategies, campaigns, and positioning
  * Brand Book: Logo assets, typography, tone of voice, colors, and brand guidelines
  * Audience: User personas with detailed demographics and behavioral insights
  * Product/Servizio: Product/service listings with performance metrics and features
  * Mission: Company vision, values, and strategic goals
- **Impact**: More coherent knowledge organization for content creation workflows
- **Components**: Updated knowledge-base.tsx, knowledge-summary.tsx with new categories and mock data

## Previous Changes (July 2024)

### Complete Platform Enhancement
- **Date**: July 18-19, 2024
- **Change**: Comprehensive platform overhaul with advanced features and Apple-like design
- **Impact**: Transformed from basic dashboard to enterprise-grade AI workflow platform

### Design System Updates
- **Date**: July 18, 2024
- **Change**: Migrated button styling from Fylle green to professional gray/black scheme
- **Components**: Updated all buttons across Header, Sidebar, AI Agents, Integrations, and modals
- **Impact**: More professional and Apple-like aesthetic throughout the application

### Advanced Knowledge Base System
- **Date**: July 18, 2024  
- **Change**: Enhanced "Add Knowledge" to comprehensive 6-modal input system
- **Components**: Enhanced add-knowledge-modal.tsx with document upload, AI workflows, natural language, Q&A, scheduled reports, and Connect API
- **Features**: Multi-modal knowledge input with AI explanations and validation
- **Impact**: Complete knowledge management ecosystem for enterprise needs

### Content Management & Tracking
- **Date**: July 18, 2024
- **Change**: Implemented advanced content tracking in AI Agents section
- **Components**: Enhanced content.tsx with filtering, metrics, and engagement tracking
- **Features**: 6 content types with realistic engagement metrics, destination filtering, time-based analytics
- **Impact**: Comprehensive content performance monitoring and optimization

### Profile & User Management
- **Date**: July 19, 2024
- **Change**: Added complete profile sidebar with billing and user management
- **Components**: Created profile-sidebar.tsx with comprehensive user features
- **Features**: Profile management, settings, user management, billing with 4-tier Fylle plans
- **Plans**: Seed (€250), Bloom (€500), Pulse (€800), Mind (€2000) - all 12-month subscriptions
- **Impact**: Enterprise-grade user and subscription management

### Integration System Redesign
- **Date**: July 19, 2024
- **Change**: Completely restructured integrations into 4 strategic categories
- **Components**: Rebuilt integrations.tsx with new categorization system
- **Categories**: 
  * Data Source In (Google Analytics, Meta, Google Ads)
  * Data Source Out (Website, LinkedIn, Email Platform)
  * Storage (Google Drive, Dropbox, OneDrive)
  * Communication (Slack, Discord, Email)
- **Impact**: Clear data flow visualization and comprehensive workflow automation support

### Workflow Builder Enhancements
- **Date**: July 19, 2024
- **Change**: Simplified workflow creation by removing unnecessary icon field
- **Components**: Streamlined create-workflow-modal.tsx
- **Impact**: Cleaner, more focused workflow creation experience

### Context Chat System
- **Date**: July 19, 2024
- **Change**: Implemented right-click context chat functionality "Ask Fylle"
- **Components**: Created context-chat-bubble.tsx and use-context-chat.tsx hook
- **Features**: Context-aware chat bubble that appears on right-click, recognizes sections (Knowledge Base, Integrations, AI Agents), interactive AI responses
- **Impact**: Enhanced user experience with contextual AI assistance throughout the platform

### Profile Sidebar Enhancement
- **Date**: July 19, 2024
- **Change**: Restructured profile sidebar with dedicated Plans page and improved navigation
- **Components**: Enhanced profile-sidebar.tsx, created plans-page.tsx
- **Features**: 
  * Tabbed navigation within profile sidebar (Profile, Settings, Users, Billing, Plans)
  * Dedicated Plans page with detailed comparison of 4 Fylle tiers
  * Plan differentiators: Custom Plan availability, Integration limits, Agent limits, Workflow run limits
  * Current usage tracking with progress bars
  * Responsive sidebar width (800px for Plans, 384px for other sections)
- **Plans Structure**:
  * Seed: €250/12mo - 5 integrations, 3 agents, 1K runs, no custom plan
  * Bloom: €500/12mo - 15 integrations, 8 agents, 5K runs, no custom plan
  * Pulse: €800/12mo - 25 integrations, 15 agents, 15K runs, custom plan available
  * Mind: €2000/12mo - Unlimited resources, custom plan available
- **Impact**: Complete subscription management system with clear feature differentiation

The architecture emphasizes developer experience with hot reloading, type safety, and modern tooling while maintaining production-ready performance and scalability. Recent enhancements focus on advanced UI patterns and comprehensive business automation capabilities.