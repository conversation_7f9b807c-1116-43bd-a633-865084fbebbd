import React from 'react';
import { Header } from '@/components/header';
import { KnowledgeBase } from '@/components/knowledge-base';
import { Integrations } from '@/components/integrations';
import { AgentsOverview } from '@/components/agents-overview';
import { ContentsOverview } from '@/components/contents-overview';
import { ActivityFeed } from '@/components/activity-feed';
import { ContextChatBubble } from '@/components/context-chat-bubble';
import { useDashboard } from '@/hooks/use-dashboard';
import { useContextChat } from '@/hooks/use-context-chat';
import { ChevronLeft, ChevronRight } from 'lucide-react';

export default function Dashboard() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = React.useState(false);
  
  const {
    selectedClient,
    userRole,
    activeSection,
    showActivityFeed,
    clients,
    knowledgeBases,
    integrations,
    aiAgents,
    workflows,
    isLoading,
    knowledgeStats,
    integrationStats,
    agentStats,
    setActiveSection,
    selectClient,
    toggleRole,
    setShowActivityFeed,
    uploadKnowledgeBase
  } = useDashboard();

  const { chatState, closeChatBubble } = useContextChat();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-fylle-bg">
        <div className="text-center">
          <div className="w-12 h-12 bg-fylle-green rounded-lg flex items-center justify-center mb-4 mx-auto">
            <i className="fas fa-cube text-white text-lg"></i>
          </div>
          <div className="text-fylle-dark font-medium">Loading Fylle Dashboard...</div>
        </div>
      </div>
    );
  }

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'knowledge':
        return (
          <KnowledgeBase 
            knowledgeBases={knowledgeBases}
            userRole={userRole}
            knowledgeStats={knowledgeStats}
            uploadMutation={uploadKnowledgeBase}
          />
        );
      case 'integrations':
        return <Integrations integrations={integrations} />;
      case 'agents':
        return <AgentsOverview aiAgents={aiAgents} workflows={workflows} />;
      case 'contents':
        return <ContentsOverview aiAgents={aiAgents} workflows={workflows} />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-fylle-bg">
      <Header 
        clients={clients}
        selectedClient={selectedClient}
        userRole={userRole}
        onClientSelect={selectClient}
        onRoleToggle={toggleRole}
      />
      
      <div className="flex flex-1">
        {/* Left Sidebar Navigation */}
        <div className={`${isSidebarCollapsed ? 'w-16 sidebar-collapsed' : 'w-64 sidebar-expanded'} bg-white border-r border-gray-200 flex-shrink-0 sidebar-transition`}>
          <nav className="p-4">
            {/* Toggle Button */}
            <div className="mb-4 flex justify-end">
              <button
                onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-apple text-fylle-gray hover:text-fylle-dark"
                title={isSidebarCollapsed ? "Espandi sidebar" : "Comprimi sidebar"}
              >
                {isSidebarCollapsed ? (
                  <ChevronRight className="w-5 h-5" />
                ) : (
                  <ChevronLeft className="w-5 h-5" />
                )}
              </button>
            </div>

            <div className="space-y-2">
              <button
                onClick={() => setActiveSection('knowledge')}
                className={`w-full text-left px-4 py-3 rounded-lg transition-apple flex items-center ${
                  activeSection === 'knowledge'
                    ? 'bg-gray-100 text-fylle-dark font-medium'
                    : 'text-fylle-gray hover:bg-gray-50 hover:text-fylle-dark'
                } ${isSidebarCollapsed ? 'justify-center' : ''}`}
                title={isSidebarCollapsed ? "Knowledge Base" : ""}
              >
                <span className={isSidebarCollapsed ? '' : 'mr-3'}>📚</span>
                {!isSidebarCollapsed && <span className="nav-text">Knowledge Base</span>}
              </button>
              
              <button
                onClick={() => setActiveSection('integrations')}
                className={`w-full text-left px-4 py-3 rounded-lg transition-apple flex items-center ${
                  activeSection === 'integrations'
                    ? 'bg-gray-100 text-fylle-dark font-medium'
                    : 'text-fylle-gray hover:bg-gray-50 hover:text-fylle-dark'
                } ${isSidebarCollapsed ? 'justify-center' : ''}`}
                title={isSidebarCollapsed ? "Integrations" : ""}
              >
                <span className={isSidebarCollapsed ? '' : 'mr-3'}>🔗</span>
                {!isSidebarCollapsed && <span className="nav-text">Integrations</span>}
              </button>
              
              <button
                onClick={() => setActiveSection('agents')}
                className={`w-full text-left px-4 py-3 rounded-lg transition-apple flex items-center ${
                  activeSection === 'agents'
                    ? 'bg-gray-100 text-fylle-dark font-medium'
                    : 'text-fylle-gray hover:bg-gray-50 hover:text-fylle-dark'
                } ${isSidebarCollapsed ? 'justify-center' : ''}`}
                title={isSidebarCollapsed ? "AI Agents" : ""}
              >
                <span className={isSidebarCollapsed ? '' : 'mr-3'}>🤖</span>
                {!isSidebarCollapsed && <span className="nav-text">AI Agents</span>}
              </button>
              
              <button
                onClick={() => setActiveSection('contents')}
                className={`w-full text-left px-4 py-3 rounded-lg transition-apple flex items-center ${
                  activeSection === 'contents'
                    ? 'bg-gray-100 text-fylle-dark font-medium'
                    : 'text-fylle-gray hover:bg-gray-50 hover:text-fylle-dark'
                } ${isSidebarCollapsed ? 'justify-center' : ''}`}
                title={isSidebarCollapsed ? "Contents" : ""}
              >
                <span className={isSidebarCollapsed ? '' : 'mr-3'}>📄</span>
                {!isSidebarCollapsed && <span className="nav-text">Contents</span>}
              </button>
            </div>
          </nav>
        </div>
        
        {/* Main Content Area */}
        <main className="flex-1 overflow-auto custom-scrollbar bg-fylle-bg">
          {renderActiveSection()}
        </main>
      </div>

      <ActivityFeed 
        isVisible={showActivityFeed}
        onClose={() => setShowActivityFeed(false)}
      />

      {/* Context Chat Bubble */}
      {chatState.isVisible && (
        <ContextChatBubble
          x={chatState.x}
          y={chatState.y}
          contextInfo={chatState.contextInfo}
          onClose={closeChatBubble}
        />
      )}
    </div>
  );
}
