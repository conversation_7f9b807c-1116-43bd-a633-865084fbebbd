export interface Client {
  id: number;
  name: string;
  icon: string;
  color: string;
  isActive: boolean;
  createdAt: Date;
}

export interface KnowledgeBase {
  id: number;
  clientId: number;
  name: string;
  category: string;
  content: string;
  metadata?: any;
  createdAt: Date;
}

export interface Integration {
  id: number;
  clientId: number;
  name: string;
  type: string;
  status: 'connected' | 'disconnected';
  lastSync?: Date;
  metadata?: any;
}

export interface AIAgent {
  id: number;
  name: string;
  description: string;
  icon: string;
  status: 'available' | 'running' | 'configured';
  channels: string[];
  createdAt: Date;
}

export interface Workflow {
  id: number;
  name: string;
  description: string;
  steps: WorkflowStep[];
  estimatedTime: number;
  usedBy: string[];
  icon: string;
  createdAt: Date;
}

export interface WorkflowStep {
  step: number;
  name: string;
  agent: string;
  icon: string;
}

export interface MetricData {
  name: string;
  value: string;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
  details?: string;
}

export interface RecommendationData {
  priority: 'high' | 'medium' | 'low';
  icon: string;
  title: string;
  message: string;
  type: 'success' | 'warning' | 'info' | 'error';
  timestamp?: string;
  action?: string;
}

export type UserRole = 'marketing' | 'clevel';

export interface ActivityItem {
  id: string;
  icon: string;
  title: string;
  description: string;
  timestamp: string;
  type: 'performance' | 'insight' | 'operation' | 'knowledge';
}
