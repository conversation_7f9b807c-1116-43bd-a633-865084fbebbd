import { MetricData, RecommendationData, ActivityItem } from './types';

export const performanceMetrics = {
  marketing: [
    {
      name: "Google Ads",
      value: "CTR 3.2%",
      change: "↑12%",
      trend: "up" as const,
      details: "CPC €1.45 | Conversions 127"
    },
    {
      name: "Meta Ads",
      value: "CPM €8.90",
      change: "↑8%",
      trend: "up" as const,
      details: "ROAS 4.2x | Reach 45K"
    },
    {
      name: "LinkedIn Ads",
      value: "CTR 2.8%",
      change: "↑15%",
      trend: "up" as const,
      details: "CPC €2.30 | Leads 89"
    }
  ] as MetricData[],
  clevel: [
    {
      name: "Lead Quality Score",
      value: "87/100",
      change: "+4 pts",
      trend: "up" as const,
      details: "Qualified lead assessment"
    },
    {
      name: "Revenue Attribution",
      value: "€47,310",
      change: "+€7K",
      trend: "up" as const,
      details: "Direct revenue from campaigns"
    },
    {
      name: "Overall ROAS",
      value: "3.8x",
      trend: "neutral" as const,
      details: "Target: 4.0x"
    }
  ] as MetricData[]
};

export const recommendations = {
  marketing: [
    {
      priority: "high" as const,
      icon: "🔥",
      title: "High Priority",
      message: "Last 2 social posts increased ER by 32%, review PED accordingly next week",
      type: "error" as const,
      timestamp: "2 hours ago",
      action: "Review and adjust campaign settings"
    },
    {
      priority: "medium" as const,
      icon: "⚠️",
      title: "Action Needed",
      message: "Welcome flow for lead generation not generating forms for 5 days",
      type: "warning" as const,
      timestamp: "1 day ago",
      action: "Check form integration settings"
    },
    {
      priority: "low" as const,
      icon: "📈",
      title: "Opportunity",
      message: "LinkedIn content performs 40% better on Tuesday mornings",
      type: "info" as const,
      timestamp: "3 hours ago",
      action: "Schedule content for optimal times"
    }
  ] as RecommendationData[],
  clevel: [
    {
      priority: "low" as const,
      icon: "💰",
      title: "Revenue Impact",
      message: "Marketing efficiency up 23% this quarter, consider increasing budget allocation",
      type: "success" as const
    },
    {
      priority: "low" as const,
      icon: "📊",
      title: "Performance",
      message: "Customer acquisition cost down 15%, ROI improving significantly",
      type: "info" as const
    },
    {
      priority: "medium" as const,
      icon: "🚀",
      title: "Growth",
      message: "AI automation sector growing 40% YoY, time to accelerate",
      type: "info" as const
    }
  ] as RecommendationData[]
};

export const knowledgeCategories = [
  {
    id: "performance",
    name: "Performance",
    icon: "fas fa-chart-line",
    color: "border-blue-500",
    bgColor: "bg-blue-50",
    textColor: "text-blue-600",
    description: "Live advertising metrics and KPIs",
    count: 124
  },
  {
    id: "insights",
    name: "Insights",
    icon: "fas fa-lightbulb",
    color: "border-purple-500",
    bgColor: "bg-purple-50",
    textColor: "text-purple-600",
    description: "AI recommendations and analysis",
    count: 89
  },
  {
    id: "operations",
    name: "Operations",
    icon: "fas fa-cogs",
    color: "border-orange-500",
    bgColor: "bg-orange-50",
    textColor: "text-orange-600",
    description: "Current activities and workflows",
    count: 12
  },
  {
    id: "brand-dna",
    name: "Brand DNA",
    icon: "fas fa-bullseye",
    color: "border-green-500",
    bgColor: "bg-green-50",
    textColor: "text-green-600",
    description: "Core identity and positioning",
    count: 1
  }
];

export const overviewStats = [
  {
    icon: "fas fa-check-circle",
    iconColor: "text-green-600",
    iconBg: "bg-green-100",
    value: "98%",
    label: "Optimized",
    badge: "Healthy",
    badgeColor: "bg-green-100 text-green-600"
  },
  {
    icon: "fas fa-clock",
    iconColor: "text-blue-600",
    iconBg: "bg-blue-100",
    value: "2 min",
    label: "Last Update"
  },
  {
    icon: "fas fa-phone",
    iconColor: "text-purple-600",
    iconBg: "bg-purple-100",
    value: "1,254",
    label: "Total Calls",
    change: "↑12%"
  },
  {
    icon: "fas fa-network-wired",
    iconColor: "text-orange-600",
    iconBg: "bg-orange-100",
    value: "47",
    label: "Knowledge Nodes"
  }
];

export const activityFeed: ActivityItem[] = [
  {
    id: "1",
    icon: "📊",
    title: "Performance metrics updated",
    description: "LinkedIn ads +15% conversions",
    timestamp: "2 min ago",
    type: "performance"
  },
  {
    id: "2",
    icon: "💡",
    title: "New insight generated",
    description: "Social post ER increased 32%",
    timestamp: "5 min ago",
    type: "insight"
  },
  {
    id: "3",
    icon: "⚙️",
    title: "Operations update",
    description: "Blog post 'AI Automation Trends' published",
    timestamp: "15 min ago",
    type: "operation"
  },
  {
    id: "4",
    icon: "🎯",
    title: "Brand DNA enriched",
    description: "Knowledge base enriched with 3 new competitor insights",
    timestamp: "1 hour ago",
    type: "knowledge"
  }
];

export const availableIntegrations = {
  dataSourceIn: [
    { name: "Google Analytics", icon: "fab fa-google", color: "bg-orange-500", status: "connected", description: "Website analytics and user behavior data" },
    { name: "Meta", icon: "fab fa-meta", color: "bg-blue-600", status: "connected", description: "Facebook and Instagram advertising data" },
    { name: "Google Ads", icon: "fab fa-google", color: "bg-green-600", status: "available", description: "Search and display advertising performance" }
  ],
  dataSourceOut: [
    { name: "Website", icon: "fas fa-globe", color: "bg-blue-500", status: "connected", description: "Deploy content directly to your website" },
    { name: "LinkedIn", icon: "fab fa-linkedin", color: "bg-blue-700", status: "connected", description: "Publish content and manage LinkedIn presence" },
    { name: "Email Platform", icon: "fas fa-envelope", color: "bg-red-500", status: "available", description: "Send automated email campaigns" }
  ],
  storage: [
    { name: "Google Drive", icon: "fab fa-google-drive", color: "bg-blue-600", status: "connected", description: "Store and manage your files in the cloud" },
    { name: "Dropbox", icon: "fab fa-dropbox", color: "bg-blue-500", status: "available", description: "File storage and collaboration platform" },
    { name: "OneDrive", icon: "fab fa-microsoft", color: "bg-blue-700", status: "available", description: "Microsoft cloud storage solution" }
  ],
  communication: [
    { name: "Slack", icon: "fab fa-slack", color: "bg-purple-600", status: "connected", description: "Team communication and workflow notifications" },
    { name: "Discord", icon: "fab fa-discord", color: "bg-indigo-600", status: "available", description: "Community communication and updates" },
    { name: "Email", icon: "fas fa-envelope", color: "bg-red-500", status: "connected", description: "Email notifications and reports" }
  ]
};
