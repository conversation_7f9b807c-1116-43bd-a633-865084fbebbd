@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 98%);
  --foreground: hsl(29, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(29, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(29, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(142, 100%, 55%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(29, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* Fylle specific colors */
  --fylle-green: hsl(142, 100%, 55%);
  --fylle-bg: hsl(0, 0%, 98%);
  --fylle-dark: hsl(0, 0%, 11%);
  --fylle-gray: hsl(0, 0%, 53%);
  --fylle-card: hsl(0, 0%, 100%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(142, 100%, 55%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
  }
}

@layer utilities {
  .apple-shadow {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .apple-shadow-hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .transition-apple {
    transition: all 0.15s ease-out;
  }
  
  .fylle-green {
    color: var(--fylle-green);
  }
  
  .bg-fylle-green {
    background-color: var(--fylle-green);
  }
  
  .border-fylle-green {
    border-color: var(--fylle-green);
  }
  
  .text-fylle-dark {
    color: var(--fylle-dark);
  }
  
  .text-fylle-gray {
    color: var(--fylle-gray);
  }
  
  .bg-fylle-bg {
    background-color: var(--fylle-bg);
  }
  
  .bg-fylle-card {
    background-color: var(--fylle-card);
  }
  
  .bg-fylle-button {
    background-color: hsl(0, 0%, 15%);
  }
  
  .text-fylle-button {
    color: hsl(0, 0%, 100%);
  }
  
  .hover\:bg-fylle-button-hover:hover {
    background-color: hsl(0, 0%, 25%);
  }
}

/* Context Chat Animations */
@keyframes contextChatAppear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.context-chat-animate {
  animation: contextChatAppear 0.2s ease-out forwards;
}

/* Sidebar animations */
.sidebar-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-collapsed .nav-text {
  opacity: 0;
  transform: translateX(-10px);
}

.sidebar-expanded .nav-text {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.2s ease-in-out 0.1s, transform 0.2s ease-in-out 0.1s;
}

/* Knowledge graph animations */
@keyframes pulse-node {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

.knowledge-node {
  animation: pulse-node 3s ease-in-out infinite;
}

.knowledge-node:hover {
  animation-play-state: paused;
  transform: scale(1.1);
}

/* Connection line animation */
@keyframes dash-flow {
  from { stroke-dashoffset: 0; }
  to { stroke-dashoffset: 10; }
}

.connection-line {
  stroke-dasharray: 5,5;
  animation: dash-flow 2s linear infinite;
}

/* Notification slide in */
@keyframes slide-in-right {
  from { 
    transform: translateX(100%);
    opacity: 0;
  }
  to { 
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-enter {
  animation: slide-in-right 0.3s ease-out;
}

/* Activity feed animation */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.activity-item {
  animation: fade-in-up 0.4s ease-out;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(0, 0%, 95%);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(0, 0%, 80%);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(0, 0%, 70%);
}
