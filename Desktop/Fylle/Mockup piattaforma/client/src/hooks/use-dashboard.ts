import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import type { Client, KnowledgeBase, Integration, AIAgent, Workflow, UserRole } from '@/lib/types';

export function useDashboard() {
  const [selectedClientId, setSelectedClientId] = useState<number>(1);
  const [userRole, setUserRole] = useState<UserRole>('marketing');
  const [activeSection, setActiveSection] = useState<'knowledge' | 'integrations' | 'agents'>('knowledge');
  const [showActivityFeed, setShowActivityFeed] = useState(true);
  
  const queryClient = useQueryClient();

  // Clients query
  const { data: clients = [], isLoading: clientsLoading } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  // Selected client
  const selectedClient = clients.find(client => client.id === selectedClientId);

  // Knowledge bases query
  const { data: knowledgeBases = [], isLoading: kbLoading } = useQuery<KnowledgeBase[]>({
    queryKey: ['/api/clients', selectedClientId, 'knowledge-bases'],
    enabled: !!selectedClientId,
  });

  // Integrations query
  const { data: integrations = [], isLoading: integrationsLoading } = useQuery<Integration[]>({
    queryKey: ['/api/clients', selectedClientId, 'integrations'],
    enabled: !!selectedClientId,
  });

  // AI Agents query
  const { data: aiAgents = [], isLoading: agentsLoading } = useQuery<AIAgent[]>({
    queryKey: ['/api/ai-agents'],
  });

  // Workflows query
  const { data: workflows = [], isLoading: workflowsLoading } = useQuery<Workflow[]>({
    queryKey: ['/api/workflows'],
  });

  // Upload knowledge base mutation
  const uploadKnowledgeBase = useMutation({
    mutationFn: async ({ file, name, category }: { file: File; name: string; category: string }) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', name);
      formData.append('category', category);

      const response = await apiRequest(
        'POST',
        `/api/clients/${selectedClientId}/knowledge-bases`,
        formData
      );
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch knowledge bases
      queryClient.invalidateQueries({ queryKey: ['/api/clients', selectedClientId, 'knowledge-bases'] });
      
      // Show success notification
      // This would trigger a toast notification in the UI
    },
    onError: (error) => {
      console.error('Upload failed:', error);
      // Show error notification
    }
  });

  const toggleRole = () => {
    setUserRole(current => current === 'marketing' ? 'clevel' : 'marketing');
  };

  const selectClient = (clientId: number) => {
    setSelectedClientId(clientId);
    // Update the active state in clients array
    const updatedClients = clients.map(client => ({
      ...client,
      isActive: client.id === clientId
    }));
    queryClient.setQueryData(['/api/clients'], updatedClients);
  };

  // Calculate knowledge base statistics
  const knowledgeStats = {
    totalNodes: knowledgeBases.length + 47, // Base number from mock
    totalCalls: 1254,
    optimizationLevel: 98,
    lastUpdate: '2 min ago'
  };

  // Calculate integration statistics
  const integrationStats = {
    connected: integrations.filter(i => i.status === 'connected').length,
    total: integrations.length + 4, // Include available integrations
    lastSync: integrations
      .filter(i => i.lastSync)
      .sort((a, b) => new Date(b.lastSync!).getTime() - new Date(a.lastSync!).getTime())[0]?.lastSync
  };

  // Calculate agent statistics
  const agentStats = {
    available: aiAgents.filter(a => a.status === 'available').length,
    running: aiAgents.filter(a => a.status === 'running').length,
    total: aiAgents.length
  };

  return {
    // State
    selectedClientId,
    selectedClient,
    userRole,
    activeSection,
    showActivityFeed,
    
    // Data
    clients,
    knowledgeBases,
    integrations,
    aiAgents,
    workflows,
    
    // Loading states
    isLoading: clientsLoading || kbLoading || integrationsLoading || agentsLoading || workflowsLoading,
    
    // Statistics
    knowledgeStats,
    integrationStats,
    agentStats,
    
    // Actions
    setActiveSection,
    selectClient,
    toggleRole,
    setShowActivityFeed,
    uploadKnowledgeBase
  };
}
