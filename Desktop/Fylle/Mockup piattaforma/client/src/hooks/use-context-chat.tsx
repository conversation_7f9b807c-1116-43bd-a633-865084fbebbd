import { useState, useEffect, useCallback } from "react";

interface ContextChatState {
  isVisible: boolean;
  x: number;
  y: number;
  contextInfo?: string;
}

export function useContextChat() {
  const [chatState, setChatState] = useState<ContextChatState>({
    isVisible: false,
    x: 0,
    y: 0,
    contextInfo: undefined
  });

  const getContextInfo = useCallback((target: Element): string | undefined => {
    // Try to extract context information from the clicked element
    const closest = target.closest('[data-context]');
    if (closest) {
      return closest.getAttribute('data-context') || undefined;
    }

    // Check for specific component contexts
    if (target.closest('.knowledge-base-section')) {
      return "Knowledge Base";
    }
    
    if (target.closest('.integrations-section')) {
      return "Integrations";
    }
    
    if (target.closest('.ai-agents-section')) {
      return "AI Agents";
    }
    
    if (target.closest('.workflows-section')) {
      return "Workflows";
    }
    
    if (target.closest('.content-section')) {
      return "Content Management";
    }

    // Check for text content context
    const textContent = target.textContent?.trim();
    if (textContent && textContent.length > 3 && textContent.length < 100) {
      return `"${textContent}"`;
    }

    return undefined;
  }, []);

  const handleContextMenu = useCallback((event: MouseEvent) => {
    // Prevent default context menu
    event.preventDefault();
    
    // Don't show on inputs, textareas, or buttons
    const target = event.target as Element;
    if (target.matches('input, textarea, button, [role="button"], a')) {
      return;
    }

    // Get context information
    const contextInfo = getContextInfo(target);

    // Show chat bubble at click position
    setChatState({
      isVisible: true,
      x: event.clientX,
      y: event.clientY,
      contextInfo
    });
  }, [getContextInfo]);

  const closeChatBubble = useCallback(() => {
    setChatState(prev => ({ ...prev, isVisible: false }));
  }, []);

  useEffect(() => {
    // Add event listener for right-click
    document.addEventListener('contextmenu', handleContextMenu);

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, [handleContextMenu]);

  return {
    chatState,
    closeChatBubble
  };
}