import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CloudUpload, X } from 'lucide-react';
import { UseMutationResult } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  uploadMutation: UseMutationResult<any, Error, { file: File; name: string; category: string }, unknown>;
}

export function UploadModal({ isOpen, onClose, uploadMutation }: UploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [name, setName] = useState('');
  const [category, setCategory] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      const file = files[0];
      if (isValidFileType(file)) {
        setSelectedFile(file);
        if (!name) {
          setName(file.name.replace(/\.[^/.]+$/, ""));
        }
      } else {
        toast({
          title: "Invalid file type",
          description: "Please upload a .md, .txt, .pdf, or .docx file",
          variant: "destructive"
        });
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      const file = files[0];
      if (isValidFileType(file)) {
        setSelectedFile(file);
        if (!name) {
          setName(file.name.replace(/\.[^/.]+$/, ""));
        }
      } else {
        toast({
          title: "Invalid file type",
          description: "Please upload a .md, .txt, .pdf, or .docx file",
          variant: "destructive"
        });
      }
    }
  };

  const isValidFileType = (file: File) => {
    const validTypes = ['.md', '.txt', '.pdf', '.docx'];
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    return validTypes.includes(extension);
  };

  const handleUpload = async () => {
    if (!selectedFile || !name || !category) {
      toast({
        title: "Missing information",
        description: "Please fill in all fields and select a file",
        variant: "destructive"
      });
      return;
    }

    try {
      await uploadMutation.mutateAsync({ file: selectedFile, name, category });
      toast({
        title: "Success!",
        description: "Knowledge base uploaded successfully",
      });
      handleClose();
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file",
        variant: "destructive"
      });
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setName('');
    setCategory('');
    setIsDragOver(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Upload Knowledge Base
            <Button variant="ghost" size="sm" onClick={handleClose}>
              <X className="w-4 h-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* File Drop Zone */}
          <div
            className={`border-2 border-dashed rounded-xl p-8 text-center transition-apple cursor-pointer ${
              isDragOver 
                ? 'border-fylle-green bg-green-50' 
                : 'border-gray-300 hover:border-fylle-green'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <CloudUpload className="w-12 h-12 text-fylle-gray mx-auto mb-4" />
            {selectedFile ? (
              <div>
                <p className="text-fylle-dark font-medium mb-2">{selectedFile.name}</p>
                <p className="text-sm text-fylle-gray">Click to change file</p>
              </div>
            ) : (
              <div>
                <p className="text-fylle-gray mb-2">Drag and drop your knowledge base files</p>
                <p className="text-sm text-fylle-gray mb-4">or</p>
                <Button className="bg-fylle-green hover:bg-green-600">
                  Choose Files
                </Button>
                <p className="text-xs text-fylle-gray mt-2">Supports .md, .txt, .pdf, .docx</p>
              </div>
            )}
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept=".md,.txt,.pdf,.docx"
            onChange={handleFileSelect}
          />
          
          {/* Name Input */}
          <div className="space-y-2">
            <Label htmlFor="kb-name" className="text-sm font-medium text-fylle-dark">
              Knowledge Base Name
            </Label>
            <Input
              id="kb-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., Fylle AI Knowledge Base"
              className="focus:border-fylle-green"
            />
          </div>
          
          {/* Category Select */}
          <div className="space-y-2">
            <Label htmlFor="kb-category" className="text-sm font-medium text-fylle-dark">
              Category
            </Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger className="focus:border-fylle-green">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="brand-dna">Brand DNA</SelectItem>
                <SelectItem value="performance">Performance</SelectItem>
                <SelectItem value="insights">Insights</SelectItem>
                <SelectItem value="operations">Operations</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button 
              variant="outline" 
              onClick={handleClose}
              className="flex-1"
              disabled={uploadMutation.isPending}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleUpload}
              className="flex-1 bg-fylle-green hover:bg-green-600"
              disabled={uploadMutation.isPending || !selectedFile || !name || !category}
            >
              {uploadMutation.isPending ? 'Uploading...' : 'Upload'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
