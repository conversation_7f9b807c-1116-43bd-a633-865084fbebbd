import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Bell, Plus } from 'lucide-react';
import { ProfileSidebar } from './profile-sidebar';
import type { Client, UserRole } from '@/lib/types';

interface HeaderProps {
  clients: Client[];
  selectedClient?: Client;
  userRole: UserRole;
  onClientSelect: (clientId: number) => void;
  onRoleToggle: () => void;
}

export function Header({ clients, selectedClient, userRole, onClientSelect, onRoleToggle }: HeaderProps) {
  const [isProfileSidebarOpen, setIsProfileSidebarOpen] = useState(false);

  return (
    <>
      <header className="bg-fylle-card border-b border-gray-200 px-6 py-3 flex items-center justify-between">
      <div className="flex items-center space-x-6">
        {/* Fylle Logo */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-fylle-green rounded-lg flex items-center justify-center">
            <i className="fas fa-cube text-white text-sm"></i>
          </div>
          <span className="font-semibold text-xl text-fylle-dark">Fylle</span>
        </div>

        {/* Client Selector */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              className="flex items-center space-x-3 px-3 py-2 rounded-lg border border-gray-200 hover:border-blue-300 transition-apple bg-white text-sm"
            >
              {selectedClient && (
                <div className={`w-6 h-6 ${selectedClient.color} rounded flex items-center justify-center`}>
                  <i className={`${selectedClient.icon} text-white text-xs`}></i>
                </div>
              )}
              <span className="font-medium">
                {selectedClient?.name || 'Select Client'}
              </span>
              <ChevronDown className="w-4 h-4 text-fylle-gray" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 p-1">
            {clients.map((client) => (
              <DropdownMenuItem
                key={client.id}
                onClick={() => onClientSelect(client.id)}
                className={`flex items-center space-x-3 px-2 py-2 rounded-md cursor-pointer ${
                  client.isActive ? 'bg-blue-50' : ''
                }`}
              >
                <div className={`w-6 h-6 ${client.color} rounded flex items-center justify-center`}>
                  <i className={`${client.icon} text-white text-xs`}></i>
                </div>
                <div className="font-medium">{client.name}</div>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-fylle-green hover:bg-green-50 flex items-center space-x-2">
              <Plus className="w-4 h-4" />
              <span>Add New Client</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Role Switcher with Enhanced Indicator */}
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-fylle-gray flex items-center space-x-1">
            <i className="fas fa-eye text-xs"></i>
            <span>Viewing as:</span>
          </span>
          <Button 
            onClick={onRoleToggle}
            className="px-3 py-1 bg-fylle-button text-fylle-button rounded-full text-xs font-medium hover:bg-fylle-button-hover transition-apple relative group"
          >
            <span className="flex items-center space-x-1">
              <span>{userRole === 'marketing' ? 'Marketing Manager' : 'C-Level'}</span>
              <i className="fas fa-exchange-alt text-xs opacity-70"></i>
            </span>
            
            {/* Tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
              Switch to {userRole === 'marketing' ? 'C-Level' : 'Marketing Manager'} view
            </div>
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <Button variant="ghost" size="sm" className="relative p-2 text-fylle-gray hover:text-fylle-dark transition-apple">
          <Bell className="w-5 h-5" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        </Button>
        
        {/* Profile */}
        <Button 
          variant="ghost" 
          size="sm" 
          className="flex items-center space-x-2 p-1"
          onClick={() => setIsProfileSidebarOpen(true)}
        >
          <div className="w-8 h-8 bg-fylle-green rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">SA</span>
          </div>
        </Button>
      </div>
    </header>

    <ProfileSidebar 
      isOpen={isProfileSidebarOpen}
      onClose={() => setIsProfileSidebarOpen(false)}
    />
  </>
  );
}
