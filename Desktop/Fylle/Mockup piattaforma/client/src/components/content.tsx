import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Filter, Eye, Download, Share2, Calendar, FileText, Send, GitBranch, Upload, Link, Plus } from 'lucide-react';

interface ContentItem {
  id: number;
  title: string;
  type: 'blog' | 'social' | 'video' | 'email' | 'report';
  destination: string;
  agent: string;
  workflow: string;
  createdAt: string;
  status: 'published' | 'draft' | 'scheduled';
  preview: string;
  metrics?: {
    views?: number;
    engagement?: number;
    clicks?: number;
  };
}

const mockContent: ContentItem[] = [
  {
    id: 1,
    title: "Q4 Financial Performance Analysis",
    type: "blog",
    destination: "Company Blog",
    agent: "✍️ Copywriter",
    workflow: "Blog Post Workflow",
    createdAt: "2024-12-18T15:30:00Z",
    status: "published",
    preview: "Our Q4 financial results demonstrate strong growth across key metrics. Revenue increased by 23% year-over-year, driven by strategic initiatives in digital transformation and client acquisition...",
    metrics: { views: 1247, engagement: 8.5, clicks: 89 }
  },
  {
    id: 2,
    title: "Social Media Campaign - Holiday Promotion",
    type: "social",
    destination: "LinkedIn, Twitter",
    agent: "📱 Social Agent",
    workflow: "Social Campaign Workflow",
    createdAt: "2024-12-18T10:15:00Z",
    status: "scheduled",
    preview: "🎄 This holiday season, we're grateful for our incredible clients and partners. Special offers on our premium services - limited time only! #HolidaySpecial #ClientAppreciation",
    metrics: { views: 523, engagement: 12.3, clicks: 45 }
  },
  {
    id: 3,
    title: "Market Analysis Report - Investment Trends",
    type: "report",
    destination: "Client Portal",
    agent: "📊 Research Agent",
    workflow: "Market Analysis Workflow",
    createdAt: "2024-12-17T14:45:00Z",
    status: "published",
    preview: "Executive Summary: Investment trends for Q1 2025 show increased interest in ESG funds and technology sectors. Key findings include 15% growth in sustainable investing...",
    metrics: { views: 342, engagement: 15.7, clicks: 67 }
  },
  {
    id: 4,
    title: "Product Demo Video Script",
    type: "video",
    destination: "YouTube, Website",
    agent: "🎬 Video Agent",
    workflow: "Video Content Workflow",
    createdAt: "2024-12-17T09:20:00Z",
    status: "draft",
    preview: "Welcome to Fylle AI - where intelligent automation meets business excellence. In this demo, we'll show you how our multi-agent platform transforms your workflow...",
    metrics: { views: 0, engagement: 0, clicks: 0 }
  },
  {
    id: 5,
    title: "Weekly Newsletter - Industry Insights",
    type: "email",
    destination: "Email Subscribers",
    agent: "✉️ Email Agent",
    workflow: "Newsletter Workflow",
    createdAt: "2024-12-16T16:00:00Z",
    status: "published",
    preview: "This week in financial services: AI adoption reaches new heights, regulatory updates from the SEC, and emerging trends in digital banking...",
    metrics: { views: 2156, engagement: 18.9, clicks: 234 }
  },
  {
    id: 6,
    title: "Client Success Story - TechCorp Implementation",
    type: "blog",
    destination: "Company Blog",
    agent: "✍️ Copywriter", 
    workflow: "Case Study Workflow",
    createdAt: "2024-12-15T11:30:00Z",
    status: "published",
    preview: "How TechCorp achieved 40% efficiency gains with Fylle AI. From manual processes to intelligent automation, this case study reveals the transformation journey...",
    metrics: { views: 867, engagement: 22.1, clicks: 156 }
  }
];

const destinations = ["All Destinations", "Company Blog", "LinkedIn", "Twitter", "YouTube", "Website", "Client Portal", "Email Subscribers"];
const agents = ["All Agents", "✍️ Copywriter", "📱 Social Agent", "📊 Research Agent", "🎬 Video Agent", "✉️ Email Agent"];
const workflows = ["All Workflows", "Blog Post Workflow", "Social Campaign Workflow", "Market Analysis Workflow", "Video Content Workflow", "Newsletter Workflow", "Case Study Workflow"];

export function Content() {
  const [searchQuery, setSearchQuery] = useState('');
  const [destinationFilter, setDestinationFilter] = useState('All Destinations');
  const [agentFilter, setAgentFilter] = useState('All Agents');
  const [workflowFilter, setWorkflowFilter] = useState('All Workflows');
  const [dateFilter, setDateFilter] = useState('last-7-days');

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'blog': return '📝';
      case 'social': return '📱';
      case 'video': return '🎬';
      case 'email': return '✉️';
      case 'report': return '📊';
      default: return '📄';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-600';
      case 'draft': return 'bg-gray-100 text-gray-600';
      case 'scheduled': return 'bg-blue-100 text-blue-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const filteredContent = mockContent.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.preview.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDestination = destinationFilter === 'All Destinations' || item.destination.includes(destinationFilter);
    const matchesAgent = agentFilter === 'All Agents' || item.agent === agentFilter;
    const matchesWorkflow = workflowFilter === 'All Workflows' || item.workflow === workflowFilter;
    
    // Date filter (last 7 days is default)
    const itemDate = new Date(item.createdAt);
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const matchesDate = dateFilter === 'all-time' || itemDate >= sevenDaysAgo;
    
    return matchesSearch && matchesDestination && matchesAgent && matchesWorkflow && matchesDate;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-semibold text-fylle-dark">Content</h1>
        </div>

        {/* Content Creation Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {/* Blog Post Card */}
          <BlogPostCard />
          
          {/* Newsletter Card */}
          <NewsletterCard />
          
          {/* Versioning Card */}
          <VersioningCard />
        </div>

        {/* Filters Section */}
        <div className="bg-white rounded-xl p-6 apple-shadow mb-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-fylle-gray w-4 h-4" />
              <Input
                placeholder="Search content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Destination Filter */}
            <Select value={destinationFilter} onValueChange={setDestinationFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Destination" />
              </SelectTrigger>
              <SelectContent>
                {destinations.map((destination) => (
                  <SelectItem key={destination} value={destination}>
                    {destination}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Agent Filter */}
            <Select value={agentFilter} onValueChange={setAgentFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Agent" />
              </SelectTrigger>
              <SelectContent>
                {agents.map((agent) => (
                  <SelectItem key={agent} value={agent}>
                    {agent}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Workflow Filter */}
            <Select value={workflowFilter} onValueChange={setWorkflowFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Workflow" />
              </SelectTrigger>
              <SelectContent>
                {workflows.map((workflow) => (
                  <SelectItem key={workflow} value={workflow}>
                    {workflow}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Date Filter */}
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="last-7-days">Last 7 days</SelectItem>
                <SelectItem value="last-30-days">Last 30 days</SelectItem>
                <SelectItem value="all-time">All time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContent.map((item) => (
            <div key={item.id} className="bg-white rounded-xl p-6 apple-shadow hover:apple-shadow-hover transition-apple cursor-pointer">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span className="text-lg">{getTypeIcon(item.type)}</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-fylle-dark line-clamp-2">{item.title}</h3>
                  </div>
                </div>
                <Badge className={`text-xs ${getStatusColor(item.status)}`}>
                  {item.status}
                </Badge>
              </div>

              {/* Preview */}
              <p className="text-sm text-fylle-gray line-clamp-3 mb-4">
                {item.preview}
              </p>

              {/* Metadata */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-xs text-fylle-gray">
                  <span>Destination:</span>
                  <span className="font-medium">{item.destination}</span>
                </div>
                <div className="flex items-center justify-between text-xs text-fylle-gray">
                  <span>Agent:</span>
                  <span className="font-medium">{item.agent}</span>
                </div>
                <div className="flex items-center justify-between text-xs text-fylle-gray">
                  <span>Workflow:</span>
                  <span className="font-medium">{item.workflow}</span>
                </div>
                <div className="flex items-center justify-between text-xs text-fylle-gray">
                  <span>Created:</span>
                  <span className="font-medium">{formatDate(item.createdAt)}</span>
                </div>
              </div>

              {/* Metrics */}
              {item.metrics && item.status === 'published' && (
                <div className="border-t pt-3 mb-4">
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div>
                      <div className="text-sm font-semibold text-fylle-dark">{item.metrics.views}</div>
                      <div className="text-xs text-fylle-gray">Views</div>
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-fylle-dark">{item.metrics.engagement}%</div>
                      <div className="text-xs text-fylle-gray">Engagement</div>
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-fylle-dark">{item.metrics.clicks}</div>
                      <div className="text-xs text-fylle-gray">Clicks</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1 text-xs">
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
                <Button variant="outline" size="sm" className="flex-1 text-xs">
                  <Download className="w-3 h-3 mr-1" />
                  Export
                </Button>
                <Button variant="outline" size="sm" className="text-xs">
                  <Share2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>


      </div>
    </div>
  );
}

// Blog Post Card Component
function BlogPostCard() {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    topic: '',
    additionalDocument: null as File | null,
    linkReference: '',
    wordCount: '',
    extra: '',
    channelDraft: ''
  });

  const handleSubmit = () => {
    console.log('Blog Post Form Data:', formData);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className="bg-fylle-card rounded-lg p-6 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <h3 className="text-lg font-semibold text-fylle-dark text-center mb-2">Blog Post</h3>
          <p className="text-sm text-fylle-gray text-center">Crea contenuti per il blog con AI</p>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Crea Blog Post</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="topic">Topic</Label>
            <Input
              id="topic"
              maxLength={50}
              value={formData.topic}
              onChange={(e) => setFormData({...formData, topic: e.target.value})}
              placeholder="Argomento del blog post (max 50 caratteri)"
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.topic.length}/50 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="additional-doc">Additional Document</Label>
            <Input
              id="additional-doc"
              type="file"
              onChange={(e) => setFormData({...formData, additionalDocument: e.target.files?.[0] || null})}
            />
          </div>
          
          <div>
            <Label htmlFor="link-ref">Link Reference</Label>
            <Input
              id="link-ref"
              type="url"
              value={formData.linkReference}
              onChange={(e) => setFormData({...formData, linkReference: e.target.value})}
              placeholder="https://esempio.com"
            />
          </div>
          
          <div>
            <Label htmlFor="word-count">Word Count</Label>
            <Input
              id="word-count"
              type="number"
              value={formData.wordCount}
              onChange={(e) => setFormData({...formData, wordCount: e.target.value})}
              placeholder="1000"
            />
          </div>
          
          <div>
            <Label htmlFor="extra">Extra</Label>
            <Textarea
              id="extra"
              maxLength={300}
              value={formData.extra}
              onChange={(e) => setFormData({...formData, extra: e.target.value})}
              placeholder="Informazioni aggiuntive (max 300 caratteri)"
              rows={3}
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.extra.length}/300 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="channel-draft">Channel Draft</Label>
            <Select value={formData.channelDraft} onValueChange={(value) => setFormData({...formData, channelDraft: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona canale" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mail">Mail</SelectItem>
                <SelectItem value="wordpress">Wordpress</SelectItem>
                <SelectItem value="hubspot">Hubspot</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)}>Annulla</Button>
            <Button onClick={handleSubmit} className="bg-blue-500 text-white hover:bg-blue-600">
              Crea Blog Post
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Newsletter Card Component
function NewsletterCard() {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    topic: '',
    additionalDocument: null as File | null,
    linkReference: '',
    wordCount: '',
    extra: '',
    channelDraft: '',
    schedule: ''
  });

  const handleSubmit = () => {
    console.log('Newsletter Form Data:', formData);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className="bg-fylle-card rounded-lg p-6 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Send className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-lg font-semibold text-fylle-dark text-center mb-2">Newsletter</h3>
          <p className="text-sm text-fylle-gray text-center">Crea newsletter personalizzate</p>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Crea Newsletter</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="newsletter-topic">Topic</Label>
            <Input
              id="newsletter-topic"
              maxLength={50}
              value={formData.topic}
              onChange={(e) => setFormData({...formData, topic: e.target.value})}
              placeholder="Argomento della newsletter (max 50 caratteri)"
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.topic.length}/50 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="newsletter-doc">Additional Document</Label>
            <Input
              id="newsletter-doc"
              type="file"
              onChange={(e) => setFormData({...formData, additionalDocument: e.target.files?.[0] || null})}
            />
          </div>
          
          <div>
            <Label htmlFor="newsletter-link">Link Reference</Label>
            <Input
              id="newsletter-link"
              type="url"
              value={formData.linkReference}
              onChange={(e) => setFormData({...formData, linkReference: e.target.value})}
              placeholder="https://esempio.com"
            />
          </div>
          
          <div>
            <Label htmlFor="newsletter-words">Word Count</Label>
            <Input
              id="newsletter-words"
              type="number"
              value={formData.wordCount}
              onChange={(e) => setFormData({...formData, wordCount: e.target.value})}
              placeholder="500"
            />
          </div>
          
          <div>
            <Label htmlFor="newsletter-extra">Extra</Label>
            <Textarea
              id="newsletter-extra"
              maxLength={300}
              value={formData.extra}
              onChange={(e) => setFormData({...formData, extra: e.target.value})}
              placeholder="Informazioni aggiuntive (max 300 caratteri)"
              rows={3}
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.extra.length}/300 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="newsletter-channel">Channel Draft</Label>
            <Select value={formData.channelDraft} onValueChange={(value) => setFormData({...formData, channelDraft: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona canale" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mail">Mail</SelectItem>
                <SelectItem value="hubspot">Hubspot</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="newsletter-schedule">Schedule</Label>
            <Select value={formData.schedule} onValueChange={(value) => setFormData({...formData, schedule: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona frequenza" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="bi-weekly">Bi-weekly</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)}>Annulla</Button>
            <Button onClick={handleSubmit} className="bg-green-500 text-white hover:bg-green-600">
              Crea Newsletter
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Versioning Card Component
function VersioningCard() {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    document: null as File | null,
    linkReference: '',
    textFormat: '',
    gruppi: [] as string[],
    piattaforme: [] as string[],
    extra: '',
    channelDraft: ''
  });

  const handleGruppiChange = (gruppo: string, checked: boolean) => {
    if (checked) {
      setFormData({...formData, gruppi: [...formData.gruppi, gruppo]});
    } else {
      setFormData({...formData, gruppi: formData.gruppi.filter(g => g !== gruppo)});
    }
  };

  const handlePiattaformeChange = (piattaforma: string, checked: boolean) => {
    if (checked) {
      setFormData({...formData, piattaforme: [...formData.piattaforme, piattaforma]});
    } else {
      setFormData({...formData, piattaforme: formData.piattaforme.filter(p => p !== piattaforma)});
    }
  };

  const handleSubmit = () => {
    console.log('Versioning Form Data:', formData);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className="bg-fylle-card rounded-lg p-6 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <GitBranch className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <h3 className="text-lg font-semibold text-fylle-dark text-center mb-2">Versioning</h3>
          <p className="text-sm text-fylle-gray text-center">Versiona contenuti per diverse piattaforme</p>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Crea Versioning</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="versioning-doc">Document</Label>
            <Input
              id="versioning-doc"
              type="file"
              onChange={(e) => setFormData({...formData, document: e.target.files?.[0] || null})}
            />
          </div>
          
          <div>
            <Label htmlFor="versioning-link">Link Reference</Label>
            <Input
              id="versioning-link"
              type="url"
              value={formData.linkReference}
              onChange={(e) => setFormData({...formData, linkReference: e.target.value})}
              placeholder="https://esempio.com"
            />
          </div>
          
          <div>
            <Label htmlFor="text-format">Text Format</Label>
            <Select value={formData.textFormat} onValueChange={(value) => setFormData({...formData, textFormat: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona formato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="word">Word</SelectItem>
                <SelectItem value="md">Markdown</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label>Gruppi per cui versionare</Label>
            <div className="space-y-2 mt-2">
              {['Personas 1', 'Personas 2', 'Personas 3'].map((gruppo) => (
                <div key={gruppo} className="flex items-center space-x-2">
                  <Checkbox
                    id={gruppo}
                    checked={formData.gruppi.includes(gruppo)}
                    onCheckedChange={(checked) => handleGruppiChange(gruppo, checked as boolean)}
                  />
                  <Label htmlFor={gruppo}>{gruppo}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <Label>Piattaforme per cui versionare</Label>
            <div className="space-y-2 mt-2">
              {['Newsletter', 'Facebook', 'Instagram', 'LinkedIn'].map((piattaforma) => (
                <div key={piattaforma} className="flex items-center space-x-2">
                  <Checkbox
                    id={piattaforma}
                    checked={formData.piattaforme.includes(piattaforma)}
                    onCheckedChange={(checked) => handlePiattaformeChange(piattaforma, checked as boolean)}
                  />
                  <Label htmlFor={piattaforma}>{piattaforma}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <Label htmlFor="versioning-extra">Extra</Label>
            <Textarea
              id="versioning-extra"
              maxLength={300}
              value={formData.extra}
              onChange={(e) => setFormData({...formData, extra: e.target.value})}
              placeholder="Informazioni aggiuntive (max 300 caratteri)"
              rows={3}
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.extra.length}/300 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="versioning-channel">Channel Draft</Label>
            <Select value={formData.channelDraft} onValueChange={(value) => setFormData({...formData, channelDraft: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona canale" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mail">Mail</SelectItem>
                <SelectItem value="hubspot">Hubspot</SelectItem>
                {formData.piattaforme.map((piattaforma) => (
                  <SelectItem key={piattaforma} value={piattaforma.toLowerCase()}>
                    {piattaforma}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)}>Annulla</Button>
            <Button onClick={handleSubmit} className="bg-purple-500 text-white hover:bg-purple-600">
              Crea Versioning
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}