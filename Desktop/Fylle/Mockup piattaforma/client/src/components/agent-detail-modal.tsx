import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AIAgent } from "@/lib/types";

interface AgentDetailModalProps {
  agent: AIAgent | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function AgentDetailModal({ agent, isOpen, onClose }: AgentDetailModalProps) {
  if (!agent) return null;

  const getAgentConfig = (agentName: string) => {
    switch (agentName) {
      case "Context Setter":
        return {
          description: "Sets up context from Knowledge Base for optimal task execution",
          inputSources: [
            { name: "Brand DNA", docs: "2 documents", status: "active" },
            { name: "Performance Data", docs: "125 documents", status: "active" },
            { name: "Market Intelligence", docs: "Real-time", status: "active" }
          ],
          outputFormats: [
            { type: "JSON", description: "Structured data for agents" },
            { type: "Markdown", description: "Human-readable context" }
          ],
          channels: [
            { name: "Slack", icon: "💬", tooltip: "Delivers results via DM or #marketing channel" },
            { name: "Email", icon: "✉️", tooltip: "Sends formatted report to specified recipients" },
            { name: "Discord", icon: "🎮", tooltip: "Posts in designated workspace channel" }
          ],
          configuration: {
            trigger: ["Manual", "Scheduled", "Event-based"],
            contextDepth: ["Standard", "Deep", "Custom"],
            updateFrequency: ["Real-time", "Hourly", "Daily"]
          }
        };
      case "Web Agent":
        return {
          description: "Performs comprehensive web research and competitive analysis",
          inputSources: [
            { name: "Search Queries", docs: "Dynamic", status: "active" },
            { name: "Competitor URLs", docs: "50+ sources", status: "active" },
            { name: "Industry Keywords", docs: "500+ terms", status: "active" }
          ],
          outputFormats: [
            { type: "Research Report", description: "Structured findings with sources" },
            { type: "Data Export", description: "CSV/JSON for further analysis" }
          ],
          channels: [
            { name: "Slack", icon: "💬", tooltip: "Delivers results via DM or #research channel" },
            { name: "Email", icon: "✉️", tooltip: "Sends detailed research reports" }
          ],
          configuration: {
            searchDepth: ["Surface", "Deep", "Comprehensive"],
            dataRetention: ["24 hours", "7 days", "30 days"],
            updateFrequency: ["On-demand", "Daily", "Weekly"]
          }
        };
      case "Copywriter":
        return {
          description: "Creates high-quality content optimized for brand voice and SEO",
          inputSources: [
            { name: "Brand Guidelines", docs: "12 documents", status: "active" },
            { name: "SEO Keywords", docs: "300+ terms", status: "active" },
            { name: "Content Templates", docs: "25 templates", status: "active" }
          ],
          outputFormats: [
            { type: "Blog Posts", description: "SEO-optimized articles" },
            { type: "Social Content", description: "Platform-specific posts" },
            { type: "Ad Copy", description: "Performance marketing copy" }
          ],
          channels: [
            { name: "Slack", icon: "💬", tooltip: "Sends drafts for review" },
            { name: "Email", icon: "✉️", tooltip: "Delivers final content via email" }
          ],
          configuration: {
            toneOfVoice: ["Professional", "Friendly", "Authoritative"],
            contentLength: ["Short-form", "Medium", "Long-form"],
            seoOptimization: ["Basic", "Advanced", "Full"]
          }
        };
      default:
        return {
          description: "AI Agent for automated task execution",
          inputSources: [],
          outputFormats: [],
          channels: [],
          configuration: {}
        };
    }
  };

  const config = getAgentConfig(agent.name);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3 text-xl">
            <span className="text-2xl">{agent.icon}</span>
            <span className="text-fylle-dark">{agent.name} - Configuration</span>
            <Badge variant={agent.status === 'available' ? 'default' : agent.status === 'running' ? 'destructive' : 'secondary'}>
              {agent.status}
            </Badge>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Description */}
          <div>
            <p className="text-fylle-gray">{config.description}</p>
          </div>

          <Separator />

          {/* Input Sources */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-3">Input Sources:</h3>
            <div className="space-y-2">
              {config.inputSources.map((source, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="font-medium text-sm text-fylle-dark">{source.name}</span>
                  </div>
                  <span className="text-xs text-fylle-gray">({source.docs})</span>
                </div>
              ))}
            </div>
          </div>

          {/* Output Format */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-3">Output Format:</h3>
            <div className="space-y-2">
              {config.outputFormats.map((format, index) => (
                <div key={index} className="flex justify-between items-start p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-sm text-fylle-dark">- {format.type}</div>
                    <div className="text-xs text-fylle-gray">({format.description})</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Channels */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-3">Channels:</h3>
            <div className="flex space-x-4">
              {config.channels.map((channel, index) => (
                <div key={index} className="group relative">
                  <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <span className="text-lg">{channel.icon}</span>
                    <span className="text-sm font-medium text-fylle-dark">{channel.name}</span>
                  </div>
                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                    {channel.tooltip}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Configuration */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-3">Configuration:</h3>
            <div className="space-y-3">
              {Object.entries(config.configuration).map(([key, values]) => (
                <div key={key} className="flex items-center justify-between">
                  <span className="text-sm text-fylle-gray capitalize">{key.replace(/([A-Z])/g, ' $1')}:</span>
                  <div className="flex space-x-2">
                    {Array.isArray(values) && values.map((value, index) => (
                      <Badge key={index} variant={index === 0 ? "default" : "outline"} className="text-xs">
                        {value}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button variant="outline" className="flex-1">
              <i className="fas fa-eye mr-2"></i>
              Preview Output
            </Button>
            <Button variant="default" className="flex-1 bg-fylle-green hover:bg-fylle-green/90">
              <i className="fas fa-rocket mr-2"></i>
              Deploy Agent
            </Button>
            <Button variant="outline" className="flex-1">
              <i className="fas fa-save mr-2"></i>
              Save Configuration
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}