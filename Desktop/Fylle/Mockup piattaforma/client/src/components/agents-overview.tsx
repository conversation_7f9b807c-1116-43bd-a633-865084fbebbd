import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from '@/components/ui/dialog';

import { Play, Pause, Settings, TrendingUp, Clock, CheckCircle, AlertTriangle, Users, MessageSquare, Link, FileText, Search, PenTool, BarChart3, Target, Filter, Calendar, Grid } from 'lucide-react';

interface AgentsOverviewProps {
  aiAgents: any[];
  workflows: any[];
}

export function AgentsOverview({ aiAgents, workflows }: AgentsOverviewProps) {
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);

  // Team-based agent structure
  const agentTeams = [
    {
      id: 1,
      name: 'Team Content',
      status: 'active',
      description: 'Content creation and writing specialists',
      icon: <PenTool className="w-6 h-6" />,
      color: 'bg-blue-500',
      members: [
        {
          id: 1,
          name: 'Content Setter',
          role: 'Context and strategy setup',
          status: 'running',
          icon: <Target className="w-5 h-5" />
        },
        {
          id: 2,
          name: 'Web Searcher',
          role: 'Research and data gathering',
          status: 'available',
          icon: <Search className="w-5 h-5" />
        },
        {
          id: 3,
          name: 'Copywriter',
          role: 'Content creation and revision',
          status: 'available',
          icon: <PenTool className="w-5 h-5" />
        }
      ],
      settings: {
        communicationChannels: ['Slack', 'Discord', 'Email'],
        webLinks: ['https://docs.company.com', 'https://brand.guidelines.com'],
        additionalDocuments: ['Brand Guidelines.pdf', 'Content Style Guide.docx', 'SEO Checklist.pdf']
      },
      stats: {
        tasksCompleted: 45,
        avgEfficiency: 92,
        uptime: '15h 30m'
      }
    },
    {
      id: 2,
      name: 'Team Analisi',
      status: 'inactive',
      description: 'Data analysis and insights generation',
      icon: <BarChart3 className="w-6 h-6" />,
      color: 'bg-purple-500',
      members: [
        {
          id: 4,
          name: 'Data Analyzer',
          role: 'Statistical analysis and reporting',
          status: 'idle',
          icon: <BarChart3 className="w-5 h-5" />
        },
        {
          id: 5,
          name: 'Market Researcher',
          role: 'Market trends and competitor analysis',
          status: 'idle',
          icon: <TrendingUp className="w-5 h-5" />
        },
        {
          id: 6,
          name: 'Performance Monitor',
          role: 'KPI tracking and optimization',
          status: 'idle',
          icon: <Target className="w-5 h-5" />
        }
      ],
      settings: {
        communicationChannels: ['Email', 'Dashboard'],
        webLinks: ['https://analytics.company.com'],
        additionalDocuments: ['Analysis Templates.xlsx', 'KPI Definitions.pdf']
      },
      stats: {
        tasksCompleted: 0,
        avgEfficiency: 0,
        uptime: '0h'
      }
    }
  ];

  const getTeamStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-600';
      case 'inactive': return 'bg-gray-100 text-gray-600';
      case 'maintenance': return 'bg-yellow-100 text-yellow-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getAgentStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-600';
      case 'available': return 'bg-blue-100 text-blue-600';
      case 'idle': return 'bg-gray-100 text-gray-600';
      case 'busy': return 'bg-orange-100 text-orange-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getTeamStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="w-4 h-4" />;
      case 'inactive': return <Pause className="w-4 h-4" />;
      case 'maintenance': return <Settings className="w-4 h-4" />;
      default: return <Pause className="w-4 h-4" />;
    }
  };



  // Active workflows data for teams
  const teamWorkflows = [
    {
      id: 1,
      name: 'Newsletter Creation',
      status: 'running',
      progress: 60,
      estimatedTime: '15 min',
      currentStep: 'Content Review',
      teamId: 1, // Team Content
      tasks: [
        { name: 'Setup Context', agent: 'Content Setter', status: 'completed' },
        { name: 'Research Topics', agent: 'Web Searcher', status: 'running' },
        { name: 'Write Newsletter', agent: 'Copywriter', status: 'pending' }
      ]
    },
    {
      id: 2,
      name: 'Blog Post Creation',
      status: 'scheduled',
      progress: 0,
      estimatedTime: '25 min',
      currentStep: 'Waiting to start',
      teamId: 1, // Team Content
      tasks: [
        { name: 'Topic Research', agent: 'Web Searcher', status: 'pending' },
        { name: 'Content Strategy', agent: 'Content Setter', status: 'pending' },
        { name: 'Write Article', agent: 'Copywriter', status: 'pending' },
        { name: 'SEO Optimization', agent: 'Copywriter', status: 'pending' }
      ]
    }
  ];

  const getWorkflowStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-600';
      case 'scheduled': return 'bg-blue-100 text-blue-600';
      case 'completed': return 'bg-purple-100 text-purple-600';
      case 'paused': return 'bg-yellow-100 text-yellow-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-600';
      case 'running': return 'bg-blue-100 text-blue-600';
      case 'pending': return 'bg-gray-100 text-gray-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const TeamDetailModal = ({ team }: { team: any }) => {
    const activeWorkflows = teamWorkflows.filter(w => w.teamId === team.id);
    
    return (
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <div className={`w-10 h-10 ${team.color} rounded-lg flex items-center justify-center text-white`}>
              {team.icon}
            </div>
            <div>
              <h2 className="text-xl font-semibold">{team.name}</h2>
              <p className="text-sm text-gray-600">{team.description}</p>
            </div>
          </DialogTitle>
          <DialogDescription>
            Dettagli del team con membri, impostazioni e workflow attivi
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-2 gap-8">
          {/* Left Column - Team Members */}
          <div>
            <h3 className="text-lg font-medium mb-4">Team Members</h3>
            <div className="space-y-4 mb-6">
              {team.members.map((member: any) => (
                <div key={member.id} className="bg-fylle-card rounded-xl p-4 apple-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        {member.icon}
                      </div>
                      <div>
                        <h4 className="font-medium text-fylle-dark">{member.name}</h4>
                        <p className="text-sm text-fylle-gray">{member.role}</p>
                      </div>
                    </div>
                    <Badge className={getAgentStatusColor(member.status)}>
                      {member.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>

            {/* Team Settings */}
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-3 flex items-center space-x-2">
                  <MessageSquare className="w-5 h-5" />
                  <span>Canali di Comunicazione</span>
                </h4>
                <div className="space-y-2">
                  {team.settings.communicationChannels.map((channel: string, index: number) => (
                    <div key={index} className="px-3 py-2 bg-blue-50 rounded-lg text-sm text-blue-700">
                      {channel}
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-3 flex items-center space-x-2">
                  <Link className="w-5 h-5" />
                  <span>Web Links</span>
                </h4>
                <div className="space-y-2">
                  {team.settings.webLinks.map((link: string, index: number) => (
                    <div key={index} className="px-3 py-2 bg-green-50 rounded-lg text-sm text-green-700 truncate">
                      {link}
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-3 flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Documenti Aggiuntivi</span>
                </h4>
                <div className="space-y-2">
                  {team.settings.additionalDocuments.map((doc: string, index: number) => (
                    <div key={index} className="px-3 py-2 bg-purple-50 rounded-lg text-sm text-purple-700 truncate">
                      {doc}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Active Workflows */}
          <div>
            <h3 className="text-lg font-medium mb-4">Workflow Attivi</h3>
            <div className="space-y-4">
              {activeWorkflows.map((workflow) => (
                <div key={workflow.id} className="bg-fylle-card rounded-xl p-4 apple-shadow">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium text-fylle-dark">{workflow.name}</h4>
                      <p className="text-sm text-fylle-gray">{workflow.currentStep}</p>
                    </div>
                    <Badge className={getWorkflowStatusColor(workflow.status)}>
                      {workflow.status}
                    </Badge>
                  </div>

                  {workflow.status === 'running' && (
                    <div className="mb-3">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span className="text-fylle-gray">Progress</span>
                        <span className="text-fylle-dark">{workflow.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all" 
                          style={{width: `${workflow.progress}%`}}
                        ></div>
                      </div>
                    </div>
                  )}

                  <div className="mb-3">
                    <h5 className="text-sm font-medium text-fylle-dark mb-2">Tasks</h5>
                    <div className="space-y-2">
                      {workflow.tasks.map((task, index) => (
                        <div key={index} className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${
                              task.status === 'completed' ? 'bg-green-500' :
                              task.status === 'running' ? 'bg-blue-500' : 'bg-gray-300'
                            }`}></div>
                            <span className="text-fylle-dark">{task.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-fylle-gray">{task.agent}</span>
                            <Badge className={getTaskStatusColor(task.status)}>
                              {task.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="text-sm text-fylle-gray">
                    Est. Time: {workflow.estimatedTime}
                  </div>
                </div>
              ))}
            </div>

            {/* Team Performance Stats */}
            <div className="mt-6">
              <h4 className="font-medium mb-3">Team Performance</h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-fylle-card rounded-xl p-4 apple-shadow text-center">
                  <div className="text-2xl font-bold text-fylle-dark">{team.stats.tasksCompleted}</div>
                  <div className="text-sm text-fylle-gray">Tasks</div>
                </div>
                <div className="bg-fylle-card rounded-xl p-4 apple-shadow text-center">
                  <div className="text-2xl font-bold text-fylle-dark">{team.stats.avgEfficiency}%</div>
                  <div className="text-sm text-fylle-gray">Efficiency</div>
                </div>
                <div className="bg-fylle-card rounded-xl p-4 apple-shadow text-center">
                  <div className="text-2xl font-bold text-fylle-dark">{team.stats.uptime}</div>
                  <div className="text-sm text-fylle-gray">Uptime</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    );
  };

  // Team overview stats
  const teamStats = {
    totalTeams: agentTeams.length,
    activeTeams: agentTeams.filter(t => t.status === 'active').length,
    totalAgents: agentTeams.reduce((acc, team) => acc + team.members.length, 0),
    runningAgents: agentTeams.reduce((acc, team) => 
      acc + team.members.filter(m => m.status === 'running').length, 0
    )
  };

  return (
    <div className="p-8 agents-overview-section" data-context="AI Agents">
      {/* Simplified Team Summary */}
      <div className="bg-fylle-card rounded-lg p-4 border border-gray-200 mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="font-medium text-fylle-dark">Team Overview</h2>
            <p className="text-sm text-fylle-gray">{teamStats.activeTeams} of {teamStats.totalTeams} teams active • {teamStats.runningAgents} agents running</p>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-fylle-dark">89%</div>
            <div className="text-xs text-fylle-gray">Avg. efficiency</div>
          </div>
        </div>
      </div>

      {/* AI Agent Teams */}
      <div>
        <h3 className="text-xl font-semibold text-fylle-dark mb-6">AI Agent Teams</h3>
        <div className="grid md:grid-cols-2 gap-6">
              {agentTeams.map((team) => (
                <div key={team.id} className="bg-fylle-card rounded-lg p-4 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 ${team.color} rounded-lg flex items-center justify-center text-white`}>
                        {team.icon}
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-fylle-dark">{team.name}</h4>
                        <p className="text-sm text-fylle-gray">{team.description}</p>
                      </div>
                    </div>
                    <Badge className={`${getTeamStatusColor(team.status)} flex items-center space-x-1`}>
                      {getTeamStatusIcon(team.status)}
                      <span className="capitalize">{team.status}</span>
                    </Badge>
                  </div>

                  {/* Team Members Preview */}
                  <div className="mb-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="w-4 h-4 text-fylle-gray" />
                      <span className="text-sm font-medium text-fylle-gray">
                        {team.members.length} Members
                      </span>
                    </div>
                    <div className="flex -space-x-2">
                      {team.members.slice(0, 3).map((member, index) => (
                        <div 
                          key={member.id}
                          className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center border-2 border-white"
                          style={{ zIndex: team.members.length - index }}
                        >
                          {member.icon}
                        </div>
                      ))}
                      {team.members.length > 3 && (
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center border-2 border-white text-xs font-medium text-gray-600">
                          +{team.members.length - 3}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Team Quick Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
                    <div className="text-center">
                      <div className="font-semibold text-fylle-dark">{team.stats.tasksCompleted}</div>
                      <div className="text-fylle-gray">Tasks</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-fylle-dark">{team.stats.avgEfficiency}%</div>
                      <div className="text-fylle-gray">Efficiency</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-fylle-dark">{team.stats.uptime}</div>
                      <div className="text-fylle-gray">Uptime</div>
                    </div>
                  </div>

                  {/* Action Button */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button 
                        className="w-full bg-fylle-button text-white hover:bg-fylle-button-hover transition-apple"
                        disabled={team.status === 'inactive'}
                      >
                        {team.status === 'active' ? 'Gestisci Team' : 'Team Disattivato'}
                      </Button>
                    </DialogTrigger>
                    <TeamDetailModal team={team} />
                  </Dialog>
                </div>
              ))}
        </div>
      </div>
    </div>
  );
}