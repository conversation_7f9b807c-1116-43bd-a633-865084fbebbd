import { useState, useRef, useEffect } from "react";
import { Send, X, MessageCircle } from "lucide-react";

interface ContextChatBubbleProps {
  x: number;
  y: number;
  onClose: () => void;
  contextInfo?: string;
}

export function ContextChatBubble({ x, y, onClose, contextInfo }: ContextChatBubbleProps) {
  const [message, setMessage] = useState("");
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState<Array<{ text: string; isUser: boolean; timestamp: Date }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const bubbleRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Focus input when component mounts
    if (inputRef.current) {
      inputRef.current.focus();
    }

    // <PERSON>le clicks outside bubble
    const handleClickOutside = (event: MouseEvent) => {
      if (bubbleRef.current && !bubbleRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    // Handle escape key
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleEscape);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [onClose]);

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    const userMessage = { text: message, isUser: true, timestamp: new Date() };
    setMessages(prev => [...prev, userMessage]);
    setMessage("");
    setIsLoading(true);
    setIsExpanded(true);

    // Simulate AI response (in real app, this would call Fylle AI API)
    setTimeout(() => {
      const contextPrefix = contextInfo ? `Based on ${contextInfo}, ` : "";
      const responses = [
        `${contextPrefix}I can help you with that. What specific aspect would you like me to focus on?`,
        `${contextPrefix}Here's what I understand from your request. Let me provide some insights...`,
        `${contextPrefix}I've analyzed the context. Here are some recommendations for your workflow.`,
        `${contextPrefix}That's a great question! Let me break this down for you step by step.`
      ];
      
      const aiResponse = {
        text: responses[Math.floor(Math.random() * responses.length)],
        isUser: false,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Calculate position to keep bubble within viewport
  const bubbleWidth = isExpanded ? 320 : 280;
  const bubbleHeight = isExpanded ? 400 : 60;
  const adjustedX = Math.min(x, window.innerWidth - bubbleWidth - 20);
  const adjustedY = Math.min(y, window.innerHeight - bubbleHeight - 20);

  return (
    <div
      ref={bubbleRef}
      className="fixed z-50 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 apple-shadow"
      style={{
        left: `${adjustedX}px`,
        top: `${adjustedY}px`,
        width: `${bubbleWidth}px`,
        minHeight: isExpanded ? `${bubbleHeight}px` : "auto",
        transform: "scale(0.95)",
        animation: "contextChatAppear 0.2s ease-out forwards"
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
            <MessageCircle className="w-4 h-4 text-white" />
          </div>
          <div>
            <div className="font-medium text-sm text-gray-900 dark:text-white">Ask Fylle</div>
            {contextInfo && (
              <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-32">
                {contextInfo}
              </div>
            )}
          </div>
        </div>
        <button
          onClick={onClose}
          className="w-6 h-6 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-center transition-colors"
        >
          <X className="w-4 h-4 text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Messages Area (only show when expanded) */}
      {isExpanded && (
        <div className="flex-1 p-3 max-h-64 overflow-y-auto space-y-3">
          {messages.map((msg, index) => (
            <div
              key={index}
              className={`flex ${msg.isUser ? "justify-end" : "justify-start"}`}
            >
              <div
                className={`max-w-[80%] p-2 rounded-lg text-sm ${
                  msg.isUser
                    ? "bg-blue-500 text-white"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
                }`}
              >
                {msg.text}
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Input Area */}
      <div className="p-3 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask Fylle anything..."
            className="flex-1 px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
          <button
            onClick={handleSendMessage}
            disabled={!message.trim() || isLoading}
            className="w-8 h-8 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 rounded-lg flex items-center justify-center transition-colors"
          >
            <Send className="w-4 h-4 text-white" />
          </button>
        </div>
      </div>
    </div>
  );
}