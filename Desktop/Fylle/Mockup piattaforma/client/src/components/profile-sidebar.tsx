import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { X, User, Settings, Users, CreditCard, Crown, Check } from 'lucide-react';
import { PlansPage } from './plans-page';

interface ProfileSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const plans = [
  {
    name: 'Fylle Seed',
    subtitle: 'Basic plan',
    price: '€ 250,00',
    duration: '12 mesi',
    color: 'bg-green-100 text-green-700 border-green-200',
    icon: '🌱'
  },
  {
    name: 'Fylle Bloom',
    subtitle: 'Pro plan',
    price: '€ 500,00',
    duration: '12 mesi',
    color: 'bg-blue-100 text-blue-700 border-blue-200',
    icon: '🌸',
    current: true
  },
  {
    name: 'Fylle Pulse',
    subtitle: 'Advanced plan',
    price: '€ 800,00',
    duration: '12 mesi',
    color: 'bg-purple-100 text-purple-700 border-purple-200',
    icon: '⚡'
  },
  {
    name: 'Fylle Mind',
    subtitle: 'Enterprise plan',
    price: '€ 2.000,00',
    duration: '12 mesi',
    color: 'bg-orange-100 text-orange-700 border-orange-200',
    icon: '🧠'
  }
];

export function ProfileSidebar({ isOpen, onClose }: ProfileSidebarProps) {
  const [activeSection, setActiveSection] = useState<'profile' | 'settings' | 'users' | 'billing' | 'plans'>('profile');
  
  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className={`fixed right-0 top-0 h-full bg-white shadow-2xl z-50 overflow-y-auto ${
        activeSection === 'plans' ? 'w-[800px]' : 'w-96'
      }`}>
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-fylle-dark">Profilo</h2>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onClose}
              className="p-2"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* User Profile */}
          <div className="flex items-center space-x-4 mb-6 p-4 bg-gray-50 rounded-xl">
            <div className="w-12 h-12 bg-fylle-green rounded-full flex items-center justify-center">
              <span className="text-white text-lg font-medium">SA</span>
            </div>
            <div>
              <h3 className="font-medium text-fylle-dark">Sarah Anderson</h3>
              <p className="text-sm text-fylle-gray"><EMAIL></p>
              <Badge className="mt-1 bg-green-100 text-green-700 text-xs">
                Marketing Manager
              </Badge>
            </div>
          </div>

          <Separator className="mb-6" />

          {/* Menu Items */}
          <div className="space-y-2 mb-6">
            <Button 
              variant="ghost" 
              className={`w-full justify-start transition-apple ${
                activeSection === 'profile' 
                  ? 'bg-gray-100 text-fylle-dark font-medium' 
                  : 'text-fylle-dark hover:bg-gray-50'
              }`}
              onClick={() => setActiveSection('profile')}
            >
              <User className="w-4 h-4 mr-3" />
              Profilo
            </Button>
            
            <Button 
              variant="ghost" 
              className={`w-full justify-start transition-apple ${
                activeSection === 'settings' 
                  ? 'bg-gray-100 text-fylle-dark font-medium' 
                  : 'text-fylle-dark hover:bg-gray-50'
              }`}
              onClick={() => setActiveSection('settings')}
            >
              <Settings className="w-4 h-4 mr-3" />
              Impostazioni
            </Button>
            
            <Button 
              variant="ghost" 
              className={`w-full justify-start transition-apple ${
                activeSection === 'users' 
                  ? 'bg-gray-100 text-fylle-dark font-medium' 
                  : 'text-fylle-dark hover:bg-gray-50'
              }`}
              onClick={() => setActiveSection('users')}
            >
              <Users className="w-4 h-4 mr-3" />
              Gestione utenti
            </Button>
            
            <Button 
              variant="ghost" 
              className={`w-full justify-start transition-apple ${
                activeSection === 'billing' 
                  ? 'bg-gray-100 text-fylle-dark font-medium' 
                  : 'text-fylle-dark hover:bg-gray-50'
              }`}
              onClick={() => setActiveSection('billing')}
            >
              <CreditCard className="w-4 h-4 mr-3" />
              Billing
            </Button>
            
            <Button 
              variant="ghost" 
              className={`w-full justify-start transition-apple ${
                activeSection === 'plans' 
                  ? 'bg-gray-100 text-fylle-dark font-medium' 
                  : 'text-fylle-dark hover:bg-gray-50'
              }`}
              onClick={() => setActiveSection('plans')}
            >
              <Crown className="w-4 h-4 mr-3" />
              Plans
            </Button>
          </div>

          <Separator className="mb-6" />

          {/* Content Area */}
          <div className="flex-1 mb-6">
            {activeSection === 'plans' ? (
              <div className="h-full overflow-y-auto -m-6">
                <PlansPage />
              </div>
            ) : activeSection === 'profile' ? (
              <div>
                <h3 className="font-semibold text-fylle-dark mb-4">Informazioni Profilo</h3>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-fylle-gray">Nome</label>
                    <div className="text-fylle-dark">Sarah Anderson</div>
                  </div>
                  <div>
                    <label className="text-sm text-fylle-gray">Email</label>
                    <div className="text-fylle-dark"><EMAIL></div>
                  </div>
                  <div>
                    <label className="text-sm text-fylle-gray">Ruolo</label>
                    <div className="text-fylle-dark">Marketing Manager</div>
                  </div>
                  <div>
                    <label className="text-sm text-fylle-gray">Ultimo accesso</label>
                    <div className="text-fylle-dark">19 Luglio 2024, 23:05</div>
                  </div>
                </div>
              </div>
            ) : activeSection === 'settings' ? (
              <div>
                <h3 className="font-semibold text-fylle-dark mb-4">Impostazioni</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-fylle-dark">Notifiche email</span>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-fylle-dark">Modalità scura</span>
                    <input type="checkbox" className="rounded" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-fylle-dark">Aggiornamenti automatici</span>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>
                </div>
              </div>
            ) : activeSection === 'users' ? (
              <div>
                <h3 className="font-semibold text-fylle-dark mb-4">Gestione Utenti</h3>
                <div className="space-y-3">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium text-fylle-dark">Sarah Anderson</div>
                    <div className="text-sm text-fylle-gray">Admin</div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium text-fylle-dark">Marco Rossi</div>
                    <div className="text-sm text-fylle-gray">Editor</div>
                  </div>
                  <Button className="w-full mt-3" variant="outline">
                    Invita utente
                  </Button>
                </div>
              </div>
            ) : activeSection === 'billing' ? (
              <div>
                <h3 className="font-semibold text-fylle-dark mb-4">Billing</h3>
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="font-medium text-fylle-dark">Piano attuale: Fylle Bloom</div>
                    <div className="text-sm text-fylle-gray">€500/12 mesi</div>
                    <div className="text-sm text-fylle-gray">Rinnovo: Dicembre 2025</div>
                  </div>
                  <div>
                    <h4 className="font-medium text-fylle-dark mb-2">Metodo di pagamento</h4>
                    <div className="text-sm text-fylle-gray">**** **** **** 4242</div>
                    <div className="text-sm text-fylle-gray">Visa - Scade 12/27</div>
                  </div>
                  <Button className="w-full" variant="outline">
                    Gestisci fatturazione
                  </Button>
                </div>
              </div>
            ) : null}
          </div>

          {activeSection !== 'plans' && (
            <>
              <Separator className="mb-6" />
              
              {/* Footer Actions */}
              <div className="space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full"
                >
                  Gestisci abbonamento
                </Button>
                
                <Button 
                  variant="ghost" 
                  className="w-full text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  Logout
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}