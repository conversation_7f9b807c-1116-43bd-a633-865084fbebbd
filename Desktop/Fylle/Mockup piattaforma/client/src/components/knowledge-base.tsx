import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from '@/components/ui/dialog';
import { Plus, Target, Users, Book, Award, Briefcase, Lightbulb, Search, Filter, Play, Calendar, Flag, Trash2, Eye, Share, Download, LayoutGrid, Table, FileText, MessageCircle, Send, TrendingUp, Zap, RefreshCw, BarChart3, CheckCircle, ChevronDown, ChevronUp } from 'lucide-react';
import AddKnowledgeModal from './add-knowledge-modal';
import type { KnowledgeBase, UserRole } from '@/lib/types';
import { UseMutationResult } from '@tanstack/react-query';

interface KnowledgeBaseProps {
  knowledgeBases: KnowledgeBase[];
  userRole: UserRole;
  knowledgeStats: {
    totalNodes: number;
    totalCalls: number;
    optimizationLevel: number;
    lastUpdate: string;
  };
  uploadMutation: UseMutationResult<any, Error, { file: File; name: string; category: string }, unknown>;
}

export function KnowledgeBase({ knowledgeBases, userRole, knowledgeStats, uploadMutation }: KnowledgeBaseProps) {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [selectedKnowledge, setSelectedKnowledge] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'cards' | 'table' | 'view' | 'chat'>('cards');
  const [chatMessage, setChatMessage] = useState('');
  const [isRecommendationsExpanded, setIsRecommendationsExpanded] = useState(false);

  // Knowledge items focused on traffic, blog posts, and newsletters
  const knowledgeItems = [
    {
      id: 1,
      title: 'Website Traffic Analytics',
      category: 'Performance',
      type: 'Traffic Analysis',
      status: 'active',
      size: '2.1 MB',
      items: 45,
      keyInsight: 'Blog traffic increased 34% from organic search',
      updatedTime: '15 min ago',
      icon: <TrendingUp className="w-5 h-5" />,
      description: 'Analisi completa del traffico del sito web con focus su blog post performance e conversioni.',
      content: 'Report dettagliato delle performance del traffico web degli ultimi 30 giorni, con particolare attenzione al traffico generato dai blog post e alle conversioni da newsletter.',
      tags: ['Traffic', 'SEO', 'Blog', 'Analytics'],
      source: 'Google Analytics + Internal Tracking',
      createdDate: '2025-07-19',
      lastAccessed: '2025-07-19',
      usage: 28,
      effectiveness: 94
    },
    {
      id: 2,
      title: 'Blog Post Content Strategy',
      category: 'Content',
      type: 'Editorial Plan',
      status: 'active', 
      size: '1.8 MB',
      items: 67,
      keyInsight: 'AI automation topics drive 2x more engagement',
      updatedTime: '1 hour ago',
      icon: <FileText className="w-5 h-5" />,
      description: 'Piano editoriale e strategia per i blog post con analisi delle performance dei contenuti.',
      content: 'Strategia completa per la creazione di blog post, includendo calendario editoriale, keyword research, e analisi delle performance dei contenuti pubblicati.',
      tags: ['Blog', 'Content Strategy', 'SEO', 'Engagement'],
      source: 'Content Team + Analytics',
      createdDate: '2025-07-18',
      lastAccessed: '2025-07-19',
      usage: 42,
      effectiveness: 89
    },
    {
      id: 3,
      title: 'Newsletter Performance Report',
      category: 'Performance',
      type: 'Email Analytics',
      status: 'active',
      size: '1.4 MB',
      items: 89,
      keyInsight: 'Open rate improved 18% with personalized subjects',
      updatedTime: '2 hours ago',
      icon: <Send className="w-5 h-5" />,
      description: 'Report completo delle performance delle newsletter con analisi di aperture, click e conversioni.',
      content: 'Analisi dettagliata delle performance delle newsletter degli ultimi 3 mesi, includendo tassi di apertura, click-through rate, e impatto sulle conversioni del sito.',
      tags: ['Newsletter', 'Email Marketing', 'Conversions', 'Analytics'],
      source: 'Email Platform + CRM',
      createdDate: '2025-07-19',
      lastAccessed: '2025-07-19',
      usage: 35,
      effectiveness: 91
    },
    {
      id: 4,
      title: 'SEO Content Optimization',
      category: 'Optimization',
      type: 'SEO Analysis',
      status: 'active',
      size: '2.3 MB',
      items: 134,
      keyInsight: 'Long-tail keywords drive 45% more qualified traffic',
      updatedTime: '3 hours ago',
      icon: <Zap className="w-5 h-5" />,
      description: 'Analisi e ottimizzazione SEO dei contenuti blog per migliorare il ranking e il traffico organico.',
      content: 'Studio completo delle performance SEO dei blog post, con analisi delle keyword, ranking positions, e suggerimenti per ottimizzazioni future.',
      tags: ['SEO', 'Keywords', 'Content', 'Ranking'],
      source: 'SEO Tools + Analytics',
      createdDate: '2025-07-18',
      lastAccessed: '2025-07-19',
      usage: 52,
      effectiveness: 87
    },
    {
      id: 5,
      title: 'Traffic Source Analysis',
      category: 'Performance',
      type: 'Channel Analysis',
      status: 'completed',
      size: '1.9 MB',
      items: 76,
      keyInsight: 'Newsletter referrals have highest conversion rate',
      updatedTime: '1 day ago',
      icon: <BarChart3 className="w-5 h-5" />,
      description: 'Analisi delle sorgenti di traffico e del loro impatto sulle conversioni e engagement.',
      content: 'Report dettagliato sui canali di traffico più performanti, con particolare focus sui referral da newsletter e blog post.',
      tags: ['Traffic Sources', 'Conversions', 'Referrals', 'Analysis'],
      source: 'User Research + Surveys',
      createdDate: '2025-07-13',
      lastAccessed: '2025-07-19',
      usage: 31,
      effectiveness: 91
    },
    {
      id: 6,
      title: 'Content Calendar & Publishing',
      category: 'Content',
      type: 'Editorial Calendar',
      status: 'active',
      size: '1.6 MB',
      items: 98,
      keyInsight: 'Tuesday & Thursday posts get 40% more engagement',
      updatedTime: '4 hours ago',
      icon: <Calendar className="w-5 h-5" />,
      description: 'Calendario editoriale completo per blog post e newsletter con analisi dei tempi di pubblicazione ottimali.',
      content: 'Piano di pubblicazione settimanale per blog post e newsletter, con analisi dei giorni e orari migliori per il coinvolgimento del pubblico.',
      tags: ['Content Calendar', 'Publishing', 'Timing', 'Engagement'],
      source: 'Editorial Team + Analytics',
      createdDate: '2025-07-17',
      lastAccessed: '2025-07-19',
      usage: 48,
      effectiveness: 85
    },
    {
      id: 7,
      title: 'Conversion Funnel Analysis',
      category: 'Optimization',
      type: 'Funnel Report',
      status: 'active',
      size: '2.7 MB',
      items: 112,
      keyInsight: 'Blog-to-newsletter conversion rate: 28%',
      updatedTime: '5 hours ago',
      icon: <RefreshCw className="w-5 h-5" />,
      description: 'Analisi del funnel di conversione dal traffico blog alle iscrizioni newsletter e vendite.',
      content: 'Report completo del percorso utente dal primo click sui blog post fino alla conversione finale, con identificazione dei punti di abbandono.',
      tags: ['Conversion', 'Funnel', 'Blog to Newsletter', 'Optimization'],
      source: 'Analytics + CRM Integration',
      createdDate: '2025-07-18',
      lastAccessed: '2025-07-19',
      usage: 37,
      effectiveness: 92
    }
  ];

  const categories = ['All Categories', 'Performance', 'Content', 'Optimization'];
  
  const filteredItems = selectedCategory === 'All Categories' 
    ? knowledgeItems 
    : knowledgeItems.filter(item => item.category === selectedCategory);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-600';
      case 'completed': return 'bg-blue-100 text-blue-600';
      case 'processing': return 'bg-yellow-100 text-yellow-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <div className="p-8 knowledge-base-section" data-context="Knowledge Base">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-semibold text-fylle-dark">Knowledge Base</h1>
          <Button 
            onClick={() => setIsUploadModalOpen(true)}
            className="px-6 py-3 bg-fylle-button text-white rounded-xl font-medium hover:bg-fylle-button-hover transition-apple flex items-center space-x-2 apple-shadow hover:apple-shadow-hover"
          >
            <Plus className="w-5 h-5" />
            <span>Add Knowledge</span>
          </Button>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-fylle-gray" />
              <input
                type="text"
                placeholder="Search knowledge..."
                className="pl-9 pr-4 py-2 bg-fylle-card border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 bg-fylle-card border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
          
          {/* View Toggle with Chat */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center bg-fylle-card rounded-md border border-gray-200">
              <button
                onClick={() => setViewMode('cards')}
                className={`p-2 rounded-l-md text-sm ${viewMode === 'cards' ? 'bg-blue-500 text-white' : 'text-fylle-gray hover:bg-gray-50'}`}
              >
                <LayoutGrid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('table')}
                className={`p-2 text-sm ${viewMode === 'table' ? 'bg-blue-500 text-white' : 'text-fylle-gray hover:bg-gray-50'}`}
              >
                <Table className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('chat')}
                className={`p-2 rounded-r-md text-sm ${viewMode === 'chat' ? 'bg-blue-500 text-white' : 'text-fylle-gray hover:bg-gray-50'}`}
              >
                <MessageCircle className="w-4 h-4" />
              </button>
            </div>
            <span className="text-sm text-fylle-gray">
              {filteredItems.length} items
            </span>
          </div>
        </div>

        {/* Performance Optimization Banner */}
        <div className="bg-fylle-card rounded-lg p-4 border-l-4 border-green-500 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-medium text-fylle-dark">Performance Score: 91%</h3>
                <p className="text-sm text-fylle-gray">Blog traffic +34% • Newsletter open rate +18%</p>
              </div>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setIsRecommendationsExpanded(!isRecommendationsExpanded)}
              className="text-green-600 border-green-200 hover:bg-green-50"
            >
              {isRecommendationsExpanded ? 'Nascondi' : 'Suggerimenti'}
            </Button>
          </div>
          
          {isRecommendationsExpanded && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-blue-50 rounded text-sm">
                  <span className="text-blue-700">Ottimizza pubblicazione Tuesday/Thursday</span>
                  <span className="text-xs bg-blue-200 text-blue-700 px-2 py-1 rounded">+15%</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-orange-50 rounded text-sm">
                  <span className="text-orange-700">Aumenta long-tail keywords nei blog</span>
                  <span className="text-xs bg-orange-200 text-orange-700 px-2 py-1 rounded">+22%</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-purple-50 rounded text-sm">
                  <span className="text-purple-700">Personalizza subject newsletter</span>
                  <span className="text-xs bg-purple-200 text-purple-700 px-2 py-1 rounded">+18%</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Content Based on View Mode */}
        {viewMode === 'chat' && (
          /* Minimalist Chat Interface */
          <div className="bg-fylle-card rounded-lg p-6 border border-gray-200">
            <div className="max-w-2xl mx-auto">
              <h3 className="text-lg font-medium text-fylle-dark mb-4">Analisi Rapida Knowledge Base</h3>
              
              {/* Chat Summary Points */}
              <div className="space-y-4 mb-6">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-fylle-dark font-medium">Traffic Performance</p>
                    <p className="text-sm text-fylle-gray">Blog traffic +34% da ricerca organica. Long-tail keywords generano 45% più traffico qualificato.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-fylle-dark font-medium">Newsletter Results</p>
                    <p className="text-sm text-fylle-gray">Open rate migliorato del 18% con subject personalizzati. Conversione blog-newsletter al 28%.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-fylle-dark font-medium">Content Strategy</p>
                    <p className="text-sm text-fylle-gray">Argomenti AI automation generano 2x più engagement. Pubblicazioni martedì/giovedì ottimali (+40%).</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-fylle-dark font-medium">Optimization Opportunities</p>
                    <p className="text-sm text-fylle-gray">3 aree di miglioramento identificate con potenziale +22% performance complessiva.</p>
                  </div>
                </div>
              </div>
              
              {/* Simple Chat Input */}
              <div className="flex items-center space-x-3 bg-gray-50 rounded-lg p-3">
                <input
                  type="text"
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  placeholder="Chiedi qualcosa sui dati..."
                  className="flex-1 bg-transparent border-none outline-none text-sm placeholder-fylle-gray"
                />
                <Button size="sm" className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
        
        {viewMode === 'cards' && (
          /* Knowledge Cards Grid */
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <Dialog key={item.id}>
              <DialogTrigger asChild>
                <div className="bg-fylle-card rounded-lg p-4 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    {item.icon}
                  </div>
                  <div>
                    <h3 className="font-medium text-fylle-dark">{item.title}</h3>
                    <p className="text-xs text-fylle-gray">{item.category}</p>
                  </div>
                </div>
                <Badge className={`${getStatusColor(item.status)} text-xs`}>
                  {item.status}
                </Badge>
              </div>

              <div className="mb-3">
                <p className="text-sm text-fylle-gray">{item.keyInsight}</p>
              </div>

              <div className="flex items-center justify-between text-xs text-fylle-gray">
                <span>{item.updatedTime}</span>
                <span>{item.items} items</span>
              </div>
                </div>
              </DialogTrigger>
              
              {/* Knowledge Detail Modal */}
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      {item.icon}
                    </div>
                    <div>
                      <DialogTitle className="text-xl font-semibold text-fylle-dark">{item.title}</DialogTitle>
                      <DialogDescription className="text-fylle-gray">{item.type} • {item.category}</DialogDescription>
                    </div>
                  </div>
                </DialogHeader>

                <div className="space-y-6">
                  {/* Status and Quick Info */}
                  <div className="flex items-center justify-between">
                    <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                    <div className="flex items-center space-x-4 text-sm text-fylle-gray">
                      <span>{item.size}</span>
                      <span>{item.items} items</span>
                      <span>Updated {item.updatedTime}</span>
                    </div>
                  </div>

                  {/* Description */}
                  <div>
                    <h4 className="font-semibold text-fylle-dark mb-2">Description</h4>
                    <p className="text-fylle-gray">{item.description}</p>
                  </div>

                  {/* Content Preview */}
                  <div>
                    <h4 className="font-semibold text-fylle-dark mb-2">Content</h4>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-sm text-fylle-gray">{item.content}</p>
                    </div>
                  </div>

                  {/* Tags */}
                  <div>
                    <h4 className="font-semibold text-fylle-dark mb-2">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {item.tags?.map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Statistics Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-fylle-card rounded-lg p-4 text-center">
                      <div className="text-lg font-semibold text-fylle-dark">{item.usage}</div>
                      <div className="text-sm text-fylle-gray">Times Used</div>
                    </div>
                    <div className="bg-fylle-card rounded-lg p-4 text-center">
                      <div className="text-lg font-semibold text-fylle-dark">{item.effectiveness}%</div>
                      <div className="text-sm text-fylle-gray">Effectiveness</div>
                    </div>
                    <div className="bg-fylle-card rounded-lg p-4 text-center">
                      <div className="text-lg font-semibold text-fylle-dark">{item.createdDate}</div>
                      <div className="text-sm text-fylle-gray">Created</div>
                    </div>
                    <div className="bg-fylle-card rounded-lg p-4 text-center">
                      <div className="text-lg font-semibold text-fylle-dark">{item.lastAccessed}</div>
                      <div className="text-sm text-fylle-gray">Last Accessed</div>
                    </div>
                  </div>

                  {/* Key Insight */}
                  <div>
                    <h4 className="font-semibold text-fylle-dark mb-2">Key Insight</h4>
                    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                      <p className="text-sm text-yellow-800">{item.keyInsight}</p>
                    </div>
                  </div>

                  {/* Source */}
                  <div>
                    <h4 className="font-semibold text-fylle-dark mb-2">Source</h4>
                    <p className="text-sm text-fylle-gray">{item.source}</p>
                  </div>

                  {/* Action Buttons */}
                  <div className="border-t pt-6">
                    <h4 className="font-semibold text-fylle-dark mb-4">Actions</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <Button 
                        className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white"
                        onClick={() => {/* Handle Activate */}}
                      >
                        <Play className="w-4 h-4" />
                        <span>Attiva</span>
                      </Button>
                      
                      <Button 
                        className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white"
                        onClick={() => {/* Handle Schedule */}}
                      >
                        <Calendar className="w-4 h-4" />
                        <span>Schedula</span>
                      </Button>
                      
                      <Button 
                        className="flex items-center space-x-2 bg-orange-500 hover:bg-orange-600 text-white"
                        onClick={() => {/* Handle Flag */}}
                      >
                        <Flag className="w-4 h-4" />
                        <span>Segnala dopo</span>
                      </Button>
                      
                      <Button 
                        variant="destructive" 
                        className="flex items-center space-x-2"
                        onClick={() => {/* Handle Remove */}}
                      >
                        <Trash2 className="w-4 h-4" />
                        <span>Rimuovi</span>
                      </Button>
                    </div>
                    
                    {/* Secondary Actions */}
                    <div className="flex items-center space-x-3 mt-4 pt-4 border-t">
                      <Button variant="outline" className="flex items-center space-x-2">
                        <Eye className="w-4 h-4" />
                        <span>View Full Content</span>
                      </Button>
                      
                      <Button variant="outline" className="flex items-center space-x-2">
                        <Share className="w-4 h-4" />
                        <span>Share</span>
                      </Button>
                      
                      <Button variant="outline" className="flex items-center space-x-2">
                        <Download className="w-4 h-4" />
                        <span>Download</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          ))}
          </div>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <div className="bg-fylle-card rounded-xl overflow-hidden apple-shadow">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Nome</th>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Categoria</th>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Tipo</th>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Status</th>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Dimensione</th>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Items</th>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Utilizzo</th>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Efficacia</th>
                    <th className="text-left py-4 px-6 font-semibold text-fylle-dark">Aggiornato</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredItems.map((item, index) => (
                    <Dialog key={item.id}>
                      <DialogTrigger asChild>
                        <tr className={`border-b hover:bg-gray-50 cursor-pointer transition-apple ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                {item.icon}
                              </div>
                              <div>
                                <div className="font-medium text-fylle-dark">{item.title}</div>
                                <div className="text-sm text-fylle-gray truncate max-w-xs">{item.keyInsight}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <span className="px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-xs">
                              {item.category}
                            </span>
                          </td>
                          <td className="py-4 px-6 text-fylle-gray">{item.type}</td>
                          <td className="py-4 px-6">
                            <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                          </td>
                          <td className="py-4 px-6 text-fylle-dark">{item.size}</td>
                          <td className="py-4 px-6 text-fylle-dark">{item.items}</td>
                          <td className="py-4 px-6 text-fylle-dark">{item.usage}</td>
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-2">
                              <div className="w-12 bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-green-500 h-2 rounded-full" 
                                  style={{ width: `${item.effectiveness}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-fylle-dark">{item.effectiveness}%</span>
                            </div>
                          </td>
                          <td className="py-4 px-6 text-fylle-gray text-sm">{item.updatedTime}</td>
                        </tr>
                      </DialogTrigger>
                      {/* Reuse the same modal content */}
                      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <div className="flex items-center space-x-3 mb-2">
                            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                              {item.icon}
                            </div>
                            <div>
                              <DialogTitle className="text-xl font-semibold text-fylle-dark">{item.title}</DialogTitle>
                              <DialogDescription className="text-fylle-gray">{item.type} • {item.category}</DialogDescription>
                            </div>
                          </div>
                        </DialogHeader>
                        {/* Reuse the same modal content structure */}
                        <div className="space-y-6">
                          <div className="flex items-center justify-between">
                            <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                            <div className="flex items-center space-x-4 text-sm text-fylle-gray">
                              <span>{item.size}</span>
                              <span>{item.items} items</span>
                              <span>Updated {item.updatedTime}</span>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-semibold text-fylle-dark mb-2">Description</h4>
                            <p className="text-fylle-gray">{item.description}</p>
                          </div>
                          <div>
                            <h4 className="font-semibold text-fylle-dark mb-2">Content</h4>
                            <div className="bg-gray-50 rounded-lg p-4">
                              <p className="text-sm text-fylle-gray">{item.content}</p>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-semibold text-fylle-dark mb-2">Tags</h4>
                            <div className="flex flex-wrap gap-2">
                              {item.tags?.map((tag, index) => (
                                <span key={index} className="px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-xs">
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="bg-fylle-card rounded-lg p-4 text-center">
                              <div className="text-lg font-semibold text-fylle-dark">{item.usage}</div>
                              <div className="text-sm text-fylle-gray">Times Used</div>
                            </div>
                            <div className="bg-fylle-card rounded-lg p-4 text-center">
                              <div className="text-lg font-semibold text-fylle-dark">{item.effectiveness}%</div>
                              <div className="text-sm text-fylle-gray">Effectiveness</div>
                            </div>
                            <div className="bg-fylle-card rounded-lg p-4 text-center">
                              <div className="text-lg font-semibold text-fylle-dark">{item.createdDate}</div>
                              <div className="text-sm text-fylle-gray">Created</div>
                            </div>
                            <div className="bg-fylle-card rounded-lg p-4 text-center">
                              <div className="text-lg font-semibold text-fylle-dark">{item.lastAccessed}</div>
                              <div className="text-sm text-fylle-gray">Last Accessed</div>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-semibold text-fylle-dark mb-2">Key Insight</h4>
                            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                              <p className="text-sm text-yellow-800">{item.keyInsight}</p>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-semibold text-fylle-dark mb-2">Source</h4>
                            <p className="text-sm text-fylle-gray">{item.source}</p>
                          </div>
                          <div className="border-t pt-6">
                            <h4 className="font-semibold text-fylle-dark mb-4">Actions</h4>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                              <Button className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white">
                                <Play className="w-4 h-4" />
                                <span>Attiva</span>
                              </Button>
                              <Button className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white">
                                <Calendar className="w-4 h-4" />
                                <span>Schedula</span>
                              </Button>
                              <Button className="flex items-center space-x-2 bg-orange-500 hover:bg-orange-600 text-white">
                                <Flag className="w-4 h-4" />
                                <span>Segnala dopo</span>
                              </Button>
                              <Button variant="destructive" className="flex items-center space-x-2">
                                <Trash2 className="w-4 h-4" />
                                <span>Rimuovi</span>
                              </Button>
                            </div>
                            <div className="flex items-center space-x-3 mt-4 pt-4 border-t">
                              <Button variant="outline" className="flex items-center space-x-2">
                                <Eye className="w-4 h-4" />
                                <span>View Full Content</span>
                              </Button>
                              <Button variant="outline" className="flex items-center space-x-2">
                                <Share className="w-4 h-4" />
                                <span>Share</span>
                              </Button>
                              <Button variant="outline" className="flex items-center space-x-2">
                                <Download className="w-4 h-4" />
                                <span>Download</span>
                              </Button>
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Chat View */}
        {viewMode === 'chat' && (
          <div className="space-y-6">
            {/* Chat Header with Summary */}
            <div className="bg-fylle-card rounded-xl p-6 apple-shadow">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <MessageCircle className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-fylle-dark">Resoconto Knowledge Base</h3>
                  <p className="text-sm text-fylle-gray">Analisi conversazionale dei tuoi knowledge assets</p>
                </div>
              </div>
              
              {/* AI Summary */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mb-4">
                <h4 className="font-semibold text-fylle-dark mb-2">📊 Resoconto Automatico</h4>
                <p className="text-sm text-fylle-gray mb-3">
                  La tua Knowledge Base contiene <strong>{filteredItems.length} elementi</strong> con un livello di ottimizzazione complessivo dell'<strong>89%</strong>. 
                  Gli asset più performanti sono quelli relativi alle analisi competitive, con un'efficacia media del 93%.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="bg-white rounded-lg p-3">
                    <div className="font-medium text-green-600">⭐ Più Utilizzato</div>
                    <div className="text-fylle-dark">Amazon Marketing Strategy (47 utilizzi)</div>
                  </div>
                  <div className="bg-white rounded-lg p-3">
                    <div className="font-medium text-blue-600">🎯 Più Efficace</div>
                    <div className="text-fylle-dark">Nike Social Media (95% efficacia)</div>
                  </div>
                  <div className="bg-white rounded-lg p-3">
                    <div className="font-medium text-orange-600">📈 Più Recente</div>
                    <div className="text-fylle-dark">Marketing Manager Persona (6 ore fa)</div>
                  </div>
                </div>
              </div>

              {/* Quick Insights */}
              <div className="space-y-3">
                <h4 className="font-semibold text-fylle-dark">💡 Insights Chiave</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-start space-x-2">
                    <span className="text-blue-500">•</span>
                    <span className="text-fylle-gray">I tuoi competitor insights sono particolarmente forti, soprattutto per Amazon e Nike</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-green-500">•</span>
                    <span className="text-fylle-gray">Il Brand Book potrebbe beneficiare di aggiornamenti più frequenti</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-purple-500">•</span>
                    <span className="text-fylle-gray">Le analisi audience stanno performando bene ma hanno margini di miglioramento</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Chat Interface */}
            <div className="bg-fylle-card rounded-xl p-6 apple-shadow">
              <h4 className="font-semibold text-fylle-dark mb-4">💬 Chiedimi qualcosa sulla Knowledge Base</h4>
              
              {/* Sample Questions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                <button 
                  onClick={() => setChatMessage("Qual è l'insight più importante dai competitor?")}
                  className="text-left p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-apple text-sm text-blue-700"
                >
                  "Qual è l'insight più importante dai competitor?"
                </button>
                <button 
                  onClick={() => setChatMessage("Come posso migliorare l'efficacia del Brand Book?")}
                  className="text-left p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-apple text-sm text-green-700"
                >
                  "Come posso migliorare l'efficacia del Brand Book?"
                </button>
                <button 
                  onClick={() => setChatMessage("Quali sono i gap nella mia audience research?")}
                  className="text-left p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-apple text-sm text-purple-700"
                >
                  "Quali sono i gap nella mia audience research?"
                </button>
                <button 
                  onClick={() => setChatMessage("Suggeriscimi 3 azioni per ottimizzare la Knowledge Base")}
                  className="text-left p-3 bg-orange-50 rounded-lg hover:bg-orange-100 transition-apple text-sm text-orange-700"
                >
                  "Suggeriscimi 3 azioni per ottimizzare la Knowledge Base"
                </button>
              </div>

              {/* Chat Input */}
              <div className="flex items-center space-x-3">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={chatMessage}
                    onChange={(e) => setChatMessage(e.target.value)}
                    placeholder="Scrivi la tua domanda sulla Knowledge Base..."
                    className="w-full pl-4 pr-12 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-apple"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        // Handle send message
                        console.log('Send message:', chatMessage);
                        setChatMessage('');
                      }
                    }}
                  />
                  <button 
                    onClick={() => {
                      console.log('Send message:', chatMessage);
                      setChatMessage('');
                    }}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-blue-500 hover:bg-blue-100 rounded-lg transition-apple"
                  >
                    <Send className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Chat History Placeholder */}
              <div className="mt-6 space-y-4">
                <div className="text-sm text-fylle-gray text-center py-4">
                  Inizia una conversazione per ricevere insights personalizzati sulla tua Knowledge Base
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <AddKnowledgeModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUpload={uploadMutation?.mutate || (() => {})}
        isUploading={uploadMutation?.isPending || false}
      />
    </div>
  );
}