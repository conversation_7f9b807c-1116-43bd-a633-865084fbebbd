import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Filter, Calendar, Grid, TrendingUp, Clock, CheckCircle, Users, FileText, Send, GitBranch, Upload, Link, Plus } from 'lucide-react';

interface ContentsOverviewProps {
  aiAgents: any[];
  workflows: any[];
}

export function ContentsOverview({ aiAgents, workflows }: ContentsOverviewProps) {
  const [selectedTeamFilter, setSelectedTeamFilter] = useState<string>('all');
  const [selectedWorkflowFilter, setSelectedWorkflowFilter] = useState<string>('all');
  const [selectedDateFilter, setSelectedDateFilter] = useState<string>('all');

  // Content artifacts data
  const contentArtifacts = [
    {
      id: 1,
      title: 'Newsletter Settimanale - Week 29',
      type: 'Newsletter',
      team: 'Team Content',
      workflow: 'Newsletter Creation',
      status: 'completed',
      createdAt: '2025-07-19',
      thumbnail: '/api/placeholder/300/200',
      description: 'Newsletter settimanale con insights di mercato e aggiornamenti prodotto',
      tags: ['Marketing', 'Weekly', 'Insights']
    },
    {
      id: 2,
      title: 'Analisi Competitor Q2 2025',
      type: 'Report',
      team: 'Team Analisi',
      workflow: 'Market Analysis',
      status: 'in-progress',
      createdAt: '2025-07-18',
      thumbnail: '/api/placeholder/300/200',
      description: 'Report completo sui competitor principali nel Q2 2025',
      tags: ['Analysis', 'Competitor', 'Research']
    },
    {
      id: 3,
      title: 'Blog Post: Future of AI Marketing',
      type: 'Blog Post',
      team: 'Team Content',
      workflow: 'Blog Post Creation',
      status: 'draft',
      createdAt: '2025-07-17',
      thumbnail: '/api/placeholder/300/200',
      description: 'Articolo sul futuro del marketing AI-driven e le tendenze emergenti',
      tags: ['AI', 'Marketing', 'Future']
    },
    {
      id: 4,
      title: 'Social Media Campaign - Summer',
      type: 'Campaign',
      team: 'Team Content',
      workflow: 'Campaign Creation',
      status: 'scheduled',
      createdAt: '2025-07-16',
      thumbnail: '/api/placeholder/300/200',
      description: 'Campagna social media per la stagione estiva 2025',
      tags: ['Social', 'Campaign', 'Summer']
    },
    {
      id: 5,
      title: 'Performance Dashboard Q2',
      type: 'Dashboard',
      team: 'Team Analisi',
      workflow: 'Analytics Report',
      status: 'completed',
      createdAt: '2025-07-15',
      thumbnail: '/api/placeholder/300/200',
      description: 'Dashboard interattiva delle performance Q2 con KPI principali',
      tags: ['Analytics', 'Performance', 'Q2']
    },
    {
      id: 6,
      title: 'Email Automation Sequence',
      type: 'Email',
      team: 'Team Content',
      workflow: 'Email Campaign',
      status: 'active',
      createdAt: '2025-07-14',
      thumbnail: '/api/placeholder/300/200',
      description: 'Sequenza automatizzata di email per il nurturing dei lead',
      tags: ['Email', 'Automation', 'Lead Generation']
    },
    {
      id: 7,
      title: 'Video Content Strategy 2025',
      type: 'Video',
      team: 'Team Content',
      workflow: 'Video Creation',
      status: 'in-progress',
      createdAt: '2025-07-13',
      thumbnail: '/api/placeholder/300/200',
      description: 'Strategia completa per i contenuti video del 2025',
      tags: ['Video', 'Strategy', '2025']
    },
    {
      id: 8,
      title: 'Market Research Report - AI Tools',
      type: 'Research',
      team: 'Team Analisi',
      workflow: 'Market Research',
      status: 'completed',
      createdAt: '2025-07-12',
      thumbnail: '/api/placeholder/300/200',
      description: 'Analisi approfondita del mercato degli strumenti AI',
      tags: ['Research', 'AI Tools', 'Market']
    }
  ];

  // Filter options
  const teamOptions = ['all', 'Team Content', 'Team Analisi'];
  const workflowOptions = ['all', 'Newsletter Creation', 'Blog Post Creation', 'Market Analysis', 'Campaign Creation', 'Analytics Report', 'Email Campaign', 'Video Creation', 'Market Research'];
  const dateOptions = ['all', 'today', 'this-week', 'this-month'];

  const getFilteredContent = () => {
    return contentArtifacts.filter(content => {
      if (selectedTeamFilter !== 'all' && content.team !== selectedTeamFilter) return false;
      if (selectedWorkflowFilter !== 'all' && content.workflow !== selectedWorkflowFilter) return false;
      if (selectedDateFilter !== 'all') {
        const contentDate = new Date(content.createdAt);
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        switch (selectedDateFilter) {
          case 'today':
            if (contentDate.toDateString() !== today.toDateString()) return false;
            break;
          case 'this-week':
            if (contentDate < weekAgo) return false;
            break;
          case 'this-month':
            if (contentDate < monthAgo) return false;
            break;
        }
      }
      return true;
    });
  };

  const getContentStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-600';
      case 'in-progress': return 'bg-blue-100 text-blue-600';
      case 'draft': return 'bg-yellow-100 text-yellow-600';
      case 'scheduled': return 'bg-purple-100 text-purple-600';
      case 'active': return 'bg-green-100 text-green-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <div className="p-8 contents-overview-section" data-context="Contents">
      <div className="mb-8">
        <h1 className="text-3xl font-semibold text-fylle-dark mb-6">Content</h1>

        {/* Content Creation Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {/* Blog Post Card */}
          <BlogPostCard />
          
          {/* Newsletter Card */}
          <NewsletterCard />
          
          {/* Versioning Card */}
          <VersioningCard />
        </div>
      </div>

      {/* Content Artifacts Section */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-fylle-dark mb-4">Content Artifacts</h3>
        
        {/* Filters */}
        <div className="flex items-center space-x-4 mb-6 p-4 bg-fylle-card rounded-xl apple-shadow">
          <div className="flex items-center space-x-2">
            <Filter className="w-5 h-5 text-fylle-gray" />
            <span className="text-sm font-medium text-fylle-dark">Filters:</span>
          </div>
          
          <select
            value={selectedTeamFilter}
            onChange={(e) => setSelectedTeamFilter(e.target.value)}
            className="px-3 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-apple text-sm"
          >
            <option value="all">All Teams</option>
            {teamOptions.slice(1).map((team) => (
              <option key={team} value={team}>{team}</option>
            ))}
          </select>

          <select
            value={selectedWorkflowFilter}
            onChange={(e) => setSelectedWorkflowFilter(e.target.value)}
            className="px-3 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-apple text-sm"
          >
            <option value="all">All Workflows</option>
            {workflowOptions.slice(1).map((workflow) => (
              <option key={workflow} value={workflow}>{workflow}</option>
            ))}
          </select>

          <select
            value={selectedDateFilter}
            onChange={(e) => setSelectedDateFilter(e.target.value)}
            className="px-3 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-apple text-sm"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="this-week">This Week</option>
            <option value="this-month">This Month</option>
          </select>

          <div className="text-sm text-fylle-gray ml-auto">
            {getFilteredContent().length} content{getFilteredContent().length !== 1 ? 's' : ''}
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {getFilteredContent().map((content) => (
            <div key={content.id} className="bg-fylle-card rounded-xl p-6 apple-shadow hover:apple-shadow-hover transition-apple cursor-pointer">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h4 className="font-semibold text-fylle-dark mb-1">{content.title}</h4>
                  <p className="text-sm text-fylle-gray">{content.type}</p>
                </div>
                <Badge className={getContentStatusColor(content.status)}>
                  {content.status}
                </Badge>
              </div>

              <div className="mb-4">
                <div className="w-full h-32 bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                  <Grid className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-sm text-fylle-gray">{content.description}</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-fylle-gray">Team:</span>
                  <span className="font-medium text-fylle-dark">{content.team}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-fylle-gray">Workflow:</span>
                  <span className="font-medium text-fylle-dark">{content.workflow}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-fylle-gray">Created:</span>
                  <span className="font-medium text-fylle-dark">{new Date(content.createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="mt-4 flex flex-wrap gap-1">
                {content.tags.map((tag, index) => (
                  <span key={index} className="px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-xs">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Blog Post Card Component
function BlogPostCard() {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    topic: '',
    additionalDocument: null as File | null,
    linkReference: '',
    wordCount: '',
    extra: '',
    channelDraft: ''
  });

  const handleSubmit = () => {
    console.log('Blog Post Form Data:', formData);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className="bg-fylle-card rounded-lg p-6 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <h3 className="text-lg font-semibold text-fylle-dark text-center mb-2">Blog Post</h3>
          <p className="text-sm text-fylle-gray text-center">Crea contenuti per il blog con AI</p>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Crea Blog Post</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="topic">Topic</Label>
            <Input
              id="topic"
              maxLength={50}
              value={formData.topic}
              onChange={(e) => setFormData({...formData, topic: e.target.value})}
              placeholder="Argomento del blog post (max 50 caratteri)"
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.topic.length}/50 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="additional-doc">Additional Document</Label>
            <Input
              id="additional-doc"
              type="file"
              onChange={(e) => setFormData({...formData, additionalDocument: e.target.files?.[0] || null})}
            />
          </div>
          
          <div>
            <Label htmlFor="link-ref">Link Reference</Label>
            <Input
              id="link-ref"
              type="url"
              value={formData.linkReference}
              onChange={(e) => setFormData({...formData, linkReference: e.target.value})}
              placeholder="https://esempio.com"
            />
          </div>
          
          <div>
            <Label htmlFor="word-count">Word Count</Label>
            <Input
              id="word-count"
              type="number"
              value={formData.wordCount}
              onChange={(e) => setFormData({...formData, wordCount: e.target.value})}
              placeholder="1000"
            />
          </div>
          
          <div>
            <Label htmlFor="extra">Extra</Label>
            <Textarea
              id="extra"
              maxLength={300}
              value={formData.extra}
              onChange={(e) => setFormData({...formData, extra: e.target.value})}
              placeholder="Informazioni aggiuntive (max 300 caratteri)"
              rows={3}
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.extra.length}/300 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="channel-draft">Channel Draft</Label>
            <Select value={formData.channelDraft} onValueChange={(value) => setFormData({...formData, channelDraft: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona canale" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mail">Mail</SelectItem>
                <SelectItem value="wordpress">Wordpress</SelectItem>
                <SelectItem value="hubspot">Hubspot</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)}>Annulla</Button>
            <Button onClick={handleSubmit} className="bg-blue-500 text-white hover:bg-blue-600">
              Crea Blog Post
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Newsletter Card Component
function NewsletterCard() {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    topic: '',
    additionalDocument: null as File | null,
    linkReference: '',
    wordCount: '',
    extra: '',
    channelDraft: '',
    schedule: ''
  });

  const handleSubmit = () => {
    console.log('Newsletter Form Data:', formData);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className="bg-fylle-card rounded-lg p-6 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Send className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-lg font-semibold text-fylle-dark text-center mb-2">Newsletter</h3>
          <p className="text-sm text-fylle-gray text-center">Crea newsletter personalizzate</p>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Crea Newsletter</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="newsletter-topic">Topic</Label>
            <Input
              id="newsletter-topic"
              maxLength={50}
              value={formData.topic}
              onChange={(e) => setFormData({...formData, topic: e.target.value})}
              placeholder="Argomento della newsletter (max 50 caratteri)"
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.topic.length}/50 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="newsletter-doc">Additional Document</Label>
            <Input
              id="newsletter-doc"
              type="file"
              onChange={(e) => setFormData({...formData, additionalDocument: e.target.files?.[0] || null})}
            />
          </div>
          
          <div>
            <Label htmlFor="newsletter-link">Link Reference</Label>
            <Input
              id="newsletter-link"
              type="url"
              value={formData.linkReference}
              onChange={(e) => setFormData({...formData, linkReference: e.target.value})}
              placeholder="https://esempio.com"
            />
          </div>
          
          <div>
            <Label htmlFor="newsletter-words">Word Count</Label>
            <Input
              id="newsletter-words"
              type="number"
              value={formData.wordCount}
              onChange={(e) => setFormData({...formData, wordCount: e.target.value})}
              placeholder="500"
            />
          </div>
          
          <div>
            <Label htmlFor="newsletter-extra">Extra</Label>
            <Textarea
              id="newsletter-extra"
              maxLength={300}
              value={formData.extra}
              onChange={(e) => setFormData({...formData, extra: e.target.value})}
              placeholder="Informazioni aggiuntive (max 300 caratteri)"
              rows={3}
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.extra.length}/300 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="newsletter-channel">Channel Draft</Label>
            <Select value={formData.channelDraft} onValueChange={(value) => setFormData({...formData, channelDraft: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona canale" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mail">Mail</SelectItem>
                <SelectItem value="hubspot">Hubspot</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="newsletter-schedule">Schedule</Label>
            <Select value={formData.schedule} onValueChange={(value) => setFormData({...formData, schedule: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona frequenza" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="bi-weekly">Bi-weekly</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)}>Annulla</Button>
            <Button onClick={handleSubmit} className="bg-green-500 text-white hover:bg-green-600">
              Crea Newsletter
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Versioning Card Component
function VersioningCard() {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    document: null as File | null,
    linkReference: '',
    textFormat: '',
    gruppi: [] as string[],
    piattaforme: [] as string[],
    extra: '',
    channelDraft: ''
  });

  const handleGruppiChange = (gruppo: string, checked: boolean) => {
    if (checked) {
      setFormData({...formData, gruppi: [...formData.gruppi, gruppo]});
    } else {
      setFormData({...formData, gruppi: formData.gruppi.filter(g => g !== gruppo)});
    }
  };

  const handlePiattaformeChange = (piattaforma: string, checked: boolean) => {
    if (checked) {
      setFormData({...formData, piattaforme: [...formData.piattaforme, piattaforma]});
    } else {
      setFormData({...formData, piattaforme: formData.piattaforme.filter(p => p !== piattaforma)});
    }
  };

  const handleSubmit = () => {
    console.log('Versioning Form Data:', formData);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className="bg-fylle-card rounded-lg p-6 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <GitBranch className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <h3 className="text-lg font-semibold text-fylle-dark text-center mb-2">Versioning</h3>
          <p className="text-sm text-fylle-gray text-center">Versiona contenuti per diverse piattaforme</p>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Crea Versioning</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="versioning-doc">Document</Label>
            <Input
              id="versioning-doc"
              type="file"
              onChange={(e) => setFormData({...formData, document: e.target.files?.[0] || null})}
            />
          </div>
          
          <div>
            <Label htmlFor="versioning-link">Link Reference</Label>
            <Input
              id="versioning-link"
              type="url"
              value={formData.linkReference}
              onChange={(e) => setFormData({...formData, linkReference: e.target.value})}
              placeholder="https://esempio.com"
            />
          </div>
          
          <div>
            <Label htmlFor="text-format">Text Format</Label>
            <Select value={formData.textFormat} onValueChange={(value) => setFormData({...formData, textFormat: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona formato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="word">Word</SelectItem>
                <SelectItem value="md">Markdown</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label>Gruppi per cui versionare</Label>
            <div className="space-y-2 mt-2">
              {['Personas 1', 'Personas 2', 'Personas 3'].map((gruppo) => (
                <div key={gruppo} className="flex items-center space-x-2">
                  <Checkbox
                    id={gruppo}
                    checked={formData.gruppi.includes(gruppo)}
                    onCheckedChange={(checked) => handleGruppiChange(gruppo, checked as boolean)}
                  />
                  <Label htmlFor={gruppo}>{gruppo}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <Label>Piattaforme per cui versionare</Label>
            <div className="space-y-2 mt-2">
              {['Newsletter', 'Facebook', 'Instagram', 'LinkedIn'].map((piattaforma) => (
                <div key={piattaforma} className="flex items-center space-x-2">
                  <Checkbox
                    id={piattaforma}
                    checked={formData.piattaforme.includes(piattaforma)}
                    onCheckedChange={(checked) => handlePiattaformeChange(piattaforma, checked as boolean)}
                  />
                  <Label htmlFor={piattaforma}>{piattaforma}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <Label htmlFor="versioning-extra">Extra</Label>
            <Textarea
              id="versioning-extra"
              maxLength={300}
              value={formData.extra}
              onChange={(e) => setFormData({...formData, extra: e.target.value})}
              placeholder="Informazioni aggiuntive (max 300 caratteri)"
              rows={3}
            />
            <div className="text-xs text-fylle-gray mt-1">{formData.extra.length}/300 caratteri</div>
          </div>
          
          <div>
            <Label htmlFor="versioning-channel">Channel Draft</Label>
            <Select value={formData.channelDraft} onValueChange={(value) => setFormData({...formData, channelDraft: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona canale" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mail">Mail</SelectItem>
                <SelectItem value="hubspot">Hubspot</SelectItem>
                {formData.piattaforme.map((piattaforma) => (
                  <SelectItem key={piattaforma} value={piattaforma.toLowerCase()}>
                    {piattaforma}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)}>Annulla</Button>
            <Button onClick={handleSubmit} className="bg-purple-500 text-white hover:bg-purple-600">
              Crea Versioning
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}