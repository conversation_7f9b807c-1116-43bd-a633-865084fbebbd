import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { availableIntegrations } from '@/lib/mock-data';
import type { Integration } from '@/lib/types';

interface IntegrationsProps {
  integrations: Integration[];
}

export function Integrations({ integrations }: IntegrationsProps) {
  const getStatusColor = (status: string) => {
    return status === 'connected' ? 'bg-green-500' : 'bg-red-500';
  };

  const getStatusBorder = (status: string) => {
    return status === 'connected' ? 'border-green-500' : 'border-red-500';
  };

  const formatLastSync = (lastSync?: Date) => {
    if (!lastSync) return 'Never';
    const now = new Date();
    const diff = now.getTime() - new Date(lastSync).getTime();
    const minutes = Math.floor(diff / 60000);
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes} min ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    const days = Math.floor(hours / 24);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  };

  return (
    <div className="p-8 integrations-section" data-context="Integrations">
      <div className="mb-8">
        <h1 className="text-3xl font-semibold mb-6 text-fylle-dark">Integrations</h1>
        
        {/* Connected Sources */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 text-fylle-dark">Connected Sources</h2>
          <div className="grid grid-cols-5 gap-4">
            {/* Google Analytics */}
            <div className="bg-fylle-card rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                  <i className="fab fa-google text-white text-sm"></i>
                </div>
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              </div>
              <div className="font-medium text-fylle-dark">Google Analytics</div>
              <div className="text-xs text-green-600 mt-1">Active</div>
            </div>

            {/* Meta */}
            <div className="bg-fylle-card rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <i className="fab fa-meta text-white text-sm"></i>
                </div>
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              </div>
              <div className="font-medium text-fylle-dark">Meta</div>
              <div className="text-xs text-green-600 mt-1">Active</div>
            </div>

            {/* Website */}
            <div className="bg-fylle-card rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                  <i className="fas fa-globe text-white text-sm"></i>
                </div>
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              </div>
              <div className="font-medium text-fylle-dark">Website</div>
              <div className="text-xs text-green-600 mt-1">Active</div>
            </div>

            {/* LinkedIn */}
            <div className="bg-fylle-card rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <div className="w-8 h-8 bg-blue-700 rounded-lg flex items-center justify-center">
                  <i className="fab fa-linkedin text-white text-sm"></i>
                </div>
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              </div>
              <div className="font-medium text-fylle-dark">LinkedIn</div>
              <div className="text-xs text-orange-600 mt-1">89% quota</div>
            </div>

            {/* Slack */}
            <div className="bg-fylle-card rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                  <i className="fab fa-slack text-white text-sm"></i>
                </div>
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              </div>
              <div className="font-medium text-fylle-dark">Slack</div>
              <div className="text-xs text-green-600 mt-1">Active</div>
            </div>
          </div>
        </div>

        {/* Available Integrations */}
        <div>
          <h2 className="text-xl font-semibold mb-4 text-fylle-dark">Available Integrations</h2>
          <div className="space-y-6">
            {/* Data Source In */}
            <div>
              <h3 className="font-medium text-fylle-gray mb-3 flex items-center space-x-2">
                <i className="fas fa-download"></i>
                <span>Data Source In</span>
              </h3>
              <div className="grid grid-cols-3 gap-4">
                {availableIntegrations.dataSourceIn.map((integration, index) => (
                  <IntegrationCard key={index} integration={integration} />
                ))}
              </div>
            </div>

            {/* Data Source Out */}
            <div>
              <h3 className="font-medium text-fylle-gray mb-3 flex items-center space-x-2">
                <i className="fas fa-upload"></i>
                <span>Data Source Out</span>
              </h3>
              <div className="grid grid-cols-3 gap-4">
                {availableIntegrations.dataSourceOut.map((integration, index) => (
                  <IntegrationCard key={index} integration={integration} />
                ))}
              </div>
            </div>

            {/* Storage */}
            <div>
              <h3 className="font-medium text-fylle-gray mb-3 flex items-center space-x-2">
                <i className="fas fa-database"></i>
                <span>Storage</span>
              </h3>
              <div className="grid grid-cols-3 gap-4">
                {availableIntegrations.storage.map((integration, index) => (
                  <IntegrationCard key={index} integration={integration} />
                ))}
              </div>
            </div>

            {/* Communication */}
            <div>
              <h3 className="font-medium text-fylle-gray mb-3 flex items-center space-x-2">
                <i className="fas fa-comments"></i>
                <span>Communication</span>
              </h3>
              <div className="grid grid-cols-3 gap-4">
                {availableIntegrations.communication.map((integration, index) => (
                  <IntegrationCard key={index} integration={integration} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function IntegrationCard({ integration }: { integration: any }) {
  return (
    <div className="bg-fylle-card rounded-lg p-3 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
      <div className={`w-8 h-8 ${integration.color} rounded-lg flex items-center justify-center mb-3`}>
        <i className={`${integration.icon} text-white text-sm`}></i>
      </div>
      <div className="font-medium text-fylle-dark mb-2">{integration.name}</div>
      <Button 
        variant="link" 
        size="sm"
        className={`text-xs font-medium p-0 h-auto ${
          integration.status === 'connected' ? 'text-green-600' : 'text-blue-600'
        }`}
      >
        {integration.status === 'connected' ? 'Connected' : 'Connect'}
      </Button>
    </div>
  );
}

function getIntegrationIcon(name: string): string {
  const iconMap: { [key: string]: string } = {
    'Website': 'fas fa-globe',
    'Google Drive': 'fab fa-google-drive',
    'LinkedIn': 'fab fa-linkedin',
    'CRM': 'fas fa-users',
    'Analytics': 'fas fa-chart-bar'
  };
  return iconMap[name] || 'fas fa-plug';
}

function getIntegrationTypeLabel(type: string): string {
  const typeMap: { [key: string]: string } = {
    'website': 'Website',
    'file_storage': 'File Storage',
    'social_media': 'Social Media',
    'crm': 'Customer Management',
    'analytics': 'Analytics Platform'
  };
  return typeMap[type] || type;
}
