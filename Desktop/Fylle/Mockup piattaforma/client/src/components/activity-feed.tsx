import React from 'react';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { activityFeed } from '@/lib/mock-data';

interface ActivityFeedProps {
  isVisible: boolean;
  onClose: () => void;
}

export function ActivityFeed({ isVisible, onClose }: ActivityFeedProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 w-80 bg-fylle-card rounded-xl apple-shadow-hover border border-gray-200 p-4 transition-apple z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-sm text-fylle-dark">Recent Activity</h3>
        <Button variant="ghost" size="sm" onClick={onClose} className="text-fylle-gray hover:text-fylle-dark">
          <X className="w-4 h-4" />
        </Button>
      </div>
      
      <div className="space-y-3 text-xs custom-scrollbar max-h-64 overflow-y-auto">
        {activityFeed.map((item, index) => (
          <div 
            key={item.id} 
            className="flex items-start space-x-2 activity-item"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className={`w-2 h-2 rounded-full mt-1.5 ${
              item.type === 'performance' ? 'bg-blue-500' :
              item.type === 'insight' ? 'bg-purple-500' :
              item.type === 'operation' ? 'bg-green-500' :
              'bg-orange-500'
            }`}></div>
            <div className="flex-1">
              <div className="font-medium text-fylle-dark flex items-center space-x-1">
                <span>{item.icon}</span>
                <span>{item.title}</span>
              </div>
              <div className="text-fylle-gray">{item.description}</div>
              <div className="text-fylle-gray">{item.timestamp}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
