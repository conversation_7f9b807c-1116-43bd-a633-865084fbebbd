import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>T<PERSON>le, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Workflow } from "@/lib/types";
import { Calendar, Clock, Play, Settings, Users } from "lucide-react";

interface WorkflowDetailModalProps {
  workflow: Workflow | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function WorkflowDetailModal({ workflow, isOpen, onClose }: WorkflowDetailModalProps) {
  if (!workflow) return null;

  const getWorkflowDetails = (workflowName: string) => {
    switch (workflowName) {
      case "Blog Post Workflow":
        return {
          description: "End-to-end blog post creation with research, writing, and SEO optimization",
          estimatedTime: 15,
          tasks: [
            {
              id: 1,
              name: "Context Setting",
              agent: "🧠 Context Setter",
              input: "Brand DNA + Topic brief",
              output: "Contextual brief for next agents",
              tools: ["Knowledge Base API", "Brand guidelines"],
              icon: "🧠",
              color: "bg-blue-500"
            },
            {
              id: 2,
              name: "Web Research",
              agent: "🔍 Web Agent", 
              input: "Context + Research query",
              output: "Relevant articles, data, insights",
              tools: ["Web scraping", "Google API", "Competitor analysis"],
              icon: "🔍",
              color: "bg-purple-500"
            },
            {
              id: 3,
              name: "Content Writing",
              agent: "✍️ Copywriter",
              input: "Context + Research data", 
              output: "Blog post draft + SEO optimization",
              tools: ["Writing engine", "SEO checker", "Brand voice"],
              icon: "✍️",
              color: "bg-green-500"
            }
          ],
          usedBy: ["Siebert", "GapMed", "Altroconsumo"],
          channels: [
            { name: "Slack", icon: "💬", tooltip: "Delivers results via DM or #marketing channel" },
            { name: "Email", icon: "✉️", tooltip: "Sends formatted report to specified recipients" },
            { name: "Discord", icon: "🎮", tooltip: "Posts in designated workspace channel" }
          ]
        };
      case "Video Content Workflow":
        return {
          description: "Complete video content creation from script to production",
          estimatedTime: 30,
          tasks: [
            {
              id: 1,
              name: "Script Planning",
              agent: "🧠 Context Setter",
              input: "Video brief + Brand guidelines",
              output: "Script outline and structure",
              tools: ["Brand voice API", "Content templates"],
              icon: "🧠",
              color: "bg-blue-500"
            },
            {
              id: 2,
              name: "Content Research",
              agent: "🔍 Research Agent",
              input: "Script outline + Topic",
              output: "Supporting data and examples",
              tools: ["Web research", "Data analysis"],
              icon: "🔍",
              color: "bg-purple-500"
            },
            {
              id: 3,
              name: "Script Writing",
              agent: "✍️ Script Writer",
              input: "Research + Outline",
              output: "Complete video script",
              tools: ["Video templates", "Tone analyzer"],
              icon: "✍️",
              color: "bg-green-500"
            },
            {
              id: 4,
              name: "Video Production",
              agent: "🎬 Production Agent",
              input: "Script + Visual guidelines",
              output: "Video production brief",
              tools: ["Video tools", "Asset library"],
              icon: "🎬",
              color: "bg-pink-500"
            }
          ],
          usedBy: ["Amilon", "Novavista"],
          channels: [
            { name: "Slack", icon: "💬", tooltip: "Delivers results via DM or #video channel" },
            { name: "Email", icon: "✉️", tooltip: "Sends video assets and brief" }
          ]
        };
      case "Market Analysis Workflow":
        return {
          description: "Comprehensive market analysis with competitor research and sentiment tracking",
          estimatedTime: 25,
          tasks: [
            {
              id: 1,
              name: "Context Setting",
              agent: "🧠 Context Setter",
              input: "Market parameters + Objectives",
              output: "Analysis framework",
              tools: ["Market databases", "Industry reports"],
              icon: "🧠",
              color: "bg-blue-500"
            },
            {
              id: 2,
              name: "Competitor Research",
              agent: "🔍 Research Agent",
              input: "Market context + Competitor list",
              output: "Competitive landscape data",
              tools: ["Web scraping", "API integrations"],
              icon: "🔍",
              color: "bg-red-500"
            },
            {
              id: 3,
              name: "Sentiment Analysis",
              agent: "💝 Sentiment Agent",
              input: "Market data + Social signals",
              output: "Sentiment insights",
              tools: ["Social monitoring", "NLP analysis"],
              icon: "💝",
              color: "bg-pink-500"
            },
            {
              id: 4,
              name: "Report Generation",
              agent: "📊 Report Agent",
              input: "All analysis data",
              output: "Executive market report",
              tools: ["Report templates", "Data visualization"],
              icon: "📊",
              color: "bg-indigo-500"
            }
          ],
          usedBy: ["Enterprise clients"],
          channels: [
            { name: "Email", icon: "✉️", tooltip: "Sends detailed market reports" },
            { name: "Slack", icon: "💬", tooltip: "Delivers summary updates" }
          ]
        };
      default:
        return {
          description: "Automated workflow execution",
          estimatedTime: 15,
          tasks: [],
          usedBy: [],
          channels: []
        };
    }
  };

  const details = getWorkflowDetails(workflow.name);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3 text-xl">
            <span className="text-2xl">{workflow.icon}</span>
            <div>
              <span className="text-fylle-dark">{workflow.name}</span>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="outline" className="text-xs">
                  <Clock className="w-3 h-3 mr-1" />
                  ~{details.estimatedTime} minutes
                </Badge>
                <Badge variant="outline" className="text-xs">
                  <Users className="w-3 h-3 mr-1" />
                  {details.tasks.length} tasks
                </Badge>
              </div>
            </div>
          </DialogTitle>
          <DialogDescription>
            {details.description}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          <Separator />

          {/* Workflow Tasks */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-4 flex items-center space-x-2">
              <span>Workflow Steps</span>
            </h3>
            
            <div className="space-y-4">
              {details.tasks.map((task, index) => (
                <div key={task.id} className="relative">
                  {/* Connecting Line */}
                  {index < details.tasks.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-300"></div>
                  )}
                  
                  <div className="bg-gray-50 rounded-xl p-4 border-l-4 border-fylle-green">
                    <div className="flex items-start space-x-4">
                      {/* Task Number */}
                      <div className={`w-12 h-12 ${task.color} rounded-xl flex items-center justify-center text-white font-bold flex-shrink-0`}>
                        {task.id}
                      </div>
                      
                      {/* Task Details */}
                      <div className="flex-1">
                        <h4 className="font-semibold text-fylle-dark mb-2">Task {task.id}: {task.name}</h4>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          {/* Agent */}
                          <div>
                            <div className="text-fylle-gray font-medium mb-1">├─ Agent:</div>
                            <div className="text-fylle-dark pl-3">{task.agent}</div>
                          </div>
                          
                          {/* Input */}
                          <div>
                            <div className="text-fylle-gray font-medium mb-1">├─ Input:</div>
                            <div className="text-fylle-dark pl-3">{task.input}</div>
                          </div>
                          
                          {/* Output */}
                          <div>
                            <div className="text-fylle-gray font-medium mb-1">├─ Output:</div>
                            <div className="text-fylle-dark pl-3">{task.output}</div>
                          </div>
                        </div>
                        
                        {/* Tools */}
                        <div className="mt-3">
                          <div className="text-fylle-gray font-medium mb-2">└─ Tools:</div>
                          <div className="flex flex-wrap gap-2 pl-3">
                            {task.tools.map((tool, toolIndex) => (
                              <Badge key={toolIndex} variant="secondary" className="text-xs">
                                {tool}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Output Channels */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-3">Output Channels:</h3>
            <div className="flex space-x-4">
              {details.channels.map((channel, index) => (
                <div key={index} className="group relative">
                  <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <span className="text-lg">{channel.icon}</span>
                    <span className="text-sm font-medium text-fylle-dark">{channel.name}</span>
                  </div>
                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
                    {channel.tooltip}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Usage Information */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-3">Used by:</h3>
            <div className="flex space-x-2">
              {details.usedBy.map((client, index) => (
                <Badge key={index} variant="outline" className="text-sm">
                  {client}
                </Badge>
              ))}
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button variant="default" className="flex-1 bg-fylle-green hover:bg-green-600">
              <Play className="w-4 h-4 mr-2" />
              Run Workflow
            </Button>
            <Button variant="outline" className="flex-1">
              <Settings className="w-4 h-4 mr-2" />
              Customize
            </Button>
            <Button variant="outline" className="flex-1">
              <Calendar className="w-4 h-4 mr-2" />
              Schedule
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}