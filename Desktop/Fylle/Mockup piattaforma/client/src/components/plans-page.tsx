import React from 'react';
import { Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function PlansPage() {
  const plans = [
    {
      name: "Seed",
      price: "€250",
      period: "/12 months",
      description: "Perfect for startups getting started with AI",
      features: {
        customPlan: false,
        integrations: 5,
        agents: 3,
        workflowRuns: 1000,
        additionalFeatures: [
          "Basic knowledge base",
          "Email support",
          "Standard templates",
          "Basic analytics"
        ]
      },
      current: false,
      popular: false
    },
    {
      name: "<PERSON>",
      price: "€500",
      period: "/12 months",
      description: "Ideal for growing businesses scaling their AI operations",
      features: {
        customPlan: false,
        integrations: 15,
        agents: 8,
        workflowRuns: 5000,
        additionalFeatures: [
          "Advanced knowledge base",
          "Priority email support",
          "Custom templates",
          "Advanced analytics",
          "API access"
        ]
      },
      current: true,
      popular: true
    },
    {
      name: "Pulse",
      price: "€800",
      period: "/12 months",
      description: "For established companies with complex AI workflows",
      features: {
        customPlan: true,
        integrations: 25,
        agents: 15,
        workflowRuns: 15000,
        additionalFeatures: [
          "Enterprise knowledge base",
          "Phone & email support",
          "Unlimited custom templates",
          "Real-time analytics",
          "Full API access",
          "Custom integrations"
        ]
      },
      current: false,
      popular: false
    },
    {
      name: "Mind",
      price: "€2000",
      period: "/12 months",
      description: "Enterprise solution for large-scale AI automation",
      features: {
        customPlan: true,
        integrations: "Unlimited",
        agents: "Unlimited",
        workflowRuns: "Unlimited",
        additionalFeatures: [
          "White-label solution",
          "Dedicated account manager",
          "24/7 phone support",
          "Custom development",
          "On-premise deployment",
          "Advanced security features",
          "SLA guarantees"
        ]
      },
      current: false,
      popular: false
    }
  ];

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-semibold text-fylle-dark mb-2">Subscription Plans</h1>
        <p className="text-fylle-gray">Choose the perfect plan for your AI automation needs</p>
      </div>

      {/* Current Plan Overview */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 mb-8 border border-blue-100">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-fylle-dark">Current Plan: Bloom</h3>
            <p className="text-fylle-gray">Active until December 2025</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-fylle-dark">€500</div>
            <div className="text-sm text-fylle-gray">/12 months</div>
          </div>
        </div>
      </div>

      {/* Plans Comparison */}
      <div className="grid grid-cols-4 gap-6">
        {plans.map((plan, index) => (
          <div
            key={index}
            className={`bg-fylle-card rounded-xl p-6 apple-shadow border-2 transition-apple relative ${
              plan.current 
                ? 'border-blue-500 bg-blue-50' 
                : plan.popular 
                ? 'border-purple-500' 
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Most Popular
                </span>
              </div>
            )}

            {plan.current && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Current Plan
                </span>
              </div>
            )}

            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-fylle-dark mb-2">{plan.name}</h3>
              <div className="text-3xl font-bold text-fylle-dark mb-1">
                {plan.price}
                <span className="text-base font-normal text-fylle-gray">{plan.period}</span>
              </div>
              <p className="text-sm text-fylle-gray">{plan.description}</p>
            </div>

            {/* Core Features */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center justify-between text-sm">
                <span className="text-fylle-gray">Custom Plan</span>
                <div className="flex items-center">
                  {plan.features.customPlan ? (
                    <Check className="w-4 h-4 text-green-500" />
                  ) : (
                    <X className="w-4 h-4 text-red-500" />
                  )}
                </div>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-fylle-gray">Integrations</span>
                <span className="font-medium text-fylle-dark">{plan.features.integrations}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-fylle-gray">AI Agents</span>
                <span className="font-medium text-fylle-dark">{plan.features.agents}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-fylle-gray">Workflow Runs</span>
                <span className="font-medium text-fylle-dark">{plan.features.workflowRuns}</span>
              </div>
            </div>

            {/* Additional Features */}
            <div className="border-t border-gray-200 pt-4 mb-6">
              <div className="space-y-2">
                {plan.features.additionalFeatures.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center space-x-2 text-sm">
                    <Check className="w-3 h-3 text-green-500 flex-shrink-0" />
                    <span className="text-fylle-gray">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Button */}
            <Button
              className={`w-full py-2 rounded-lg font-medium transition-apple ${
                plan.current
                  ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                  : 'bg-fylle-button text-white hover:bg-fylle-button-hover'
              }`}
              disabled={plan.current}
            >
              {plan.current ? 'Current Plan' : 'Upgrade'}
            </Button>
          </div>
        ))}
      </div>

      {/* Usage Statistics */}
      <div className="mt-12">
        <h2 className="text-xl font-semibold text-fylle-dark mb-6">Current Usage</h2>
        <div className="grid grid-cols-3 gap-6">
          <div className="bg-fylle-card rounded-xl p-6 apple-shadow">
            <div className="flex items-center justify-between mb-2">
              <span className="text-fylle-gray">Integrations Used</span>
              <span className="text-2xl font-bold text-fylle-dark">8/15</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{width: '53%'}}></div>
            </div>
          </div>

          <div className="bg-fylle-card rounded-xl p-6 apple-shadow">
            <div className="flex items-center justify-between mb-2">
              <span className="text-fylle-gray">AI Agents Active</span>
              <span className="text-2xl font-bold text-fylle-dark">5/8</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{width: '62%'}}></div>
            </div>
          </div>

          <div className="bg-fylle-card rounded-xl p-6 apple-shadow">
            <div className="flex items-center justify-between mb-2">
              <span className="text-fylle-gray">Workflow Runs</span>
              <span className="text-2xl font-bold text-fylle-dark">3.2K/5K</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-purple-500 h-2 rounded-full" style={{width: '64%'}}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact for Custom Plans */}
      <div className="mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-6 border border-purple-100">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-fylle-dark mb-2">Need a Custom Solution?</h3>
          <p className="text-fylle-gray mb-4">
            Contact our sales team for enterprise pricing and custom integrations
          </p>
          <Button className="bg-purple-500 text-white hover:bg-purple-600 px-6 py-2 rounded-lg">
            Contact Sales
          </Button>
        </div>
      </div>
    </div>
  );
}