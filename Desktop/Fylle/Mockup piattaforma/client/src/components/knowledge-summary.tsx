import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Search, Award, Book, Briefcase, Lightbulb, Users, Target, Filter, Eye, FileText } from 'lucide-react';

interface KnowledgeSummaryProps {
  knowledgeStats: {
    totalNodes: number;
    totalCalls: number;
    optimizationLevel: number;
    lastUpdate: string;
  };
  uploadMutation: any;
}

export function KnowledgeSummary({ knowledgeStats, uploadMutation }: KnowledgeSummaryProps) {
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [searchQuery, setSearchQuery] = useState('');

  // Mock knowledge data organized by categories for content production
  const knowledgeByCategory = {
    'Competitor': [
      {
        title: 'Amazon Marketing Strategy',
        type: 'Competitive Analysis',
        size: '3.2 MB',
        items: 125,
        lastUpdated: '2 hours ago',
        status: 'active',
        insights: 'Prime Day strategies and pricing models'
      },
      {
        title: 'Apple Brand Positioning',
        type: 'Brand Study', 
        size: '2.1 MB',
        items: 89,
        lastUpdated: '1 day ago',
        status: 'active',
        insights: 'Premium positioning and minimalist approach'
      },
      {
        title: 'Nike Social Media Campaigns',
        type: 'Campaign Analysis',
        size: '4.8 MB',
        items: 234,
        lastUpdated: '6 hours ago',
        status: 'processing',
        insights: 'Athlete endorsements driving engagement'
      }
    ],
    'Brand Book': [
      {
        title: 'Logo Assets & Guidelines',
        type: 'Visual Identity',
        size: '15.3 MB',
        items: 67,
        lastUpdated: '3 days ago',
        status: 'completed',
        insights: 'Primary logo with 12 variations'
      },
      {
        title: 'Font Family & Typography',
        type: 'Typography System',
        size: '2.4 MB',
        items: 18,
        lastUpdated: '1 week ago',
        status: 'active',
        insights: 'Montserrat + Open Sans combination'
      },
      {
        title: 'Tone of Voice Guidelines',
        type: 'Communication Style',
        size: '1.8 MB',
        items: 45,
        lastUpdated: '4 days ago',
        status: 'active',
        insights: 'Professional yet approachable tone'
      },
      {
        title: 'Color Palette & Usage',
        type: 'Brand Colors',
        size: '0.9 MB',
        items: 12,
        lastUpdated: '1 week ago',
        status: 'completed',
        insights: 'Primary blue with secondary accents'
      }
    ],
    'Audience': [
      {
        title: 'Marketing Manager Persona',
        type: 'User Persona',
        size: '2.7 MB',
        items: 34,
        lastUpdated: '2 days ago',
        status: 'active',
        insights: '28-45 years, focused on ROI and efficiency'
      },
      {
        title: 'C-Level Executive Persona',
        type: 'User Persona',
        size: '3.1 MB',
        items: 28,
        lastUpdated: '1 day ago',
        status: 'active',
        insights: '40-60 years, strategic decision makers'
      },
      {
        title: 'Small Business Owner Persona',
        type: 'User Persona',
        size: '2.2 MB',
        items: 41,
        lastUpdated: '3 days ago',
        status: 'active',
        insights: 'Budget conscious, hands-on approach'
      }
    ],
    'Product/Servizio': [
      {
        title: 'AI Workflow Platform',
        type: 'Core Product',
        size: '4.2 MB',
        items: 156,
        lastUpdated: '1 day ago',
        status: 'active',
        insights: '85% user satisfaction, 40% efficiency gain'
      },
      {
        title: 'Content Generation Suite',
        type: 'Feature Set',
        size: '3.8 MB',
        items: 98,
        lastUpdated: '6 hours ago',
        status: 'active',
        insights: 'Blog posts, social media, email campaigns'
      },
      {
        title: 'Integration Hub',
        type: 'Platform Service',
        size: '2.9 MB',
        items: 67,
        lastUpdated: '12 hours ago',
        status: 'processing',
        insights: '25+ integrations, 99.9% uptime'
      },
      {
        title: 'Analytics Dashboard',
        type: 'Reporting Tool',
        size: '3.5 MB',
        items: 89,
        lastUpdated: '2 hours ago',
        status: 'active',
        insights: 'Real-time metrics, custom reports'
      }
    ],
    'Mission': [
      {
        title: 'Company Vision Statement',
        type: 'Strategic Direction',
        size: '1.2 MB',
        items: 15,
        lastUpdated: '1 month ago',
        status: 'completed',
        insights: 'Democratizing AI for content creation'
      },
      {
        title: 'Core Values & Principles',
        type: 'Company Culture',
        size: '1.8 MB',
        items: 23,
        lastUpdated: '2 weeks ago',
        status: 'active',
        insights: 'Innovation, transparency, user-centricity'
      },
      {
        title: 'Impact Metrics & Goals',
        type: 'Success Metrics',
        size: '2.3 MB',
        items: 32,
        lastUpdated: '1 week ago',
        status: 'active',
        insights: '10K+ users, 50% time savings achieved'
      }
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-600';
      case 'processing': return 'bg-blue-100 text-blue-600';
      case 'completed': return 'bg-gray-100 text-gray-600';
      case 'urgent': return 'bg-red-100 text-red-600';
      case 'attention': return 'bg-yellow-100 text-yellow-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Competitor': return <Award className="w-5 h-5 text-green-500" />;
      case 'Brand Book': return <Book className="w-5 h-5 text-purple-500" />;
      case 'Audience': return <Users className="w-5 h-5 text-orange-500" />;
      case 'Product/Servizio': return <Briefcase className="w-5 h-5 text-blue-500" />;
      case 'Mission': return <Lightbulb className="w-5 h-5 text-indigo-500" />;
      default: return null;
    }
  };

  const allKnowledge = Object.values(knowledgeByCategory).flat();
  const filteredKnowledge = selectedCategory === 'All Categories' 
    ? allKnowledge 
    : knowledgeByCategory[selectedCategory as keyof typeof knowledgeByCategory] || [];

  const totalItems = filteredKnowledge.length;

  return (
    <div>
      {/* Header with filters */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-semibold text-fylle-dark">Knowledge</h2>
          <span className="text-sm text-fylle-gray">{totalItems} items</span>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4 mb-8">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-fylle-gray w-4 h-4" />
          <input
            type="text"
            placeholder="Search knowledge..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
          />
        </div>
        
        <select 
          value={selectedCategory} 
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white min-w-48"
        >
          <option value="All Categories">All Categories</option>
          <option value="Competitor">Competitor</option>
          <option value="Brand Book">Brand Book</option>
          <option value="Audience">Audience</option>
          <option value="Product/Servizio">Product/Servizio</option>
          <option value="Mission">Mission</option>
        </select>
      </div>

      {/* Knowledge Cards */}
      <div className="grid grid-cols-2 gap-6">
        {filteredKnowledge.map((knowledge, index) => {
          const category = Object.keys(knowledgeByCategory).find(cat => 
            knowledgeByCategory[cat as keyof typeof knowledgeByCategory].includes(knowledge)
          );
          
          return (
            <div key={index} className="bg-fylle-card rounded-xl p-6 apple-shadow hover:apple-shadow-hover transition-apple">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {getCategoryIcon(category || '')}
                  <div>
                    <h3 className="font-semibold text-fylle-dark">{knowledge.title}</h3>
                    <p className="text-sm text-fylle-gray">{knowledge.type}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(knowledge.status)}`}>
                  {knowledge.status}
                </span>
              </div>

              {/* Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="text-sm text-fylle-gray">Size</div>
                  <div className="font-medium text-fylle-dark">{knowledge.size}</div>
                </div>
                <div>
                  <div className="text-sm text-fylle-gray">Items</div>
                  <div className="font-medium text-fylle-dark">{knowledge.items.toLocaleString()}</div>
                </div>
              </div>

              {/* Insights */}
              <div className="bg-blue-50 rounded-lg p-3 mb-4">
                <div className="text-sm font-medium text-blue-700 mb-1">Key Insight</div>
                <div className="text-sm text-blue-600">{knowledge.insights}</div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between text-sm text-fylle-gray">
                <span>Updated {knowledge.lastUpdated}</span>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 bg-gray-100 rounded text-xs">{category}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredKnowledge.length === 0 && (
        <div className="text-center py-12">
          <div className="text-fylle-gray mb-4">No knowledge items found</div>
          <Button 
            onClick={() => setIsUploadModalOpen(true)}
            variant="outline"
          >
            Add your first knowledge item
          </Button>
        </div>
      )}


    </div>
  );
}