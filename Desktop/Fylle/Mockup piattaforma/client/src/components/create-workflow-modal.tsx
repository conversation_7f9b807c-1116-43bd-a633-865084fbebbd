import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import { Plus, X, Clock, Users, Settings, Save } from "lucide-react";

interface CreateWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Task {
  id: string;
  name: string;
  agent: string;
  input: string;
  output: string;
  tools: string[];
  estimatedTime: number;
}

const availableAgents = [
  { id: "context-setter", name: "🧠 Context Setter", description: "Sets up context and brand guidelines" },
  { id: "web-agent", name: "🔍 Web Agent", description: "Performs web research and data collection" },
  { id: "copywriter", name: "✍️ Copywriter", description: "Creates written content with brand voice" },
  { id: "research-agent", name: "📊 Research Agent", description: "Analyzes data and generates insights" },
  { id: "social-agent", name: "📱 Social Agent", description: "Manages social media content and posting" },
  { id: "seo-agent", name: "🎯 SEO Agent", description: "Optimizes content for search engines" },
  { id: "video-agent", name: "🎬 Video Agent", description: "Handles video content creation and editing" },
  { id: "email-agent", name: "✉️ Email Agent", description: "Creates and manages email campaigns" }
];

const availableTools = [
  "Knowledge Base API", "Brand Guidelines", "Web Scraping", "Google API", "Competitor Analysis",
  "Writing Engine", "SEO Checker", "Brand Voice", "Social Media APIs", "Analytics Dashboard",
  "Content Templates", "Image Generator", "Video Editor", "Email Templates", "CRM Integration",
  "Market Research Tools", "Sentiment Analysis", "Data Visualization", "Report Generator"
];

const outputChannels = [
  { id: "slack", name: "Slack", icon: "💬", description: "Send results to Slack channels" },
  { id: "email", name: "Email", icon: "✉️", description: "Email formatted reports" },
  { id: "discord", name: "Discord", icon: "🎮", description: "Post to Discord workspace" },
  { id: "dashboard", name: "Dashboard", icon: "📊", description: "Update dashboard metrics" },
  { id: "api", name: "API", icon: "🔗", description: "Send via webhook/API" }
];

export default function CreateWorkflowModal({ isOpen, onClose }: CreateWorkflowModalProps) {
  const [workflowName, setWorkflowName] = useState("");
  const [workflowDescription, setWorkflowDescription] = useState("");
  const [tasks, setTasks] = useState<Task[]>([]);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [currentTask, setCurrentTask] = useState<Partial<Task>>({
    name: "",
    agent: "",
    input: "",
    output: "",
    tools: [],
    estimatedTime: 5
  });

  const addTask = () => {
    if (currentTask.name && currentTask.agent) {
      const newTask: Task = {
        id: Date.now().toString(),
        name: currentTask.name || "",
        agent: currentTask.agent || "",
        input: currentTask.input || "",
        output: currentTask.output || "",
        tools: currentTask.tools || [],
        estimatedTime: currentTask.estimatedTime || 5
      };
      setTasks([...tasks, newTask]);
      setCurrentTask({
        name: "",
        agent: "",
        input: "",
        output: "",
        tools: [],
        estimatedTime: 5
      });
    }
  };

  const removeTask = (taskId: string) => {
    setTasks(tasks.filter(task => task.id !== taskId));
  };

  const addToolToCurrentTask = (tool: string) => {
    if (!currentTask.tools?.includes(tool)) {
      setCurrentTask({
        ...currentTask,
        tools: [...(currentTask.tools || []), tool]
      });
    }
  };

  const removeToolFromCurrentTask = (tool: string) => {
    setCurrentTask({
      ...currentTask,
      tools: currentTask.tools?.filter(t => t !== tool) || []
    });
  };

  const toggleChannel = (channelId: string) => {
    setSelectedChannels(prev =>
      prev.includes(channelId)
        ? prev.filter(id => id !== channelId)
        : [...prev, channelId]
    );
  };

  const totalEstimatedTime = tasks.reduce((sum, task) => sum + task.estimatedTime, 0);

  const handleSave = () => {
    // Simula il salvataggio del workflow
    console.log("Saving workflow:", {
      name: workflowName,
      description: workflowDescription,
      tasks,
      channels: selectedChannels,
      estimatedTime: totalEstimatedTime
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3 text-xl">
            <span className="text-2xl">⚡</span>
            <span className="text-fylle-dark">Create New Workflow</span>
          </DialogTitle>
          <DialogDescription>
            Build a custom AI workflow with multiple tasks, agents, and tools
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Workflow Basic Info */}
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="workflow-name">Workflow Name</Label>
                <Input
                  id="workflow-name"
                  value={workflowName}
                  onChange={(e) => setWorkflowName(e.target.value)}
                  placeholder="e.g., Social Media Campaign Workflow"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="workflow-description">Description</Label>
                <Textarea
                  id="workflow-description"
                  value={workflowDescription}
                  onChange={(e) => setWorkflowDescription(e.target.value)}
                  placeholder="Describe what this workflow accomplishes..."
                  className="mt-1"
                  rows={3}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-fylle-gray" />
                    <span>Total estimated time:</span>
                  </div>
                  <Badge variant="outline">
                    ~{totalEstimatedTime} minutes
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm mt-2">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-fylle-gray" />
                    <span>Total tasks:</span>
                  </div>
                  <Badge variant="outline">
                    {tasks.length} tasks
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Task Creation Section */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-4">Add Workflow Tasks</h3>
            
            <div className="bg-gray-50 rounded-xl p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="task-name">Task Name</Label>
                  <Input
                    id="task-name"
                    value={currentTask.name || ""}
                    onChange={(e) => setCurrentTask({...currentTask, name: e.target.value})}
                    placeholder="e.g., Content Research"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="task-agent">Select Agent</Label>
                  <Select 
                    value={currentTask.agent || ""} 
                    onValueChange={(value) => setCurrentTask({...currentTask, agent: value})}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Choose an agent..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableAgents.map((agent) => (
                        <SelectItem key={agent.id} value={agent.name}>
                          <div className="flex flex-col">
                            <span>{agent.name}</span>
                            <span className="text-xs text-gray-500">{agent.description}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="task-input">Input Description</Label>
                  <Input
                    id="task-input"
                    value={currentTask.input || ""}
                    onChange={(e) => setCurrentTask({...currentTask, input: e.target.value})}
                    placeholder="What data/context does this task need?"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="task-output">Output Description</Label>
                  <Input
                    id="task-output"
                    value={currentTask.output || ""}
                    onChange={(e) => setCurrentTask({...currentTask, output: e.target.value})}
                    placeholder="What will this task produce?"
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label>Tools & APIs</Label>
                <div className="mt-2 flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {availableTools.map((tool) => (
                    <Button
                      key={tool}
                      variant={currentTask.tools?.includes(tool) ? "default" : "outline"}
                      size="sm"
                      onClick={() => 
                        currentTask.tools?.includes(tool) 
                          ? removeToolFromCurrentTask(tool)
                          : addToolToCurrentTask(tool)
                      }
                      className="text-xs"
                    >
                      {tool}
                    </Button>
                  ))}
                </div>
                {currentTask.tools && currentTask.tools.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {currentTask.tools.map((tool) => (
                      <Badge key={tool} variant="secondary" className="text-xs">
                        {tool}
                        <X 
                          className="w-3 h-3 ml-1 cursor-pointer" 
                          onClick={() => removeToolFromCurrentTask(tool)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="task-time">Estimated Time (minutes):</Label>
                  <Input
                    id="task-time"
                    type="number"
                    value={currentTask.estimatedTime || 5}
                    onChange={(e) => setCurrentTask({...currentTask, estimatedTime: parseInt(e.target.value) || 5})}
                    className="w-20"
                    min="1"
                    max="120"
                  />
                </div>

                <Button 
                  onClick={addTask} 
                  disabled={!currentTask.name || !currentTask.agent}
                  className="bg-fylle-button hover:bg-fylle-button-hover"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Task
                </Button>
              </div>
            </div>
          </div>

          {/* Current Tasks List */}
          {tasks.length > 0 && (
            <div>
              <h3 className="font-semibold text-fylle-dark mb-4">Workflow Tasks ({tasks.length})</h3>
              <div className="space-y-3">
                {tasks.map((task, index) => (
                  <div key={task.id} className="bg-white border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <div className="w-8 h-8 bg-fylle-green rounded-full flex items-center justify-center text-white font-bold text-sm">
                            {index + 1}
                          </div>
                          <h4 className="font-semibold text-fylle-dark">{task.name}</h4>
                          <Badge variant="outline" className="text-xs">
                            {task.estimatedTime}min
                          </Badge>
                        </div>
                        
                        <div className="ml-11 space-y-1 text-sm">
                          <div><span className="font-medium">Agent:</span> {task.agent}</div>
                          <div><span className="font-medium">Input:</span> {task.input}</div>
                          <div><span className="font-medium">Output:</span> {task.output}</div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">Tools:</span>
                            <div className="flex flex-wrap gap-1">
                              {task.tools.map((tool) => (
                                <Badge key={tool} variant="secondary" className="text-xs">{tool}</Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeTask(task.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <Separator />

          {/* Output Channels */}
          <div>
            <h3 className="font-semibold text-fylle-dark mb-4">Output Channels</h3>
            <div className="grid grid-cols-5 gap-3">
              {outputChannels.map((channel) => (
                <div
                  key={channel.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedChannels.includes(channel.id)
                      ? 'border-fylle-green bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => toggleChannel(channel.id)}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">{channel.icon}</div>
                    <div className="font-medium text-sm">{channel.name}</div>
                    <div className="text-xs text-gray-500 mt-1">{channel.description}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <Button 
              onClick={handleSave}
              disabled={!workflowName || tasks.length === 0}
              className="flex-1 bg-fylle-button hover:bg-fylle-button-hover"
            >
              <Save className="w-4 h-4 mr-2" />
              Create Workflow
            </Button>
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}