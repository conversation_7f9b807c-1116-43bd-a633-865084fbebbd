import React from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Plug, Bo<PERSON> } from 'lucide-react';

interface SidebarProps {
  activeSection: 'knowledge' | 'integrations' | 'agents';
  onSectionChange: (section: 'knowledge' | 'integrations' | 'agents') => void;
}

export function Sidebar({ activeSection, onSectionChange }: SidebarProps) {
  const navItems = [
    { 
      id: 'knowledge' as const, 
      label: 'Knowledge Base', 
      icon: Brain,
      iconClass: 'fas fa-brain'
    },
    { 
      id: 'integrations' as const, 
      label: 'Integrations', 
      icon: Plug,
      iconClass: 'fas fa-plug'
    },
    { 
      id: 'agents' as const, 
      label: 'AI Agents', 
      icon: Bot,
      iconClass: 'fas fa-robot'
    }
  ];

  return (
    <aside className="w-64 bg-fylle-card border-r border-gray-200">
      <nav className="p-4 space-y-1">
        {navItems.map((item) => {
          const isActive = activeSection === item.id;
          return (
            <Button
              key={item.id}
              onClick={() => onSectionChange(item.id)}
              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-apple ${
                isActive 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-transparent text-fylle-dark hover:bg-gray-50'
              }`}
              variant="ghost"
            >
              <i className={`${item.iconClass} text-base`}></i>
              <span className="font-medium">{item.label}</span>
            </Button>
          );
        })}
      </nav>
    </aside>
  );
}
