import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { CloudUpload, X, FileText, Zap, MessageSquare, Search, Calendar, Bot, Link } from 'lucide-react';
import { UseMutationResult } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

interface AddKnowledgeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (data: { file: File; name: string; category: string }) => void;
  isUploading: boolean;
}

type KnowledgeType = 'document' | 'workflow' | 'prompt' | 'question' | 'schedule' | 'api';

export function AddKnowledgeModal({ isOpen, onClose, onUpload, isUploading }: AddKnowledgeModalProps) {
  const [knowledgeType, setKnowledgeType] = useState<KnowledgeType>('document');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [name, setName] = useState('');
  const [category, setCategory] = useState('');
  const [prompt, setPrompt] = useState('');
  const [question, setQuestion] = useState('');
  const [workflowType, setWorkflowType] = useState('');
  const [scheduleDetails, setScheduleDetails] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const knowledgeTypes = [
    {
      id: 'document' as KnowledgeType,
      title: 'Upload Documents',
      description: 'Add PDF, Word, text files or markdown documents',
      icon: FileText,
      color: 'bg-blue-100 text-blue-600'
    },
    {
      id: 'workflow' as KnowledgeType,
      title: 'AI Workflow',
      description: 'Create automated research or data collection workflows',
      icon: Zap,
      color: 'bg-purple-100 text-purple-600'
    },
    {
      id: 'prompt' as KnowledgeType,
      title: 'Natural Language Request',
      description: 'Give instructions in plain language for AI to process',
      icon: MessageSquare,
      color: 'bg-green-100 text-green-600'
    },
    {
      id: 'question' as KnowledgeType,
      title: 'Ask a Question',
      description: 'Get answers from your existing knowledge base',
      icon: Search,
      color: 'bg-orange-100 text-orange-600'
    },
    {
      id: 'schedule' as KnowledgeType,
      title: 'Schedule Reports',
      description: 'Set up automated reports for specific channels',
      icon: Calendar,
      color: 'bg-pink-100 text-pink-600'
    },
    {
      id: 'api' as KnowledgeType,
      title: 'Connect API',
      description: 'Connect external APIs and data sources to your knowledge base',
      icon: Link,
      color: 'bg-teal-100 text-teal-600'
    }
  ];

  const workflowOptions = [
    { value: 'competitor-research', label: 'Competitor Research Workflow' },
    { value: 'market-analysis', label: 'Market Analysis Workflow' },
    { value: 'content-audit', label: 'Content Audit Workflow' },
    { value: 'social-monitoring', label: 'Social Media Monitoring' },
    { value: 'brand-sentiment', label: 'Brand Sentiment Analysis' }
  ];

  const handleReset = () => {
    setSelectedFile(null);
    setName('');
    setCategory('');
    setPrompt('');
    setQuestion('');
    setWorkflowType('');
    setScheduleDetails('');
    setKnowledgeType('document');
  };

  const handleClose = () => {
    handleReset();
    onClose();
  };

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    if (!name) {
      setName(file.name.replace(/\.[^/.]+$/, ""));
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleSubmit = () => {
    if (knowledgeType === 'document' && selectedFile && name && category) {
      onUpload({ file: selectedFile, name, category });
      handleClose();
    } else if (knowledgeType !== 'document' && name) {
      // Simulate other knowledge types
      toast({
        title: "Knowledge Added",
        description: `${knowledgeTypes.find(t => t.id === knowledgeType)?.title} has been processed successfully`,
      });
      handleClose();
    }
  };

  const isFormValid = () => {
    if (knowledgeType === 'document') {
      return selectedFile && name && category;
    }
    return name && (
      (knowledgeType === 'workflow' && workflowType) ||
      (knowledgeType === 'prompt' && prompt) ||
      (knowledgeType === 'question' && question) ||
      (knowledgeType === 'schedule' && scheduleDetails) ||
      knowledgeType === 'document'
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Bot className="w-6 h-6 text-fylle-green" />
            <span>Add Knowledge to your AI System</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Knowledge Type Selection */}
          <div>
            <Label className="text-sm font-medium text-fylle-dark mb-3 block">How would you like to add knowledge?</Label>
            <div className="grid grid-cols-2 gap-3">
              {knowledgeTypes.map((type) => {
                const IconComponent = type.icon;
                return (
                  <div
                    key={type.id}
                    onClick={() => setKnowledgeType(type.id)}
                    className={`p-4 rounded-xl border-2 cursor-pointer transition-all ${
                      knowledgeType === type.id
                        ? 'border-fylle-button bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${type.color}`}>
                        <IconComponent className="w-5 h-5" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-sm text-fylle-dark">{type.title}</h3>
                        <p className="text-xs text-fylle-gray mt-1">{type.description}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Form Fields Based on Type */}
          <div className="space-y-4">
            {/* Common Name Field */}
            <div>
              <Label htmlFor="name" className="text-sm font-medium text-fylle-dark">
                Name *
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder={
                  knowledgeType === 'document' ? 'Document name' :
                  knowledgeType === 'workflow' ? 'Workflow name' :
                  knowledgeType === 'prompt' ? 'Task name' :
                  knowledgeType === 'question' ? 'Question title' :
                  'Report name'
                }
                className="mt-1"
              />
            </div>

            {/* Document Upload */}
            {knowledgeType === 'document' && (
              <>
                <div>
                  <Label className="text-sm font-medium text-fylle-dark">Category *</Label>
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="performance">Performance</SelectItem>
                      <SelectItem value="insights">Insights</SelectItem>
                      <SelectItem value="operations">Operations</SelectItem>
                      <SelectItem value="brand">Brand DNA</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium text-fylle-dark">Upload File *</Label>
                  <div
                    className={`mt-1 border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                      isDragOver ? 'border-fylle-green bg-green-50' : 'border-gray-300'
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    {selectedFile ? (
                      <div className="flex items-center justify-center space-x-3">
                        <FileText className="w-8 h-8 text-fylle-green" />
                        <div>
                          <p className="font-medium text-fylle-dark">{selectedFile.name}</p>
                          <p className="text-sm text-fylle-gray">
                            {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedFile(null)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ) : (
                      <div>
                        <CloudUpload className="w-12 h-12 text-fylle-gray mx-auto mb-4" />
                        <p className="text-fylle-dark font-medium mb-2">
                          Drop your file here or{' '}
                          <Button
                            variant="link"
                            className="p-0 h-auto text-fylle-green"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            browse
                          </Button>
                        </p>
                        <p className="text-sm text-fylle-gray">
                          Supports .md, .txt, .pdf, .docx files
                        </p>
                      </div>
                    )}
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept=".md,.txt,.pdf,.docx"
                    onChange={(e) => {
                      if (e.target.files?.[0]) {
                        handleFileSelect(e.target.files[0]);
                      }
                    }}
                  />
                </div>
              </>
            )}

            {/* Workflow Selection */}
            {knowledgeType === 'workflow' && (
              <div>
                <Label className="text-sm font-medium text-fylle-dark">Workflow Type *</Label>
                <Select value={workflowType} onValueChange={setWorkflowType}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Choose a workflow type" />
                  </SelectTrigger>
                  <SelectContent>
                    {workflowOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-fylle-gray mt-2">
                  AI agents will execute this workflow and add results to your knowledge base
                </p>
              </div>
            )}

            {/* Natural Language Prompt */}
            {knowledgeType === 'prompt' && (
              <div>
                <Label className="text-sm font-medium text-fylle-dark">Describe what you need *</Label>
                <Textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Example: Create a weekly report on social media performance for our marketing channels, including engagement rates and top-performing content"
                  className="mt-1 min-h-[100px]"
                />
                <p className="text-xs text-fylle-gray mt-2">
                  AI will interpret your request and create the appropriate knowledge or workflow
                </p>
              </div>
            )}

            {/* Question */}
            {knowledgeType === 'question' && (
              <div>
                <Label className="text-sm font-medium text-fylle-dark">Your Question *</Label>
                <Textarea
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  placeholder="Example: What are our best performing marketing channels and why?"
                  className="mt-1 min-h-[80px]"
                />
                <p className="text-xs text-fylle-gray mt-2">
                  AI will search your knowledge base and provide a comprehensive answer
                </p>
              </div>
            )}

            {/* Schedule Reports */}
            {knowledgeType === 'schedule' && (
              <div>
                <Label className="text-sm font-medium text-fylle-dark">Report Details *</Label>
                <Textarea
                  value={scheduleDetails}
                  onChange={(e) => setScheduleDetails(e.target.value)}
                  placeholder="Example: Weekly performance report for LinkedIn and Meta ads, sent every Monday at 9 <NAME_EMAIL>"
                  className="mt-1 min-h-[80px]"
                />
                <p className="text-xs text-fylle-gray mt-2">
                  Specify frequency, channels, and recipients for automated reports
                </p>
              </div>
            )}

            {/* Connect API */}
            {knowledgeType === 'api' && (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-fylle-dark">API Endpoint URL *</Label>
                  <Input
                    placeholder="https://api.example.com/v1/data"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium text-fylle-dark">Authentication</Label>
                  <Select>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select auth method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Authentication</SelectItem>
                      <SelectItem value="api-key">API Key</SelectItem>
                      <SelectItem value="bearer">Bearer Token</SelectItem>
                      <SelectItem value="oauth">OAuth 2.0</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-sm font-medium text-fylle-dark">Data Description</Label>
                  <Textarea
                    placeholder="Describe what data this API provides and how it should be used in your knowledge base"
                    className="mt-1 min-h-[60px]"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Preview/Explanation */}
          {knowledgeType !== 'document' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Bot className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">What happens next?</h4>
                  <p className="text-sm text-blue-700">
                    {knowledgeType === 'workflow' && 'Our AI agents will execute the selected workflow and add the results to your knowledge base.'}
                    {knowledgeType === 'prompt' && 'AI will interpret your request and either create a workflow, search for information, or generate the content you need.'}
                    {knowledgeType === 'question' && 'AI will analyze your existing knowledge base to provide a comprehensive answer with sources.'}
                    {knowledgeType === 'schedule' && 'The system will set up automated reports according to your specifications.'}
                    {knowledgeType === 'api' && 'The system will establish a connection to your API and periodically sync data to enrich your knowledge base.'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!isFormValid() || isUploading}
              className="flex-1 bg-fylle-button hover:bg-fylle-button-hover"
            >
              {isUploading ? 'Processing...' : 
               knowledgeType === 'document' ? 'Upload' :
               knowledgeType === 'question' ? 'Ask Question' :
               'Create Task'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default AddKnowledgeModal;