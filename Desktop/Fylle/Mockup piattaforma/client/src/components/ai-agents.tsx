import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus } from 'lucide-react';
import type { AIAgent, Workflow } from '@/lib/types';
import AgentDetailModal from './agent-detail-modal';
import WorkflowDetailModal from './workflow-detail-modal';
import CreateWorkflowModal from './create-workflow-modal';
import { Content } from './content';

interface AIAgentsProps {
  aiAgents: AIAgent[];
  workflows: Workflow[];
}

export function AIAgents({ aiAgents, workflows }: AIAgentsProps) {
  const [activeTab, setActiveTab] = useState<'agents' | 'workflows' | 'content'>('agents');
  const [selectedAgent, setSelectedAgent] = useState<AIAgent | null>(null);
  const [isAgentModalOpen, setIsAgentModalOpen] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [isWorkflowModalOpen, setIsWorkflowModalOpen] = useState(false);
  const [isCreateWorkflowModalOpen, setIsCreateWorkflowModalOpen] = useState(false);

  // Mock workflows for the additional cards
  const mockWorkflows = [
    {
      id: 999,
      name: "Video Content Workflow",
      description: "Complete video content creation from script to production",
      icon: "🎬",
      estimatedTime: 30,
      status: "available" as const,
      steps: [],
      usedBy: ["Amilon", "Novavista"]
    },
    {
      id: 998,
      name: "Market Analysis Workflow", 
      description: "Comprehensive market analysis with competitor research",
      icon: "📊",
      estimatedTime: 25,
      status: "available" as const,
      steps: [],
      usedBy: ["Enterprise clients"]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-600';
      case 'running': return 'bg-yellow-100 text-yellow-600';
      case 'configured': return 'bg-blue-100 text-blue-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getStatusLabel = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  return (
    <div className="p-8 ai-agents-section" data-context="AI Agents">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-semibold text-fylle-dark">AI Agents & Workflows</h1>
          <Button 
            onClick={() => setIsCreateWorkflowModalOpen(true)}
            className="px-6 py-3 bg-fylle-button text-fylle-button rounded-xl font-medium hover:bg-fylle-button-hover transition-apple flex items-center space-x-2 apple-shadow hover:apple-shadow-hover"
          >
            <Plus className="w-5 h-5" />
            <span>Create Workflow</span>
          </Button>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-8 bg-gray-100 p-1 rounded-xl w-fit">
          <Button
            onClick={() => setActiveTab('agents')}
            className={`px-6 py-3 rounded-lg font-medium transition-apple ${
              activeTab === 'agents' 
                ? 'bg-white text-fylle-dark apple-shadow' 
                : 'bg-transparent text-fylle-gray hover:text-fylle-dark'
            }`}
          >
            <i className="fas fa-robot mr-2"></i>Agents
          </Button>
          <Button
            onClick={() => setActiveTab('workflows')}
            className={`px-6 py-3 rounded-lg font-medium transition-apple ${
              activeTab === 'workflows' 
                ? 'bg-white text-fylle-dark apple-shadow' 
                : 'bg-transparent text-fylle-gray hover:text-fylle-dark'
            }`}
          >
            <i className="fas fa-project-diagram mr-2"></i>Workflows
          </Button>
          <Button
            onClick={() => setActiveTab('content')}
            className={`px-6 py-3 rounded-lg font-medium transition-apple ${
              activeTab === 'content' 
                ? 'bg-white text-fylle-dark apple-shadow' 
                : 'bg-transparent text-fylle-gray hover:text-fylle-dark'
            }`}
          >
            <i className="fas fa-file-alt mr-2"></i>Content
          </Button>
        </div>

        {/* Agents Tab Content */}
        {activeTab === 'agents' && (
          <div>
            <h2 className="text-xl font-semibold mb-4 text-fylle-dark">Available Agents</h2>
            <div className="grid grid-cols-3 gap-6">
              {aiAgents.map((agent) => (
                <div 
                  key={agent.id} 
                  className="bg-fylle-card rounded-xl p-6 apple-shadow hover:apple-shadow-hover transition-apple cursor-pointer"
                  onClick={() => {
                    setSelectedAgent(agent);
                    setIsAgentModalOpen(true);
                  }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <i className={`${agent.icon} text-blue-600 text-lg`}></i>
                    </div>
                    <Badge className={`text-xs px-2 py-1 rounded-full ${getStatusColor(agent.status)}`}>
                      {getStatusLabel(agent.status)}
                    </Badge>
                  </div>
                  <h3 className="font-semibold mb-2 text-fylle-dark">{agent.name}</h3>
                  <p className="text-sm text-fylle-gray mb-4">{agent.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-1">
                      {agent.channels.includes('slack') && (
                        <i className="fab fa-slack text-fylle-gray text-xs" title="Slack"></i>
                      )}
                      {agent.channels.includes('email') && (
                        <i className="fas fa-envelope text-fylle-gray text-xs" title="Email"></i>
                      )}
                      {agent.channels.includes('discord') && (
                        <i className="fab fa-discord text-fylle-gray text-xs" title="Discord"></i>
                      )}
                    </div>
                    <Button 
                      className={`text-sm ${
                        agent.status === 'running' 
                          ? 'bg-gray-200 text-fylle-gray cursor-not-allowed' 
                          : 'bg-fylle-button text-fylle-button hover:bg-fylle-button-hover'
                      }`}
                      disabled={agent.status === 'running'}
                    >
                      {agent.status === 'running' ? 'Running' : 'Deploy'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Workflows Tab Content */}
        {activeTab === 'workflows' && (
          <div>
            <h2 className="text-xl font-semibold mb-4 text-fylle-dark">Pre-built Workflows</h2>
            <div className="grid grid-cols-2 gap-6">
              {workflows.map((workflow) => (
                <div 
                  key={workflow.id} 
                  className="bg-fylle-card rounded-xl p-6 apple-shadow hover:apple-shadow-hover transition-apple cursor-pointer"
                  onClick={() => {
                    setSelectedWorkflow(workflow);
                    setIsWorkflowModalOpen(true);
                  }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <i className={`${workflow.icon} text-blue-600 text-lg`}></i>
                    </div>
                    <span className="text-xs text-fylle-gray">~{workflow.estimatedTime} minutes</span>
                  </div>
                  <h3 className="font-semibold mb-2 text-fylle-dark">{workflow.name}</h3>
                  <div className="space-y-2 mb-4">
                    {workflow.steps.map((step, index) => (
                      <div key={index} className="flex items-center space-x-2 text-sm">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                          {step.step}
                        </div>
                        <span className="text-fylle-dark">{step.name}</span>
                        <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                        <i className={`${step.icon} text-blue-500`}></i>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-fylle-gray">
                      Used by: {workflow.usedBy.join(', ')}
                    </div>
                    <Button className="bg-fylle-button text-fylle-button hover:bg-fylle-button-hover text-sm">
                      Run Workflow
                    </Button>
                  </div>
                </div>
              ))}

              {/* Additional mock workflows */}
              <div 
                className="bg-fylle-card rounded-xl p-6 apple-shadow hover:apple-shadow-hover transition-apple cursor-pointer"
                onClick={() => {
                  setSelectedWorkflow(mockWorkflows[0]);
                  setIsWorkflowModalOpen(true);
                }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                    <i className="fas fa-video text-red-600 text-lg"></i>
                  </div>
                  <span className="text-xs text-fylle-gray">~30 minutes</span>
                </div>
                <h3 className="font-semibold mb-2 text-fylle-dark">Video Content Workflow</h3>
                <div className="space-y-2 mb-4">
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">1</div>
                    <span className="text-fylle-dark">Script Planning</span>
                    <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                    <i className="fas fa-brain text-blue-500"></i>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs">2</div>
                    <span className="text-fylle-dark">Content Research</span>
                    <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                    <i className="fas fa-search text-purple-500"></i>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">3</div>
                    <span className="text-fylle-dark">Script Writing</span>
                    <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                    <i className="fas fa-pen text-green-500"></i>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white text-xs">4</div>
                    <span className="text-fylle-dark">Video Production</span>
                    <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                    <i className="fas fa-video text-pink-500"></i>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-fylle-gray">Used by: Amilon, Novavista</div>
                  <Button className="bg-fylle-button text-fylle-button hover:bg-fylle-button-hover text-sm">
                    Run Workflow
                  </Button>
                </div>
              </div>

              <div 
                className="bg-fylle-card rounded-xl p-6 apple-shadow hover:apple-shadow-hover transition-apple cursor-pointer"
                onClick={() => {
                  setSelectedWorkflow(mockWorkflows[1]);
                  setIsWorkflowModalOpen(true);
                }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
                    <i className="fas fa-chart-bar text-indigo-600 text-lg"></i>
                  </div>
                  <span className="text-xs text-fylle-gray">~25 minutes</span>
                </div>
                <h3 className="font-semibold mb-2 text-fylle-dark">Market Analysis Workflow</h3>
                <div className="space-y-2 mb-4">
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">1</div>
                    <span className="text-fylle-dark">Context Setting</span>
                    <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                    <i className="fas fa-brain text-blue-500"></i>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs">2</div>
                    <span className="text-fylle-dark">Competitor Research</span>
                    <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                    <i className="fas fa-binoculars text-red-500"></i>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white text-xs">3</div>
                    <span className="text-fylle-dark">Sentiment Analysis</span>
                    <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                    <i className="fas fa-heart text-pink-500"></i>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center text-white text-xs">4</div>
                    <span className="text-fylle-dark">Report Generation</span>
                    <i className="fas fa-arrow-right text-fylle-gray text-xs"></i>
                    <i className="fas fa-chart-bar text-indigo-500"></i>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-fylle-gray">Enterprise workflow</div>
                  <Button className="bg-fylle-button text-fylle-button hover:bg-fylle-button-hover text-sm">
                    Run Workflow
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Tab */}
        {activeTab === 'content' && (
          <Content />
        )}
      </div>
      
      {/* Agent Detail Modal */}
      <AgentDetailModal
        agent={selectedAgent}
        isOpen={isAgentModalOpen}
        onClose={() => {
          setIsAgentModalOpen(false);
          setSelectedAgent(null);
        }}
      />

      {/* Workflow Detail Modal */}
      <WorkflowDetailModal
        workflow={selectedWorkflow}
        isOpen={isWorkflowModalOpen}
        onClose={() => {
          setIsWorkflowModalOpen(false);
          setSelectedWorkflow(null);
        }}
      />

      {/* Create Workflow Modal */}
      <CreateWorkflowModal
        isOpen={isCreateWorkflowModalOpen}
        onClose={() => setIsCreateWorkflowModalOpen(false)}
      />
    </div>
  );
}
