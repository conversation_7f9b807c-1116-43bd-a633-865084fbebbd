import { users, clients, knowledgeBases, integrations, aiAgents, workflows, type User, type InsertUser, type Client, type KnowledgeBase, type Integration, type AIAgent, type Workflow, type InsertClient, type InsertKnowledgeBase } from "@shared/schema";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getClients(): Promise<Client[]>;
  getClient(id: number): Promise<Client | undefined>;
  createClient(client: InsertClient): Promise<Client>;
  
  getKnowledgeBases(clientId: number): Promise<KnowledgeBase[]>;
  createKnowledgeBase(kb: InsertKnowledgeBase): Promise<KnowledgeBase>;
  
  getIntegrations(clientId: number): Promise<Integration[]>;
  getAIAgents(): Promise<AIAgent[]>;
  getWorkflows(): Promise<Workflow[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private clients: Map<number, Client>;
  private knowledgeBases: Map<number, KnowledgeBase>;
  private integrations: Map<number, Integration>;
  private aiAgents: Map<number, AIAgent>;
  private workflows: Map<number, Workflow>;
  private currentUserId: number;
  private currentClientId: number;
  private currentKbId: number;
  private currentIntegrationId: number;
  private currentAgentId: number;
  private currentWorkflowId: number;

  constructor() {
    this.users = new Map();
    this.clients = new Map();
    this.knowledgeBases = new Map();
    this.integrations = new Map();
    this.aiAgents = new Map();
    this.workflows = new Map();
    this.currentUserId = 1;
    this.currentClientId = 1;
    this.currentKbId = 1;
    this.currentIntegrationId = 1;
    this.currentAgentId = 1;
    this.currentWorkflowId = 1;
    
    this.initializeData();
  }

  private initializeData() {
    // Initialize clients
    const mockClients: Client[] = [
      { id: 1, name: "Siebert Financial", icon: "fas fa-university", color: "bg-blue-500", isActive: true, createdAt: new Date() },
      { id: 2, name: "Thron", icon: "fas fa-film", color: "bg-purple-500", isActive: false, createdAt: new Date() },
      { id: 3, name: "Altroconsumo", icon: "fas fa-newspaper", color: "bg-red-500", isActive: false, createdAt: new Date() },
      { id: 4, name: "Amilon", icon: "fas fa-video", color: "bg-green-500", isActive: false, createdAt: new Date() },
      { id: 5, name: "GapMed", icon: "fas fa-stethoscope", color: "bg-teal-500", isActive: false, createdAt: new Date() },
    ];
    
    mockClients.forEach(client => this.clients.set(client.id, client));
    this.currentClientId = 6;

    // Initialize knowledge bases with uploaded content
    const mockKnowledgeBases: KnowledgeBase[] = [
      {
        id: 1,
        clientId: 1,
        name: "Fylle AI Knowledge Base",
        category: "brand-dna",
        content: `# Fylle.ai - AI-Ready Knowledge Base

## Executive Summary
**Industry:** SaaS / Software | **Primary Goal:** Increase brand awareness | **Key Promise:** multiagent
**Target Channels:** Podcast, YouTube | **Last Updated:** December 2024

## Brand Foundation
### Core Identity
- **Market Position:** Fylle.ai positions itself as an innovative leader in the SaaS sector, specializing in multiagent solutions that democratize access to advanced artificial intelligence for businesses of all sizes
- **Tone of Voice:** Innovative, Technical-accessible, Results-oriented
- **Key Differentiators:** 
  - Multiagent specialization with proprietary technology
  - User-centric approach to complex AI implementation
  - Seamless integration capabilities
  - Enterprise-grade scalability

### Value Proposition
**Primary Promise:** Advanced multiagent solutions that transform business automation through collaborative and intelligent AI`,
        metadata: {
          originalFileName: "fylle_kb.md",
          fileSize: 5234,
          uploadedAt: new Date().toISOString()
        },
        createdAt: new Date()
      },
      {
        id: 2,
        clientId: 1,
        name: "TimeFlow Knowledge Base",
        category: "performance",
        content: `# Knowledge Base for Timeflow

## Brand Identity

### Positioning
TimeFlow si posiziona come la soluzione SaaS leader per l'ottimizzazione del procurement IT, combinando semplicità d'uso, supporto clienti eccellente e innovazione AI per trasformare la gestione delle risorse aziendali

### Key Differentiators
- Processo di onboarding 'absolutely amazing and painless'
- Customer service con courtesy, patience e communication skills
- Crescita revenue +50% YoY
- Backing finanziario solido per innovazione

## Key Messages

1. Implementazione semplice e veloce
2. Supporto clienti eccellente
3. Procurement ottimizzato con AI
4. Crescita aziendale comprovata

## Strategic Opportunities

- Capitalizzare sul sentiment clienti positivo per programmi referral
- Accelerare sviluppo features AI per mantenere vantaggio competitivo
- Espandere internazionalmente mantenendo standard di servizio
- Investire in content marketing basato su success stories
- Espansione internazionale con funding da €4M
- Sviluppo features AI-powered
- Capitalizzazione su crescita mercato SaaS
- Sfruttamento sentiment clienti positivo`,
        metadata: {
          originalFileName: "timeflow_kb.md",
          fileSize: 2876,
          uploadedAt: new Date().toISOString()
        },
        createdAt: new Date()
      }
    ];

    mockKnowledgeBases.forEach(kb => this.knowledgeBases.set(kb.id, kb));
    this.currentKbId = 3;

    // Initialize AI Agents
    const mockAgents: AIAgent[] = [
      { id: 1, name: "Context Setter", description: "Sets up context from Knowledge Base", icon: "fas fa-brain", status: "available", channels: ["slack", "email", "discord"], createdAt: new Date() },
      { id: 2, name: "Web Agent", description: "Research, scraping, and summarization", icon: "fas fa-search", status: "running", channels: ["slack", "email"], createdAt: new Date() },
      { id: 3, name: "Copywriter", description: "Content creation and revision", icon: "fas fa-pen", status: "available", channels: ["slack", "email", "discord"], createdAt: new Date() },
      { id: 4, name: "SEO Analyzer", description: "SEO optimization and analysis", icon: "fas fa-chart-line", status: "available", channels: ["slack", "email"], createdAt: new Date() },
      { id: 5, name: "Sentiment Analyzer", description: "Brand sentiment monitoring", icon: "fas fa-heart", status: "available", channels: ["slack", "email"], createdAt: new Date() },
      { id: 6, name: "Competitor Research", description: "Market analysis and insights", icon: "fas fa-binoculars", status: "available", channels: ["slack", "email"], createdAt: new Date() },
    ];
    
    mockAgents.forEach(agent => this.aiAgents.set(agent.id, agent));
    this.currentAgentId = 7;

    // Initialize Workflows
    const mockWorkflows: Workflow[] = [
      {
        id: 1,
        name: "Blog Post Workflow",
        description: "Complete blog post creation process",
        steps: [
          { step: 1, name: "Context Setting", agent: "Context Setter", icon: "fas fa-brain" },
          { step: 2, name: "Web Research", agent: "Web Agent", icon: "fas fa-search" },
          { step: 3, name: "Content Writing", agent: "Copywriter", icon: "fas fa-pen" }
        ],
        estimatedTime: 15,
        usedBy: ["Siebert", "GapMed", "Altroconsumo"],
        icon: "fas fa-blog",
        createdAt: new Date()
      },
      {
        id: 2,
        name: "Newsletter Workflow",
        description: "Newsletter creation and optimization",
        steps: [
          { step: 1, name: "Context Setting", agent: "Context Setter", icon: "fas fa-brain" },
          { step: 2, name: "Content Scraping", agent: "Web Agent", icon: "fas fa-search" },
          { step: 3, name: "Newsletter Writing", agent: "Copywriter", icon: "fas fa-pen" }
        ],
        estimatedTime: 20,
        usedBy: ["Siebert", "LUMSA"],
        icon: "fas fa-envelope-open",
        createdAt: new Date()
      }
    ];
    
    mockWorkflows.forEach(workflow => this.workflows.set(workflow.id, workflow));
    this.currentWorkflowId = 3;

    // Initialize integrations for Siebert Financial
    const mockIntegrations: Integration[] = [
      { id: 1, clientId: 1, name: "Website", type: "website", status: "connected", lastSync: new Date(), metadata: { url: "fylle.ai" } },
      { id: 2, clientId: 1, name: "Google Drive", type: "file_storage", status: "connected", lastSync: new Date(), metadata: { documentCount: 1254 } },
      { id: 3, clientId: 1, name: "LinkedIn", type: "social_media", status: "connected", lastSync: new Date(), metadata: {} },
      { id: 4, clientId: 1, name: "CRM", type: "crm", status: "disconnected", lastSync: null, metadata: {} },
    ];
    
    mockIntegrations.forEach(integration => this.integrations.set(integration.id, integration));
    this.currentIntegrationId = 5;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getClients(): Promise<Client[]> {
    return Array.from(this.clients.values());
  }

  async getClient(id: number): Promise<Client | undefined> {
    return this.clients.get(id);
  }

  async createClient(client: InsertClient): Promise<Client> {
    const id = this.currentClientId++;
    const newClient: Client = { ...client, id, createdAt: new Date(), isActive: client.isActive ?? false };
    this.clients.set(id, newClient);
    return newClient;
  }

  async getKnowledgeBases(clientId: number): Promise<KnowledgeBase[]> {
    return Array.from(this.knowledgeBases.values()).filter(kb => kb.clientId === clientId);
  }

  async createKnowledgeBase(kb: InsertKnowledgeBase): Promise<KnowledgeBase> {
    const id = this.currentKbId++;
    const newKb: KnowledgeBase = { 
      ...kb, 
      id, 
      createdAt: new Date(),
      clientId: kb.clientId ?? 1,
      metadata: kb.metadata ?? null
    };
    this.knowledgeBases.set(id, newKb);
    return newKb;
  }

  async getIntegrations(clientId: number): Promise<Integration[]> {
    return Array.from(this.integrations.values()).filter(integration => integration.clientId === clientId);
  }

  async getAIAgents(): Promise<AIAgent[]> {
    return Array.from(this.aiAgents.values());
  }

  async getWorkflows(): Promise<Workflow[]> {
    return Array.from(this.workflows.values());
  }
}

export const storage = new MemStorage();
