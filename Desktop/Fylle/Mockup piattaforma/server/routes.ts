import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import multer from "multer";
import { z } from "zod";

const upload = multer({ storage: multer.memoryStorage() });

export async function registerRoutes(app: Express): Promise<Server> {
  // Clients routes
  app.get("/api/clients", async (req, res) => {
    try {
      const clients = await storage.getClients();
      res.json(clients);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch clients" });
    }
  });

  app.get("/api/clients/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const client = await storage.getClient(id);
      if (!client) {
        return res.status(404).json({ error: "Client not found" });
      }
      res.json(client);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch client" });
    }
  });

  // Knowledge Base routes
  app.get("/api/clients/:clientId/knowledge-bases", async (req, res) => {
    try {
      const clientId = parseInt(req.params.clientId);
      const knowledgeBases = await storage.getKnowledgeBases(clientId);
      res.json(knowledgeBases);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch knowledge bases" });
    }
  });

  app.post("/api/clients/:clientId/knowledge-bases", upload.single("file"), async (req, res) => {
    try {
      const clientId = parseInt(req.params.clientId);
      const { name, category } = req.body;
      
      if (!name || !category) {
        return res.status(400).json({ error: "Name and category are required" });
      }

      let content = "";
      if (req.file) {
        content = req.file.buffer.toString('utf-8');
      }

      const knowledgeBase = await storage.createKnowledgeBase({
        clientId,
        name,
        category,
        content,
        metadata: { 
          originalFileName: req.file?.originalname,
          fileSize: req.file?.size,
          uploadedAt: new Date().toISOString()
        }
      });

      res.json(knowledgeBase);
    } catch (error) {
      console.error("Knowledge base upload error:", error);
      res.status(500).json({ error: "Failed to upload knowledge base" });
    }
  });

  // Integrations routes
  app.get("/api/clients/:clientId/integrations", async (req, res) => {
    try {
      const clientId = parseInt(req.params.clientId);
      const integrations = await storage.getIntegrations(clientId);
      res.json(integrations);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch integrations" });
    }
  });

  // AI Agents routes
  app.get("/api/ai-agents", async (req, res) => {
    try {
      const agents = await storage.getAIAgents();
      res.json(agents);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch AI agents" });
    }
  });

  // Workflows routes
  app.get("/api/workflows", async (req, res) => {
    try {
      const workflows = await storage.getWorkflows();
      res.json(workflows);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflows" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
