### **Obiettivo del documento**

Prepara una **Richiesta di Preventivo (RFP)** chiara e strutturata per lo sviluppo di un flusso AI-driven che:

1. acquisisca il DNA del brand,

2. crei/aggiorni una knowledge-base semantica,

3. offra card interattive per la validazione,

4. impieghi agenti multipli sincronizzati in tempo reale,

5. consenta la configurazione di workflow e fonti esterne.

---

## **1\. Scope funzionale (User Stories sintetiche)**

| STEP | Funzionalità core | Acceptance criteria |
| ----- | ----- | ----- |
| **1\. Onboarding Guidato** | UI multistep (React) per brand profile, tone, audience, goal; validazione in tempo reale; salvataggio draft | ● Campi obbligatori validati ● Draft recuperabile |
| **2\. Knowledge-Base Engine** | Ingest di PDF/URL/CSV → parser → embedding semantico (OpenAI o HuggingFace) → indicizzazione Vector-DB | ● Upload & crawl \<1 min/MB ● Query ≤ 800 ms |
| **3\. Knowledge Cards** | Dashboard Next.js con card (status: Nuova, Confermata, Da-rigenerare) \+ CTA: **Sì**, **Approfondisci**, **Check again \+ feedback** | ● Azioni aggiornano DB e triggerano job async ● Versioning delle card |
| **4\. Sistema Multi-Agenti** | Crew/AutoGen agents con prompt auto-aggiornato dalla KB; fallback provider | ● Errore provider ⇢ switch automatico  |
| **5\. Workflow Configuration** | Pannello per: web search allow-list/black-list, formati output, temperature LLM | ● Configurazione salvata per progetto ● Audit log modifiche |

---

## **2\. Architettura di riferimento**

| Layer | Tecnologie suggerite | Note / Razionali |
| ----- | ----- | ----- |
| **Frontend** | Next.js 14 (App Router) \+ Tailwind \+ tRPC | Evita doppio server; SSR/ISR per performance |
| **Backend API** | FastAPI (Python 3.12) | Già usata nei POC; unifica API e websocket progress |
| **Worker** | Celery/Redis \+ asyncio | Job lunghi (scraping, RAG) |
| **DB** | PostgreSQL (Django-style schema) \+ Chroma/Pinecone | Normalizzare dati JSON e indicizzare vettori |
| **Auth** | JWT/OAuth2 (Auth.js) | Mancante nei POC attuali |
| **DevOps** | Docker-Compose \+ GitHub Actions → staging (Render/Fly) | Single container per API+worker; CDN per frontend |

**Migliorie chiave rispetto ai POC**

* **Unico server UI** (niente Streamlit) per ridurre overhead e porte multiple

* **Fallback SERP/mock** per sviluppo offline e riduzione lock-in API esterne

* **Prompt templates e context-trimming** per taglio costi token

---

## **3\. Requisiti non funzionali**

| Categoria | Requisito |
| ----- | ----- |
| **Performance** | P95 \< 1 s per richieste lettura KB; ingest docs 10 MB in \< 5 min |
| **Scalabilità** | Stateless API; horizontal scaling con autoscaler |
| **Sicurezza** | Secret management (dotenv → cloud vault); rate-limiting; RBAC |
| **Qualità** | Test coverage ≥ 80 %; lint/format CI; logging strutturato JSON |
| **Monitoring** | Prometheus \+ Grafana dashboard; alert su error rate \> 2 % |

---

## **4\. Deliverables richiesti**

1. **Codice sorgente** (monorepo Git) con README build & run.

2. **Documentazione tecnica** (Arch-diagram, ER-diagram, API-OpenAPI, handbook DevOps).

3. **Ambiente staging** deployato e link accesso.

4. **Test suite** unit \+ integration \+ load (Locust/JMeter).

5. **Manuale utente** breve (onboarding \+ gestione workflow).

---

## **5\. Roadmap e milestone (indicative)**

| Mese | Output principale |
| ----- | ----- |
| **1** | UX wireframe \+ spec dettagliate; setup CI/CD |
| **2** | Onboarding UI \+ API ingestion; base vector-DB |
| **3** | Knowledge Cards \+ job async; prime query RAG |
| **4** | Multi-agent orchestration \+ fallback; workflow panel |
| **5** | Hardening sicurezza \+ monitoring; beta testing |
| **6** | Staging freeze, hand-off documentazione |

---

## **6\. Rischi & mitigazioni**

| Rischio | Impatto | Mitigazione |
| ----- | ----- | ----- |
| Dipendenza API SERP | Blocco onboarding | Mock \+ caching locale |
| Costi LLM imprevedibili | Budget overrun | Prompt/embedding trimming \+ cache |
| Complessità multi-agent | Ritardi | Proof-of-concept ridotto in sprint 2 |
| Sicurezza chiavi API | Leak dati | Secret vault \+ least-privilege IAM |

---

---

